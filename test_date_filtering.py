#!/usr/bin/env python3
"""
Test script to verify the date range filtering functionality
for the CIO RSP Executive Digital Usage page improvements.
"""

import requests
import json
import sys

def test_api_endpoint(url, params=None, description=""):
    """Test an API endpoint and return the results"""
    try:
        print(f"\n🧪 Testing: {description}")
        print(f"📡 URL: {url}")
        if params:
            print(f"📋 Parameters: {params}")
        
        response = requests.get(url, params=params, timeout=10)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data:
                records = data['data']
                print(f"✅ Success: {len(records)} records returned")
                
                if records:
                    print("📝 Sample records:")
                    for i, record in enumerate(records[:3]):
                        period = record.get('Period', 'N/A')
                        total = record.get('TotalTxns', 'N/A')
                        print(f"   {i+1}. {period}: {total} transactions")
                    
                    if len(records) > 3:
                        print(f"   ... and {len(records) - 3} more records")
                
                return records
            else:
                print(f"⚠️  Warning: No 'data' field in response")
                return []
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection Error: Could not connect to {url}")
        print("💡 Make sure the backend server is running on localhost:5001")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Testing CIO RSP Executive Digital Usage Date Range Filtering")
    print("=" * 60)
    
    base_url = "http://localhost:5001/CioRspExecutive/DigitalUsageHistory"
    
    # Test 1: Get all data (no date range)
    all_data = test_api_endpoint(
        base_url,
        description="All digital usage data (no filtering)"
    )
    
    if all_data is None:
        print("\n❌ Cannot proceed with tests - backend not accessible")
        sys.exit(1)
    
    # Test 2: Get filtered data (with date range)
    filtered_data = test_api_endpoint(
        base_url,
        params={'startPeriod': 'Jan-25', 'endPeriod': 'Mar-25'},
        description="Filtered data (Jan-25 to Mar-25)"
    )
    
    # Test 3: Compare results
    print(f"\n📊 Comparison Results:")
    print(f"   All data: {len(all_data) if all_data else 0} records")
    print(f"   Filtered data: {len(filtered_data) if filtered_data else 0} records")
    
    if all_data and filtered_data:
        if len(filtered_data) < len(all_data):
            print("✅ Date range filtering is working correctly!")
            print(f"   Filtered out {len(all_data) - len(filtered_data)} records")
        elif len(filtered_data) == len(all_data):
            print("⚠️  Date range filtering may not be working - same number of records")
        else:
            print("❌ Unexpected result - filtered data has more records than all data")
    
    # Test 4: Verify filtered periods are within range
    if filtered_data:
        print(f"\n🔍 Verifying filtered periods are within Jan-25 to Mar-25:")
        periods_in_range = []
        periods_out_of_range = []
        
        for record in filtered_data:
            period = record.get('Period', '')
            if period:
                # Simple check for the periods (assuming format like "Jan-25", "Feb-25", etc.)
                if any(month in period for month in ['Jan-25', 'Feb-25', 'Mar-25']):
                    periods_in_range.append(period)
                else:
                    periods_out_of_range.append(period)
        
        print(f"   ✅ Periods in range: {len(periods_in_range)}")
        if periods_in_range:
            print(f"      {', '.join(sorted(set(periods_in_range)))}")
        
        if periods_out_of_range:
            print(f"   ⚠️  Periods out of range: {len(periods_out_of_range)}")
            print(f"      {', '.join(sorted(set(periods_out_of_range)))}")
        else:
            print(f"   ✅ All filtered periods are within the specified range!")
    
    print(f"\n🎉 Date range filtering test completed!")

if __name__ == "__main__":
    main()
