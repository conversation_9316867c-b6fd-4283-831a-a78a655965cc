#!/bin/bash

# RSPi Application Startup Script
# This script starts both the backend and frontend servers for the RSPi application
# Defaults to development mode with option to specify environment

set -e  # Exit on any error

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Parse command line arguments
ENVIRONMENT="dev"
HELP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --env|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --prod|--production)
            ENVIRONMENT="prod"
            shift
            ;;
        --dev|--development)
            ENVIRONMENT="dev"
            shift
            ;;
        --help|-h)
            HELP=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            HELP=true
            shift
            ;;
    esac
done

# Show help if requested or invalid arguments
if [ "$HELP" = true ]; then
    echo -e "${PURPLE}RSPi Application Startup Script${NC}"
    echo ""
    echo -e "${BLUE}Usage:${NC} $0 [OPTIONS]"
    echo ""
    echo -e "${BLUE}Options:${NC}"
    echo "  --env, --environment ENV    Set environment (dev|prod) [default: dev]"
    echo "  --dev, --development        Use development mode (auth disabled)"
    echo "  --prod, --production        Use production mode (auth enabled)"
    echo "  --help, -h                  Show this help message"
    echo ""
    echo -e "${BLUE}Examples:${NC}"
    echo "  $0                          # Start in development mode (default)"
    echo "  $0 --dev                    # Start in development mode"
    echo "  $0 --prod                   # Start in production mode"
    echo "  $0 --env prod               # Start in production mode"
    echo ""
    echo -e "${BLUE}Environment Modes:${NC}"
    echo -e "  ${GREEN}dev:${NC}  Authentication disabled, no login required"
    echo -e "  ${RED}prod:${NC} Authentication enabled, LDAP login required"
    echo ""
    echo -e "${BLUE}Authentication System:${NC}"
    echo "  • Development: Complete bypass, direct access to all features"
    echo "  • Production:  Full LDAP authentication against NBNCO Active Directory"
    echo "  • JWT tokens:  Secure session management with configurable expiration"
    echo "  • User context: Automatic injection into all business operations"
    exit 0
fi

# Validate environment
if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "prod" ]; then
    echo -e "${RED}Error: Invalid environment '$ENVIRONMENT'. Must be 'dev' or 'prod'.${NC}"
    echo "Use --help for usage information."
    exit 1
fi

# Function to display error and exit
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    exit 1
}

# Check if required startup scripts exist
if [ "$ENVIRONMENT" = "prod" ]; then
    if [ ! -f "./start_prod.sh" ]; then
        error_exit "Production startup script (start_prod.sh) not found"
    fi
    if [ ! -x "./start_prod.sh" ]; then
        error_exit "Production startup script (start_prod.sh) is not executable. Run: chmod +x start_prod.sh"
    fi
else
    if [ ! -f "./start_dev.sh" ]; then
        error_exit "Development startup script (start_dev.sh) not found"
    fi
    if [ ! -x "./start_dev.sh" ]; then
        error_exit "Development startup script (start_dev.sh) is not executable. Run: chmod +x start_dev.sh"
    fi
fi

echo -e "${PURPLE}========================================${NC}"
echo -e "${PURPLE}  RSPi Application Startup${NC}"
echo -e "${PURPLE}========================================${NC}"

if [ "$ENVIRONMENT" = "prod" ]; then
    echo -e "${RED}🔐 Mode: Production (Authentication ENABLED)${NC}"
    echo -e "${RED}👤 Login: LDAP Authentication Required${NC}"
    echo -e "${YELLOW}⚠️  Delegating to production startup script...${NC}"
    echo ""
    exec ./start_prod.sh
else
    echo -e "${GREEN}🔧 Mode: Development (Authentication DISABLED)${NC}"
    echo -e "${GREEN}👤 Login: No authentication required${NC}"
    echo -e "${YELLOW}🔄 Delegating to development startup script...${NC}"
    echo ""
    exec ./start_dev.sh
fi






