#!/usr/bin/env python3
"""
Test script for Ollama-based AI insights generation
Run this script to verify that Ollama integration is working correctly
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from insight_generator import InsightGenerator

def test_ollama_availability():
    """Test if Ollama service is available"""
    print("🔍 Testing Ollama availability...")
    
    generator = InsightGenerator()
    is_available = generator.check_ollama_availability()
    
    if is_available:
        print("✅ Ollama service is available at http://localhost:11434")
        return True
    else:
        print("❌ Ollama service is not available")
        print("   Please ensure Ollama is running with: ollama serve")
        return False

def test_insight_generation():
    """Test AI insight generation with sample data"""
    print("\n🧠 Testing AI insight generation...")
    
    # Sample digital usage data
    sample_data = [
        {
            "Period": "Jul-24",
            "TotalTxns": 45678,
            "TotalAPITxns": 23456,
            "TotalPortalTxns": 22222
        },
        {
            "Period": "Aug-24",
            "TotalTxns": 51234,
            "TotalAPITxns": 27845,
            "TotalPortalTxns": 23389
        },
        {
            "Period": "Sep-24",
            "TotalTxns": 48901,
            "TotalAPITxns": 26543,
            "TotalPortalTxns": 22358
        }
    ]
    
    # Sample comparison data
    comparison_data = {
        "totalPctChange": 12.3,
        "apiPctChange": 18.7,
        "portalPctChange": 5.2
    }
    
    generator = InsightGenerator()
    insights = generator.generate_insights(sample_data, comparison_data)
    
    print("📊 Generated Insights:")
    print("-" * 50)
    print(insights)
    print("-" * 50)
    
    return len(insights) > 100  # Basic check that we got substantial insights

def test_fallback_analysis():
    """Test fallback statistical analysis"""
    print("\n📈 Testing fallback statistical analysis...")
    
    # Sample data
    sample_data = [
        {
            "Period": "Jul-24",
            "TotalTxns": 45678,
            "TotalAPITxns": 23456,
            "TotalPortalTxns": 22222
        },
        {
            "Period": "Aug-24",
            "TotalTxns": 51234,
            "TotalAPITxns": 27845,
            "TotalPortalTxns": 23389
        }
    ]
    
    generator = InsightGenerator()
    insights = generator._fallback_statistical_analysis(sample_data)
    
    print("📊 Fallback Insights:")
    print("-" * 50)
    print(insights)
    print("-" * 50)
    
    return "Volume Trends" in insights and "Recommendations" in insights

def main():
    """Main test function"""
    print("🚀 Ollama AI Insights Test Suite")
    print("=" * 50)
    
    # Test 1: Ollama availability
    ollama_available = test_ollama_availability()
    
    # Test 2: AI insight generation (if Ollama is available)
    if ollama_available:
        ai_success = test_insight_generation()
        if ai_success:
            print("✅ AI insight generation test passed")
        else:
            print("❌ AI insight generation test failed")
    else:
        print("⏭️  Skipping AI insight generation test (Ollama not available)")
        ai_success = False
    
    # Test 3: Fallback analysis
    fallback_success = test_fallback_analysis()
    if fallback_success:
        print("✅ Fallback statistical analysis test passed")
    else:
        print("❌ Fallback statistical analysis test failed")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Ollama Available: {'✅' if ollama_available else '❌'}")
    print(f"   AI Insights: {'✅' if ai_success else '❌'}")
    print(f"   Fallback Analysis: {'✅' if fallback_success else '❌'}")
    
    if ollama_available and ai_success and fallback_success:
        print("\n🎉 All tests passed! AI insights are ready to use.")
        return 0
    elif fallback_success:
        print("\n⚠️  Fallback analysis works, but Ollama is not available.")
        print("   Install and start Ollama for full AI capabilities.")
        return 1
    else:
        print("\n❌ Tests failed. Please check your configuration.")
        return 2

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
