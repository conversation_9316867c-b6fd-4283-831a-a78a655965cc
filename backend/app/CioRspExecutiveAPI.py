import logging

from CioRspExecutiveBusSvc import CioRspExecutiveBusSvc
from flask import Blueprint, jsonify, request

logger = logging.getLogger(__name__)

# Create Blueprint
cio_rsp_executive_bp = Blueprint("cio_rsp_executive", __name__)


@cio_rsp_executive_bp.route("/CioRspExecutive/DigitalUsageHistory", methods=["GET"])
def get_digital_usage_history():
    """Get digital usage history data for trends analysis"""
    try:
        # Get optional date range parameters
        start_period = request.args.get("startPeriod")
        end_period = request.args.get("endPeriod")

        logger.info(
            f"Getting digital usage history with date range: {start_period} to {end_period}"
        )

        data = CioRspExecutiveBusSvc.GetDigitalUsageHistory(start_period, end_period)

        if data is None:
            logger.error("Failed to retrieve digital usage history")
            return jsonify({"error": "Failed to retrieve digital usage history"}), 500

        logger.info(f"Successfully retrieved {len(data)} digital usage history records")
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_digital_usage_history: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route("/CioRspExecutive/DigitalUsagePeriods", methods=["GET"])
def get_digital_usage_periods():
    """Get available periods for digital usage data"""
    try:
        logger.info("Getting digital usage periods")

        data = CioRspExecutiveBusSvc.GetDigitalUsagePeriods()

        if data is None:
            logger.error("Failed to retrieve digital usage periods")
            return jsonify({"error": "Failed to retrieve digital usage periods"}), 500

        logger.info(f"Successfully retrieved {len(data)} digital usage periods")
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_digital_usage_periods: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route(
    "/CioRspExecutive/DigitalServiceUsage/<period>", methods=["GET"]
)
def get_digital_service_usage(period):
    """Get digital service usage data for a specific period"""
    try:
        logger.info(f"Getting digital service usage for period: {period}")

        data = CioRspExecutiveBusSvc.GetDigitalServiceUsage(period)

        if data is None:
            logger.error(
                f"Failed to retrieve digital service usage for period {period}"
            )
            return (
                jsonify(
                    {
                        "error": f"Failed to retrieve digital service usage for period {period}"
                    }
                ),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} digital service usage records for period {period}"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(
            f"Error in get_digital_service_usage for period {period}: {str(e)}"
        )
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route("/CioRspExecutive/RSPAPIAdoption/<period>", methods=["GET"])
def get_rsp_api_adoption(period):
    """Get RSP API adoption and utilization data for a specific period"""
    try:
        logger.info(f"Getting RSP API adoption for period: {period}")

        data = CioRspExecutiveBusSvc.GetRSPAPIAdoption(period)

        if data is None:
            logger.error(f"Failed to retrieve RSP API adoption for period {period}")
            return (
                jsonify(
                    {
                        "error": f"Failed to retrieve RSP API adoption for period {period}"
                    }
                ),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP API adoption records for period {period}"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_api_adoption for period {period}: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route(
    "/CioRspExecutive/RSPDigitalUsage/<period>", methods=["GET"]
)
def get_rsp_digital_usage(period):
    """Get RSP digital usage data for a specific period"""
    try:
        logger.info(f"Getting RSP digital usage for period: {period}")

        data = CioRspExecutiveBusSvc.GetRSPDigitalUsage(period)

        if data is None:
            logger.error(f"Failed to retrieve RSP digital usage for period {period}")
            return (
                jsonify(
                    {
                        "error": f"Failed to retrieve RSP digital usage for period {period}"
                    }
                ),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP digital usage records for period {period}"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_digital_usage for period {period}: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route(
    "/CioRspExecutive/RSPAPIPercentage/<period>", methods=["GET"]
)
def get_rsp_api_percentage(period):
    """Get RSP API percentage data for a specific period"""
    try:
        logger.info(f"Getting RSP API percentage for period: {period}")

        data = CioRspExecutiveBusSvc.GetRSPAPIPercentage(period)

        if data is None:
            logger.error(f"Failed to retrieve RSP API percentage for period {period}")
            return (
                jsonify(
                    {
                        "error": f"Failed to retrieve RSP API percentage for period {period}"
                    }
                ),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP API percentage records for period {period}"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_api_percentage for period {period}: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@cio_rsp_executive_bp.route("/CioRspExecutive/GenerateInsights", methods=["POST"])
def generate_insights():
    """Generate AI insights for digital usage data"""
    try:
        logger.info("Generating AI insights")

        # Get the request data
        request_data = request.get_json()
        if not request_data:
            return jsonify({"error": "No data provided"}), 400

        digital_usage_data = request_data.get("digitalUsageData", [])
        comparison_data = request_data.get("comparisonData")

        if not digital_usage_data:
            return jsonify({"error": "No digital usage data provided"}), 400

        # Generate insights using the business service
        insights = CioRspExecutiveBusSvc.GenerateAIInsights(
            digital_usage_data, comparison_data
        )

        if insights is None:
            logger.error("Failed to generate AI insights")
            return jsonify({"error": "Failed to generate AI insights"}), 500

        logger.info("Successfully generated AI insights")
        return jsonify({"data": {"insights": insights}}), 200

    except Exception as e:
        logger.error(f"Error in generate_insights: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500
