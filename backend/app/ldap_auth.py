"""
LDAP Authentication Module for RSPi Application

This module provides LDAP authentication functionality using the NBNCO Active Directory.
It includes the ad_auth function for validating user credentials against the corporate LDAP server.
"""

import logging
import ssl
from typing import Dict, Optional

from ldap3 import ALL, NTLM, Connection, Server, Tls

logger = logging.getLogger(__name__)

# LDAP Configuration Constants
DC_HOST = "SVEDC2000004PR.nbnco.local"
BASE_DN = "DC=nbnco,DC=local"


def ad_auth(username: str, password: str) -> Optional[Dict[str, any]]:
    """
    Authenticate user against NBNCO Active Directory via LDAP.
    
    Args:
        username (str): Username for authentication. Can be in format 'username' or 'NB<PERSON><PERSON>\\username'
        password (str): User's password
        
    Returns:
        Dict containing user attributes if authentication succeeds:
        {
            "displayName": str,
            "mail": str, 
            "groups": List[str]
        }
        Returns None if authentication fails.
        
    Example:
        user_info = ad_auth("edwardbowman", "password123")
        if user_info:
            print(f"Welcome {user_info['displayName']}")
        else:
            print("Authentication failed")
    """
    try:
        logger.info(f"Attempting LDAP authentication for user: {username}")
        
        # Accept either edwardbowman or NBNCO\\edwardbowman format
        if "\\" not in username and "@" not in username:
            bind_user = f"NBNCO\\{username}"
        else:
            bind_user = username

        # Configure TLS for secure connection
        tls = Tls(validate=ssl.CERT_REQUIRED)
        srv = Server(DC_HOST, port=636, use_ssl=True, get_info=ALL, tls=tls)

        # Attempt LDAP connection and authentication
        with Connection(srv, user=bind_user, password=password,
                        authentication=NTLM, auto_bind=True) as connection:
            
            # Extract username for search (remove domain prefix if present)
            search_username = username.split('\\')[-1]
            
            # Search for user attributes in Active Directory
            connection.search(
                BASE_DN,
                f"(sAMAccountName={search_username})",
                attributes=["displayName", "mail", "memberOf"]
            )
            
            if not connection.entries:
                logger.warning(f"User {username} not found in Active Directory")
                return None
                
            entry = connection.entries[0]
            
            # Extract user information
            user_info = {
                "displayName": entry.displayName.value if entry.displayName.value else username,
                "mail": entry.mail.value if entry.mail.value else f"{username}@nbnco.com.au",
                "groups": entry.memberOf.values if entry.memberOf.values else []
            }
            
            logger.info(f"LDAP authentication successful for user: {username}")
            return user_info
            
    except Exception as e:
        logger.error(f"LDAP authentication failed for user {username}: {str(e)}")
        return None


def validate_ldap_connection() -> bool:
    """
    Test LDAP server connectivity without authentication.
    
    Returns:
        bool: True if LDAP server is reachable, False otherwise
    """
    try:
        tls = Tls(validate=ssl.CERT_REQUIRED)
        srv = Server(DC_HOST, port=636, use_ssl=True, get_info=ALL, tls=tls)
        
        # Test connection without binding
        with Connection(srv) as connection:
            logger.info("LDAP server connectivity test successful")
            return True
            
    except Exception as e:
        logger.error(f"LDAP server connectivity test failed: {str(e)}")
        return False
