import os
import pathlib
from typing import Any, Dict, Optional

import yaml


def load_yaml_file(file: str) -> Optional[Dict[str, Any]]:
    # Try the file as-is first
    if pathlib.Path(file).is_file():
        with open(file) as f:
            return yaml.safe_load(f)

    # Try relative to the current script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    relative_path = os.path.join(script_dir, file)
    if pathlib.Path(relative_path).is_file():
        with open(relative_path) as f:
            return yaml.safe_load(f)

    # Try relative to the parent directory (backend)
    parent_path = os.path.join(script_dir, "..", file)
    if pathlib.Path(parent_path).is_file():
        with open(parent_path) as f:
            return yaml.safe_load(f)

    raise FileNotFoundError(
        f"unable to find file: {file} (tried: {file}, {relative_path}, {parent_path})"
    )
