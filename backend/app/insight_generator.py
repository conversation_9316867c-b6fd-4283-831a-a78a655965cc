import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

import requests

logger = logging.getLogger(__name__)


class InsightGenerator:
    """AI-powered insight generator using Ollama for digital usage analysis"""

    def __init__(
        self, ollama_url: str = "http://localhost:11434", model: str = "llama3.2"
    ):
        """
        Initialize the InsightGenerator with Ollama connection

        Args:
            ollama_url: URL of the Ollama service (default: http://localhost:11434)
            model: Ollama model to use (default: llama3.2)
        """
        self.ollama_url = ollama_url
        self.model = model

    def generate_insights(
        self, digital_usage_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> str:
        """
        Generate AI insights using Ollama LLM

        Args:
            digital_usage_data: List of digital usage records
            comparison_data: Optional comparison analysis data

        Returns:
            AI-generated insights as formatted text
        """
        try:
            logger.info("Starting Ollama-based AI insights generation")

            if not digital_usage_data or len(digital_usage_data) == 0:
                logger.warning("No digital usage data provided for insights generation")
                return "No data available for analysis."

            # Prepare data summary for AI analysis
            data_summary = self._prepare_data_summary(
                digital_usage_data, comparison_data
            )

            # Create AI prompt
            prompt = self._create_analysis_prompt(data_summary)

            # Call Ollama API
            insights = self._call_ollama_api(prompt)

            if insights:
                logger.info("Successfully generated AI insights using Ollama")
                return insights
            else:
                logger.warning(
                    "Ollama API returned empty insights, falling back to statistical analysis"
                )
                return self._fallback_statistical_analysis(
                    digital_usage_data, comparison_data
                )

        except Exception as e:
            logger.error(f"Error generating AI insights with Ollama: {str(e)}")
            logger.info("Falling back to statistical analysis")
            return self._fallback_statistical_analysis(
                digital_usage_data, comparison_data
            )

    def _prepare_data_summary(
        self, digital_usage_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> Dict:
        """Prepare a concise data summary for AI analysis"""

        # Sort data by period
        sorted_data = sorted(digital_usage_data, key=lambda x: x.get("Period", ""))

        summary = {
            "total_periods": len(sorted_data),
            "date_range": f"{sorted_data[0].get('Period', 'Unknown')} to {sorted_data[-1].get('Period', 'Unknown')}",
            "latest_period": sorted_data[-1] if sorted_data else {},
            "previous_period": sorted_data[-2] if len(sorted_data) > 1 else {},
            "trends": {},
            "comparison": comparison_data,
        }

        # Calculate basic trends
        if len(sorted_data) >= 2:
            latest = sorted_data[-1]
            previous = sorted_data[-2]

            for metric in ["TotalTxns", "TotalAPITxns", "TotalPortalTxns"]:
                latest_val = int(latest.get(metric, 0))
                previous_val = int(previous.get(metric, 0))

                if previous_val > 0:
                    growth = ((latest_val - previous_val) / previous_val) * 100
                    summary["trends"][metric] = {
                        "latest": latest_val,
                        "previous": previous_val,
                        "growth_rate": round(growth, 1),
                    }

        # Calculate API percentage
        if summary["latest_period"]:
            total = int(summary["latest_period"].get("TotalTxns", 0))
            api = int(summary["latest_period"].get("TotalAPITxns", 0))
            if total > 0:
                summary["api_percentage"] = round((api / total) * 100, 1)

        return summary

    def _create_analysis_prompt(self, data_summary: Dict) -> str:
        """Create a detailed prompt for AI analysis"""

        prompt = f"""
You are a senior data analyst specializing in digital transformation and API adoption trends. 
Analyze the following digital usage data and provide professional insights.

DATA SUMMARY:
- Analysis Period: {data_summary.get('date_range', 'Unknown')}
- Total Periods Analyzed: {data_summary.get('total_periods', 0)}
- Current API Adoption Rate: {data_summary.get('api_percentage', 0)}%

RECENT TRENDS:
"""

        # Add trend information
        for metric, trend_data in data_summary.get("trends", {}).items():
            metric_name = {
                "TotalTxns": "Total Transactions",
                "TotalAPITxns": "API Transactions",
                "TotalPortalTxns": "Portal Transactions",
            }.get(metric, metric)

            prompt += f"""
- {metric_name}: {trend_data['previous']:,} → {trend_data['latest']:,} ({trend_data['growth_rate']:+.1f}%)"""

        if data_summary.get("comparison"):
            prompt += f"""

COMPARATIVE ANALYSIS:
- Total Volume Change: {data_summary['comparison'].get('totalPctChange', 0):+.1f}%
- API Volume Change: {data_summary['comparison'].get('apiPctChange', 0):+.1f}%
- Portal Volume Change: {data_summary['comparison'].get('portalPctChange', 0):+.1f}%
"""

        prompt += """

ANALYSIS REQUIREMENTS:
Please provide a comprehensive analysis with the following sections:

1. **📈 Volume Trends**: Analyze transaction volume changes and their significance
2. **🔄 Channel Mix Analysis**: Evaluate API vs Portal usage patterns and shifts
3. **📊 Strategic Insights**: Identify key patterns and their business implications
4. **💡 Recommendations**: Provide 3-5 specific, actionable recommendations

FORMATTING GUIDELINES:
- Use emojis for section headers
- Use bullet points (•) for key findings
- Keep insights concise but meaningful
- Focus on business impact and actionable insights
- Highlight significant changes (>5%) and trends
- Provide specific numbers and percentages where relevant

Generate professional, data-driven insights that would be valuable for executive decision-making.
"""

        return prompt

    def _call_ollama_api(self, prompt: str) -> Optional[str]:
        """Call Ollama API to generate insights"""
        try:
            url = f"{self.ollama_url}/api/generate"

            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {"temperature": 0.7, "top_p": 0.9, "max_tokens": 1000},
            }

            logger.info(f"Calling Ollama API at {url} with model {self.model}")

            response = requests.post(url, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                insights = result.get("response", "").strip()

                if insights:
                    logger.info(
                        f"Ollama API returned {len(insights)} characters of insights"
                    )
                    return insights
                else:
                    logger.warning("Ollama API returned empty response")
                    return None
            else:
                logger.error(
                    f"Ollama API error: {response.status_code} - {response.text}"
                )
                return None

        except requests.exceptions.RequestException as e:
            logger.error(f"Network error calling Ollama API: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calling Ollama API: {str(e)}")
            return None

    def _fallback_statistical_analysis(
        self, digital_usage_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> str:
        """Fallback to statistical analysis when Ollama is unavailable"""

        logger.info("Using fallback statistical analysis")

        if not digital_usage_data or len(digital_usage_data) == 0:
            return "No data available for analysis."

        insights = []

        # Sort data by period
        sorted_data = sorted(digital_usage_data, key=lambda x: x.get("Period", ""))

        # Basic trend analysis
        if len(sorted_data) >= 2:
            latest = sorted_data[-1]
            previous = sorted_data[-2]

            insights.append(
                f"📈 **Volume Trends ({previous.get('Period', 'Unknown')} to {latest.get('Period', 'Unknown')})**"
            )

            for metric in ["TotalTxns", "TotalAPITxns", "TotalPortalTxns"]:
                latest_val = int(latest.get(metric, 0))
                previous_val = int(previous.get(metric, 0))

                if previous_val > 0:
                    growth = ((latest_val - previous_val) / previous_val) * 100
                    if abs(growth) > 5:
                        direction = "increased" if growth > 0 else "decreased"
                        metric_name = {
                            "TotalTxns": "Total transactions",
                            "TotalAPITxns": "API transactions",
                            "TotalPortalTxns": "Portal transactions",
                        }.get(metric, metric)
                        insights.append(
                            f"• {metric_name} {direction} by {abs(growth):.1f}% ({previous_val:,} → {latest_val:,})"
                        )

            # Channel mix analysis
            total_latest = int(latest.get("TotalTxns", 0))
            api_latest = int(latest.get("TotalAPITxns", 0))

            if total_latest > 0:
                api_pct = (api_latest / total_latest) * 100
                insights.append(f"\n🔄 **Channel Mix Analysis**")
                insights.append(
                    f"• Current API usage: {api_pct:.1f}% of total transactions"
                )

                if api_pct < 50:
                    insights.append(
                        f"• Consider promoting API adoption - currently below 50%"
                    )
                elif api_pct > 80:
                    insights.append(
                        f"• Excellent API adoption rate - maintain current strategies"
                    )

        # Add recommendations
        insights.append(f"\n💡 **Key Recommendations**")
        insights.append("• Monitor transaction volume trends for capacity planning")
        insights.append("• Continue promoting API adoption for better efficiency")
        insights.append(
            "• Analyze user behavior patterns for optimization opportunities"
        )

        return "\n".join(insights)

    def generate_platforms_insights(
        self, platforms_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> str:
        """
        Generate AI insights for RSP platforms weekly data using Ollama LLM

        Args:
            platforms_data: List of platform performance records
            comparison_data: Optional comparison analysis data

        Returns:
            AI-generated insights as formatted text
        """
        try:
            logger.info(
                "Starting Ollama-based AI insights generation for platforms data"
            )

            if not platforms_data or len(platforms_data) == 0:
                logger.warning("No platforms data provided for insights generation")
                return "No platform data available for analysis."

            # Prepare data summary for AI analysis
            data_summary = self._prepare_platforms_data_summary(
                platforms_data, comparison_data
            )

            # Create AI prompt
            prompt = self._create_platforms_analysis_prompt(data_summary)

            # Call Ollama API
            insights = self._call_ollama_api(prompt)

            if insights:
                logger.info(
                    "Successfully generated AI insights for platforms using Ollama"
                )
                return insights
            else:
                logger.warning(
                    "Ollama API returned empty insights, falling back to statistical analysis"
                )
                return self._fallback_platforms_statistical_analysis(
                    platforms_data, comparison_data
                )

        except Exception as e:
            logger.error(
                f"Error generating AI insights for platforms with Ollama: {str(e)}"
            )
            logger.info("Falling back to statistical analysis")
            return self._fallback_platforms_statistical_analysis(
                platforms_data, comparison_data
            )

    def _prepare_platforms_data_summary(
        self, platforms_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> Dict:
        """Prepare a concise data summary for platforms AI analysis"""

        # Group data by week
        weeks_data = {}
        for item in platforms_data:
            week = item.get("WeekEnding", "Unknown")
            if week not in weeks_data:
                weeks_data[week] = []
            weeks_data[week].append(item)

        # Sort weeks
        sorted_weeks = sorted(weeks_data.keys())

        summary = {
            "total_weeks": len(sorted_weeks),
            "date_range": (
                f"{sorted_weeks[0]} to {sorted_weeks[-1]}"
                if sorted_weeks
                else "Unknown"
            ),
            "total_platforms": len(platforms_data),
            "platform_types": list(
                set(
                    item.get("PlatformServiceType", "Unknown")
                    for item in platforms_data
                )
            ),
            "performance_summary": {},
            "incidents_summary": {},
            "comparison": comparison_data,
        }

        # Calculate performance metrics
        if platforms_data:
            availabilities = [
                float(item.get("CurrentWeekActual", 0)) for item in platforms_data
            ]
            targets = [
                float(item.get("CurrentWeekTarget", 0)) for item in platforms_data
            ]
            incidents = [int(item.get("IncidentCount", 0)) for item in platforms_data]

            summary["performance_summary"] = {
                "avg_availability": round(sum(availabilities) / len(availabilities), 2),
                "avg_target": round(sum(targets) / len(targets), 2),
                "total_incidents": sum(incidents),
                "platforms_above_target": len(
                    [a for a, t in zip(availabilities, targets) if a >= t]
                ),
                "platforms_below_target": len(
                    [a for a, t in zip(availabilities, targets) if a < t]
                ),
            }

            # Find best and worst performing platforms
            platform_performance = {}
            for item in platforms_data:
                platform = item.get("PlatformServiceType", "Unknown")
                actual = float(item.get("CurrentWeekActual", 0))
                target = float(item.get("CurrentWeekTarget", 0))

                if platform not in platform_performance:
                    platform_performance[platform] = []
                platform_performance[platform].append(
                    {"actual": actual, "target": target, "performance": actual - target}
                )

            # Calculate average performance per platform
            avg_performance = {}
            for platform, performances in platform_performance.items():
                avg_actual = sum(p["actual"] for p in performances) / len(performances)
                avg_target = sum(p["target"] for p in performances) / len(performances)
                avg_performance[platform] = {
                    "avg_actual": round(avg_actual, 2),
                    "avg_target": round(avg_target, 2),
                    "avg_performance": round(avg_actual - avg_target, 2),
                }

            # Sort by performance
            sorted_performance = sorted(
                avg_performance.items(),
                key=lambda x: x[1]["avg_performance"],
                reverse=True,
            )

            summary["best_performing"] = (
                sorted_performance[0] if sorted_performance else None
            )
            summary["worst_performing"] = (
                sorted_performance[-1] if sorted_performance else None
            )

        return summary

    def _create_platforms_analysis_prompt(self, data_summary: Dict) -> str:
        """Create a detailed prompt for platforms AI analysis"""

        prompt = f"""
You are a senior infrastructure analyst specializing in platform reliability and service availability.
Analyze the following RSP platform performance data and provide professional insights.

PLATFORM PERFORMANCE SUMMARY:
- Analysis Period: {data_summary.get('date_range', 'Unknown')}
- Total Weeks Analyzed: {data_summary.get('total_weeks', 0)}
- Total Platform Types: {len(data_summary.get('platform_types', []))}
- Platform Types: {', '.join(data_summary.get('platform_types', []))}

PERFORMANCE METRICS:
- Average Availability: {data_summary.get('performance_summary', {}).get('avg_availability', 0)}%
- Average Target: {data_summary.get('performance_summary', {}).get('avg_target', 0)}%
- Total Incidents: {data_summary.get('performance_summary', {}).get('total_incidents', 0)}
- Platforms Above Target: {data_summary.get('performance_summary', {}).get('platforms_above_target', 0)}
- Platforms Below Target: {data_summary.get('performance_summary', {}).get('platforms_below_target', 0)}
"""

        if data_summary.get("best_performing"):
            best = data_summary["best_performing"]
            prompt += f"""
BEST PERFORMING PLATFORM:
- {best[0]}: {best[1]['avg_actual']}% (Target: {best[1]['avg_target']}%, Performance: {best[1]['avg_performance']:+.2f}%)
"""

        if data_summary.get("worst_performing"):
            worst = data_summary["worst_performing"]
            prompt += f"""
PLATFORM NEEDING ATTENTION:
- {worst[0]}: {worst[1]['avg_actual']}% (Target: {worst[1]['avg_target']}%, Performance: {worst[1]['avg_performance']:+.2f}%)
"""

        prompt += """

ANALYSIS REQUIREMENTS:
Please provide a comprehensive analysis with the following sections:

1. **🏗️ Platform Reliability Overview**: Assess overall platform health and availability trends
2. **⚠️ Incident Analysis**: Evaluate incident patterns and their impact on service availability
3. **📊 Performance Benchmarking**: Compare platform performance against targets and industry standards
4. **🔧 Operational Insights**: Identify operational patterns and improvement opportunities
5. **💡 Strategic Recommendations**: Provide 3-5 specific, actionable recommendations for platform optimization

FORMATTING GUIDELINES:
- Use emojis for section headers
- Use bullet points (•) for key findings
- Keep insights concise but meaningful
- Focus on operational impact and actionable insights
- Highlight significant availability issues (<99%) and trends
- Provide specific percentages and incident counts where relevant

Generate professional, data-driven insights that would be valuable for infrastructure and operations teams.
"""

        return prompt

    def _fallback_platforms_statistical_analysis(
        self, platforms_data: List[Dict], comparison_data: Optional[Dict] = None
    ) -> str:
        """Fallback to statistical analysis for platforms when Ollama is unavailable"""

        logger.info("Using fallback statistical analysis for platforms")

        if not platforms_data or len(platforms_data) == 0:
            return "No platform data available for analysis."

        insights = []

        # Calculate basic statistics
        availabilities = [
            float(item.get("CurrentWeekActual", 0)) for item in platforms_data
        ]
        targets = [float(item.get("CurrentWeekTarget", 0)) for item in platforms_data]
        incidents = [int(item.get("IncidentCount", 0)) for item in platforms_data]

        avg_availability = sum(availabilities) / len(availabilities)
        total_incidents = sum(incidents)
        platforms_above_target = len(
            [a for a, t in zip(availabilities, targets) if a >= t]
        )
        platforms_below_target = len(
            [a for a, t in zip(availabilities, targets) if a < t]
        )

        insights.append("🏗️ **Platform Reliability Overview**")
        insights.append(f"• Average platform availability: {avg_availability:.2f}%")
        insights.append(f"• Platforms meeting targets: {platforms_above_target}")
        insights.append(f"• Platforms below targets: {platforms_below_target}")

        insights.append("\n⚠️ **Incident Analysis**")
        insights.append(f"• Total incidents across all platforms: {total_incidents}")
        if total_incidents > 0:
            insights.append(
                f"• Average incidents per platform: {total_incidents / len(set(item.get('PlatformServiceType', '') for item in platforms_data)):.1f}"
            )

        # Find platforms with issues
        problem_platforms = [
            item
            for item in platforms_data
            if float(item.get("CurrentWeekActual", 0))
            < float(item.get("CurrentWeekTarget", 0))
        ]
        if problem_platforms:
            insights.append("\n📊 **Performance Issues Identified**")
            for platform in problem_platforms[:3]:  # Show top 3 issues
                actual = float(platform.get("CurrentWeekActual", 0))
                target = float(platform.get("CurrentWeekTarget", 0))
                insights.append(
                    f"• {platform.get('PlatformServiceType', 'Unknown')}: {actual:.2f}% (Target: {target:.2f}%)"
                )

        insights.append("\n💡 **Key Recommendations**")
        insights.append("• Focus on platforms consistently below target availability")
        insights.append("• Implement proactive monitoring for early incident detection")
        insights.append("• Review incident response procedures for faster resolution")
        insights.append("• Consider redundancy improvements for critical platforms")

        return "\n".join(insights)

    def check_ollama_availability(self) -> bool:
        """Check if Ollama service is available"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
