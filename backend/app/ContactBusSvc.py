# ****************************************************************************
# Author: Name
# Desc: Business Services for Contact
#
#   Record Manager functions
#   - GetContacts           --> Get List
#   - GetContact            --> Get Record
#   - AddContact            --> Add Record
#   - UpdateContact         --> Update Record
#   - DeleteContact         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

import json
import logging

from AGGridServices import generateLimitOffsetSQL, generateOrderBySQL, generateWhereSQL
from DBHandler import get_connection
from flask import Response, jsonify, request

logger = logging.getLogger()


# ****************************************************************************
# Get List of <Entity> records (e.g. GetAssetCapJobs)
# ****************************************************************************
def GetContacts(request):

    logger.info("Inside GetContacts")

    # Get the payload which contains the parameters to query data
    # including AG Grid filter model, sort model and paging details
    payload = request.json

    conn = get_connection()

    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    Contact.id,
                    Contact.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Contact.FirstName,
                    Contact.LastName,
                    Contact.Role,
                    Contact.Phone,
                    Contact.Email,
                    Contact.created_by,
                    Contact.created,
                    Contact.modified_by,
                    Contact.modified
                from
                    Contact 
                    left outer join AccessSeeker on Contact.AccessSeekerRecordId = AccessSeeker.id"""

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)

        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)

        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)

        cursor.execute(sql)
        records = cursor.fetchall()

        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult["totalRecords"]

    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response


# ****************************************************************************
# Get a specific Contact Record
# ****************************************************************************
def GetContact(Id):

    logger.info("Inside GetContact. Id: " + str(Id))

    conn = get_connection()

    with conn.cursor() as cursor:

        sql = """select
                    Contact.id,
                    Contact.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Contact.FirstName,
                    Contact.LastName,
                    Contact.Role,
                    Contact.Phone,
                    Contact.Email,
                    Contact.Notes,
                    Contact.created_by,
                    Contact.created,
                    Contact.modified_by,
                    Contact.modified
                 from Contact
                 left outer join AccessSeeker on Contact.AccessSeekerRecordId = AccessSeeker.id
                 where Contact.id = %(Id)s"""

        cursor.execute(sql, {"Id": Id})

        # Get record
        record = cursor.fetchone()

    return record


# ****************************************************************************
# Add a new Contact record
# ****************************************************************************
def AddContact(contact):

    logger.info("Inside AddContact")

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "AccessSeekerRecordId" in contact:
        sqlFieldList += "AccessSeekerRecordId, "
        sqlFieldValues += "%(AccessSeekerRecordId)s, "
        sqlQueryParams["AccessSeekerRecordId"] = contact["AccessSeekerRecordId"]
    if "FirstName" in contact:
        sqlFieldList += "FirstName, "
        sqlFieldValues += "%(FirstName)s, "
        sqlQueryParams["FirstName"] = contact["FirstName"]
    if "LastName" in contact:
        sqlFieldList += "LastName, "
        sqlFieldValues += "%(LastName)s, "
        sqlQueryParams["LastName"] = contact["LastName"]
    if "Role" in contact:
        sqlFieldList += "Role, "
        sqlFieldValues += "%(Role)s, "
        sqlQueryParams["Role"] = contact["Role"]
    if "Phone" in contact:
        sqlFieldList += "Phone, "
        sqlFieldValues += "%(Phone)s, "
        sqlQueryParams["Phone"] = contact["Phone"]
    if "Email" in contact:
        sqlFieldList += "Email, "
        sqlFieldValues += "%(Email)s, "
        sqlQueryParams["Email"] = contact["Email"]
    if "Notes" in contact:
        sqlFieldList += "Notes, "
        sqlFieldValues += "%(Notes)s, "
        sqlQueryParams["Notes"] = contact["Notes"]

    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = (
            "INSERT INTO Contact (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"
        )

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)

        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            addedContact = GetContact(cursor.lastrowid)

        # Else record was not successfully added
        else:

            # Get the updated record
            addedContact = None

    return addedContact


# ****************************************************************************
# Update a Contact Record
# ****************************************************************************
def UpdateContact(id, patchValues):

    logger.info("Inside UpdateContact")

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "UPDATE Contact SET "

        if "AccessSeekerRecordId" in patchValues:
            sql += "AccessSeekerRecordId = %(AccessSeekerRecordId)s, "
            sqlQueryParams["AccessSeekerRecordId"] = patchValues["AccessSeekerRecordId"]
        if "FirstName" in patchValues:
            sql += "FirstName = %(FirstName)s, "
            sqlQueryParams["FirstName"] = patchValues["FirstName"]
        if "LastName" in patchValues:
            sql += "LastName = %(LastName)s, "
            sqlQueryParams["LastName"] = patchValues["LastName"]
        if "Role" in patchValues:
            sql += "Role = %(Role)s, "
            sqlQueryParams["Role"] = patchValues["Role"]
        if "Phone" in patchValues:
            sql += "Phone = %(Phone)s, "
            sqlQueryParams["Phone"] = patchValues["Phone"]
        if "Email" in patchValues:
            sql += "Email = %(Email)s, "
            sqlQueryParams["Email"] = patchValues["Email"]
        if "Notes" in patchValues:
            sql += "Notes = %(Notes)s, "
            sqlQueryParams["Notes"] = patchValues["Notes"]

        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info("UpdateContact SQL: " + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)

        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            updatedContact = GetContact(id)

        # Else record was not successfully updated
        else:

            # Get the updated record
            updatedContact = None

    return updatedContact


# ****************************************************************************
# Delete a Contact Record
# ****************************************************************************
def DeleteContact(request):

    logger.info("Inside DeleteContact")

    # Get the message body which contains the data to update
    payload = request.json

    # Get the Id of the record to delete
    Id = payload["id"]

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM Contact 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})

        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify(
                {
                    "data": {"id": str(Id)},
                    "message": "Successfully deleted Contact Record: " + str(Id),
                }
            )

        # Else record was not successfully deleted
        else:

            logger.info("Error deleting Contact with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps(
                {"code": 1, "error": "Error deleting Contact with id = " + str(Id)}
            )

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype="application/json")

    return response


# ****************************************************************************
# Get details of the logged in user
# Updated to use authentication middleware for JWT-based user context
# ****************************************************************************
def GetLoggedInUser(request=None):

    logger.info("Inside GetLoggedInUser")

    # Import here to avoid circular imports
    from auth_middleware import get_current_user

    try:
        # Get current user from authentication middleware
        user = get_current_user()
        userName = user.get("username", "unknown")

        logger.debug(f"Retrieved logged in user: {userName}")
        return userName

    except Exception as e:
        logger.warning(f"Error getting logged in user: {str(e)}")
        return "unknown"
