"""
Modern Entity API Blueprint - Clean URLs without /Test/ prefix
This blueprint provides RESTful API endpoints for all CRUD entities
following modern API design patterns.

URL Patterns:
- GET /api/v1/{entity}/{id} - Get single record
- POST /api/v1/{entity}/list - Get filtered list (with pagination)
- POST /api/v1/{entity} - Create new record
- PATCH /api/v1/{entity}/{id} - Update existing record
"""

import json
import logging

from AccessSeekerBusSvc import (
    AddAccessSeeker,
    GetAccessSeeker,
    GetAccessSeekers,
    UpdateAccessSeeker,
)
from ContactBusSvc import AddContact, GetContact, GetContacts, UpdateContact
from DigitalSvcBusSvc import (
    AddDigitalSvc,
    GetDigitalSvc,
    GetDigitalSvcs,
    UpdateDigitalSvc,
)
from DigitalSvcVersionBusSvc import (
    AddDigitalSvcVersion,
    GetDigitalSvcVersion,
    GetDigitalSvcVersions,
    UpdateDigitalSvcVersion,
)
from DigitalUsageChartBusSvc import (
    GetDigitalUsageByRSP,
    GetDigitalUsageByService,
    GetDigitalUsageForRSP,
    GetDigitalUsageForServices,
    GetDigitalUsageHistory,
)
from flask import Blueprint, Response, jsonify, request
from NoteBusSvc import AddNote, GetNote, GetNotes, UpdateNote
from ProjectBusSvc import AddProject, GetProject, GetProjects, UpdateProject
from TaskBusSvc import AddTask, GetTask, GetTasks, UpdateTask

logger = logging.getLogger(__name__)

# Create Blueprint for Entity APIs
entity_api_bp = Blueprint("entity_api", __name__, url_prefix="/api/v1")


# ****************************************************************************
# Access Seeker API Endpoints
# ****************************************************************************


@entity_api_bp.route("/access-seekers/list", methods=["POST"])
def get_access_seekers():
    """Get list of access seekers with filtering, sorting, and pagination"""
    try:
        logger.info("Getting access seekers list")
        return GetAccessSeekers(request)
    except Exception as e:
        logger.error(f"Error in get_access_seekers: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/access-seekers/<record_id>", methods=["GET"])
def get_access_seeker(record_id):
    """Get a single access seeker by ID"""
    try:
        logger.info(f"Getting access seeker with ID: {record_id}")
        access_seeker = GetAccessSeeker(record_id)
        return jsonify({"data": access_seeker})
    except Exception as e:
        logger.error(f"Error in get_access_seeker: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/access-seekers", methods=["POST"])
def create_access_seeker():
    """Create a new access seeker"""
    try:
        logger.info("Creating new access seeker")
        access_seeker_data = request.json
        added_access_seeker = AddAccessSeeker(access_seeker_data)
        return jsonify({"data": added_access_seeker}), 201
    except Exception as e:
        logger.error(f"Error in create_access_seeker: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/access-seekers/<record_id>", methods=["PATCH"])
def update_access_seeker(record_id):
    """Update an existing access seeker"""
    try:
        logger.info(f"Updating access seeker with ID: {record_id}")
        patch_values = request.json
        updated_access_seeker = UpdateAccessSeeker(record_id, patch_values)

        if updated_access_seeker:
            return jsonify({"data": updated_access_seeker})
        else:
            return (
                jsonify({"error": f"Access seeker with ID {record_id} not found"}),
                404,
            )
    except Exception as e:
        logger.error(f"Error in update_access_seeker: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Contact API Endpoints
# ****************************************************************************


@entity_api_bp.route("/contacts/list", methods=["POST"])
def get_contacts():
    """Get list of contacts with filtering, sorting, and pagination"""
    try:
        logger.info("Getting contacts list")
        return GetContacts(request)
    except Exception as e:
        logger.error(f"Error in get_contacts: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/contacts/<record_id>", methods=["GET"])
def get_contact(record_id):
    """Get a single contact by ID"""
    try:
        logger.info(f"Getting contact with ID: {record_id}")
        contact = GetContact(record_id)
        return jsonify({"data": contact})
    except Exception as e:
        logger.error(f"Error in get_contact: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/contacts", methods=["POST"])
def create_contact():
    """Create a new contact"""
    try:
        logger.info("Creating new contact")
        contact_data = request.json
        added_contact = AddContact(contact_data)
        return jsonify({"data": added_contact}), 201
    except Exception as e:
        logger.error(f"Error in create_contact: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/contacts/<record_id>", methods=["PATCH"])
def update_contact(record_id):
    """Update an existing contact"""
    try:
        logger.info(f"Updating contact with ID: {record_id}")
        patch_values = request.json
        updated_contact = UpdateContact(record_id, patch_values)

        if updated_contact:
            return jsonify({"data": updated_contact})
        else:
            return jsonify({"error": f"Contact with ID {record_id} not found"}), 404
    except Exception as e:
        logger.error(f"Error in update_contact: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Note API Endpoints
# ****************************************************************************


@entity_api_bp.route("/notes/list", methods=["POST"])
def get_notes():
    """Get list of notes with filtering, sorting, and pagination"""
    try:
        logger.info("Getting notes list")
        return GetNotes(request)
    except Exception as e:
        logger.error(f"Error in get_notes: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/notes/<record_id>", methods=["GET"])
def get_note(record_id):
    """Get a single note by ID"""
    try:
        logger.info(f"Getting note with ID: {record_id}")
        note = GetNote(record_id)
        return jsonify({"data": note})
    except Exception as e:
        logger.error(f"Error in get_note: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/notes", methods=["POST"])
def create_note():
    """Create a new note"""
    try:
        logger.info("Creating new note")
        note_data = request.json
        added_note = AddNote(note_data)
        return jsonify({"data": added_note}), 201
    except Exception as e:
        logger.error(f"Error in create_note: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/notes/<record_id>", methods=["PATCH"])
def update_note(record_id):
    """Update an existing note"""
    try:
        logger.info(f"Updating note with ID: {record_id}")
        patch_values = request.json
        updated_note = UpdateNote(record_id, patch_values)

        if updated_note:
            return jsonify({"data": updated_note})
        else:
            return jsonify({"error": f"Note with ID {record_id} not found"}), 404
    except Exception as e:
        logger.error(f"Error in update_note: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Task API Endpoints
# ****************************************************************************


@entity_api_bp.route("/tasks/list", methods=["POST"])
def get_tasks():
    """Get list of tasks with filtering, sorting, and pagination"""
    try:
        logger.info("Getting tasks list")
        return GetTasks(request)
    except Exception as e:
        logger.error(f"Error in get_tasks: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/tasks/<record_id>", methods=["GET"])
def get_task(record_id):
    """Get a single task by ID"""
    try:
        logger.info(f"Getting task with ID: {record_id}")
        task = GetTask(record_id)
        return jsonify({"data": task})
    except Exception as e:
        logger.error(f"Error in get_task: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/tasks", methods=["POST"])
def create_task():
    """Create a new task"""
    try:
        logger.info("Creating new task")
        task_data = request.json
        added_task = AddTask(task_data)
        return jsonify({"data": added_task}), 201
    except Exception as e:
        logger.error(f"Error in create_task: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/tasks/<record_id>", methods=["PATCH"])
def update_task(record_id):
    """Update an existing task"""
    try:
        logger.info(f"Updating task with ID: {record_id}")
        patch_values = request.json
        updated_task = UpdateTask(record_id, patch_values)

        if updated_task:
            return jsonify({"data": updated_task})
        else:
            return jsonify({"error": f"Task with ID {record_id} not found"}), 404
    except Exception as e:
        logger.error(f"Error in update_task: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Project API Endpoints
# ****************************************************************************


@entity_api_bp.route("/projects/list", methods=["POST"])
def get_projects():
    """Get list of projects with filtering, sorting, and pagination"""
    try:
        logger.info("Getting projects list")
        return GetProjects(request)
    except Exception as e:
        logger.error(f"Error in get_projects: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/projects/<record_id>", methods=["GET"])
def get_project(record_id):
    """Get a single project by ID"""
    try:
        logger.info(f"Getting project with ID: {record_id}")
        project = GetProject(record_id)
        return jsonify({"data": project})
    except Exception as e:
        logger.error(f"Error in get_project: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/projects", methods=["POST"])
def create_project():
    """Create a new project"""
    try:
        logger.info("Creating new project")
        project_data = request.json
        added_project = AddProject(project_data)
        return jsonify({"data": added_project}), 201
    except Exception as e:
        logger.error(f"Error in create_project: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/projects/<record_id>", methods=["PATCH"])
def update_project(record_id):
    """Update an existing project"""
    try:
        logger.info(f"Updating project with ID: {record_id}")
        patch_values = request.json
        updated_project = UpdateProject(record_id, patch_values)

        if updated_project:
            return jsonify({"data": updated_project})
        else:
            return jsonify({"error": f"Project with ID {record_id} not found"}), 404
    except Exception as e:
        logger.error(f"Error in update_project: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Digital Service API Endpoints
# ****************************************************************************


@entity_api_bp.route("/digital-services/list", methods=["POST"])
def get_digital_services():
    """Get list of digital services with filtering, sorting, and pagination"""
    try:
        logger.info("Getting digital services list")
        return GetDigitalSvcs(request)
    except Exception as e:
        logger.error(f"Error in get_digital_services: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-services/<record_id>", methods=["GET"])
def get_digital_service(record_id):
    """Get a single digital service by ID"""
    try:
        logger.info(f"Getting digital service with ID: {record_id}")
        digital_service = GetDigitalSvc(record_id)
        return jsonify({"data": digital_service})
    except Exception as e:
        logger.error(f"Error in get_digital_service: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-services", methods=["POST"])
def create_digital_service():
    """Create a new digital service"""
    try:
        logger.info("Creating new digital service")
        digital_service_data = request.json
        added_digital_service = AddDigitalSvc(digital_service_data)
        return jsonify({"data": added_digital_service}), 201
    except Exception as e:
        logger.error(f"Error in create_digital_service: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-services/<record_id>", methods=["PATCH"])
def update_digital_service(record_id):
    """Update an existing digital service"""
    try:
        logger.info(f"Updating digital service with ID: {record_id}")
        patch_values = request.json
        updated_digital_service = UpdateDigitalSvc(record_id, patch_values)

        if updated_digital_service:
            return jsonify({"data": updated_digital_service})
        else:
            return (
                jsonify({"error": f"Digital service with ID {record_id} not found"}),
                404,
            )
    except Exception as e:
        logger.error(f"Error in update_digital_service: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Digital Service Version API Endpoints
# ****************************************************************************


@entity_api_bp.route("/digital-service-versions/list", methods=["POST"])
def get_digital_service_versions():
    """Get list of digital service versions with filtering, sorting, and pagination"""
    try:
        logger.info("Getting digital service versions list")
        return GetDigitalSvcVersions(request)
    except Exception as e:
        logger.error(f"Error in get_digital_service_versions: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-service-versions/<record_id>", methods=["GET"])
def get_digital_service_version(record_id):
    """Get a single digital service version by ID"""
    try:
        logger.info(f"Getting digital service version with ID: {record_id}")
        digital_service_version = GetDigitalSvcVersion(record_id)
        return jsonify({"data": digital_service_version})
    except Exception as e:
        logger.error(f"Error in get_digital_service_version: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-service-versions", methods=["POST"])
def create_digital_service_version():
    """Create a new digital service version"""
    try:
        logger.info("Creating new digital service version")
        digital_service_version_data = request.json
        added_digital_service_version = AddDigitalSvcVersion(
            digital_service_version_data
        )
        return jsonify({"data": added_digital_service_version}), 201
    except Exception as e:
        logger.error(f"Error in create_digital_service_version: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-service-versions/<record_id>", methods=["PATCH"])
def update_digital_service_version(record_id):
    """Update an existing digital service version"""
    try:
        logger.info(f"Updating digital service version with ID: {record_id}")
        patch_values = request.json
        updated_digital_service_version = UpdateDigitalSvcVersion(
            record_id, patch_values
        )

        if updated_digital_service_version:
            return jsonify({"data": updated_digital_service_version})
        else:
            return (
                jsonify(
                    {"error": f"Digital service version with ID {record_id} not found"}
                ),
                404,
            )
    except Exception as e:
        logger.error(f"Error in update_digital_service_version: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# ****************************************************************************
# Digital Usage Analytics API Endpoints
# ****************************************************************************


@entity_api_bp.route("/digital-usage/history", methods=["GET"])
def get_digital_usage_history():
    """Get digital usage history data"""
    try:
        logger.info("Getting digital usage history")
        return GetDigitalUsageHistory()
    except Exception as e:
        logger.error(f"Error in get_digital_usage_history: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-usage/by-rsp", methods=["GET"])
def get_digital_usage_by_rsp():
    """Get digital usage data by RSP"""
    try:
        logger.info("Getting digital usage by RSP")
        period = request.args.get("period", default=None, type=str)
        return GetDigitalUsageByRSP(period)
    except Exception as e:
        logger.error(f"Error in get_digital_usage_by_rsp: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-usage/by-service", methods=["GET"])
def get_digital_usage_by_service():
    """Get digital usage data by service"""
    try:
        logger.info("Getting digital usage by service")
        period = request.args.get("period", default=None, type=str)
        return GetDigitalUsageByService(period)
    except Exception as e:
        logger.error(f"Error in get_digital_usage_by_service: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-usage/for-rsp", methods=["GET"])
def get_digital_usage_for_rsp():
    """Get digital usage data for specific RSP"""
    try:
        logger.info("Getting digital usage for RSP")
        access_seeker_id = request.args.get("accessSeekerId", default=None, type=str)
        return GetDigitalUsageForRSP(access_seeker_id)
    except Exception as e:
        logger.error(f"Error in get_digital_usage_for_rsp: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@entity_api_bp.route("/digital-usage/for-services", methods=["GET"])
def get_digital_usage_for_services():
    """Get digital usage data for services"""
    try:
        logger.info("Getting digital usage for services")
        result = GetDigitalUsageForServices()
        if isinstance(result, str):
            import json

            data = json.loads(result)
            return jsonify(data)
        else:
            return jsonify({"data": result})
    except Exception as e:
        logger.error(f"Error in get_digital_usage_for_services: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500
