# ****************************************************************************
# Author: Name
# Desc: Business Services for Digital Usage
#
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

import json
import logging
from decimal import Decimal

from AGGridServices import generateLimitOffsetSQL, generateOrderBySQL, generateWhereSQL
from DBHandler import get_connection
from flask import Response, jsonify, request

logger = logging.getLogger()


# ****************************************************************************
# Get Digital Usage History
# ****************************************************************************
def GetDigitalUsageHistory():

    logger.info("Inside GetDigitalUsageHistory")

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate the query
        sql = """
        select
            distinct(AllPeriods.Period) as Period,
            AllTxn.TotalTxns as TotalTxns,
            APITxn.TotalTxns as TotalAPITxns,
            PortalTxn.TotalTxns as TotalPortalTxns
        from
            DigitalUsage AllPeriods
        left outer join
            (select Period, sum(Total) as TotalTxns from DigitalUsage dudv group by Period) as AllTxn on AllPeriods.Period = AllTxn.Period
        left outer join
            (select Period, sum(Total) as TotalTxns from DigitalUsage dudv where businessChannel = 'APIGWY' group by Period) as APITxn on AllPeriods.Period = APITxn.Period
        left outer join
            (select Period, sum(Total) as TotalTxns from DigitalUsage dudv where businessChannel = 'ServicePortal' group by Period) as PortalTxn on AllPeriods.Period = PortalTxn.Period
        """

        cursor.execute(sql)
        records = cursor.fetchall()

    response = json.dumps({"data": records}, default=myconverter)
    return response


# ****************************************************************************
# Get Digital Usage by RSP for a given period
# ****************************************************************************
def GetDigitalUsageByRSP(period: str):

    logger.info("Inside GetDigitalUsageByRSP")

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate the query
        sql = """
            select
                as2.AccessSeekerId,
                as2.Name,
                as2.Category1,
                rank() over (ORDER by AllTxn.TotalTxns desc) as AllDigitalVolRank,
                rank() over (ORDER by APITxn.TotalTxns desc) as APITxnVolRank,
                rank() over (ORDER by PortalTxn.TotalTxns desc) as PortalTxnVolRank,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                AllTxn.TotalTxns as TotalTxns,
                round((1.0 * APITxn.TotalTxns) / AllTxn.TotalTxns, 2) * 100 as APIPercentOfTxns
            from
                AccessSeeker as2
            left outer join
            (select accessSeekerId, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s group by accessSeekerId) as AllTxn on as2.AccessSeekerId = AllTxn.accessSeekerId
            left outer join
            (select accessSeekerId, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s and businessChannel = 'APIGWY' group by accessSeekerId) as APITxn on AllTxn.accessSeekerId = APITxn.accessSeekerId
            left outer join
            (select accessSeekerId, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s and businessChannel = 'ServicePortal' group by accessSeekerId) as PortalTxn on AllTxn.accessSeekerId = PortalTxn.accessSeekerId
            order by AllTxn.TotalTxns desc
            """

        cursor.execute(sql, {"period": period})
        records = cursor.fetchall()

    response = json.dumps({"data": records}, default=myconverter)
    return response


# ****************************************************************************
# Get Digital Usage by Service for a given period
# ****************************************************************************
def GetDigitalUsageByService(period: str):

    logger.info("Inside GetDigitalUsageByService")

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate the query
        sql = """
            select
                ds.ServiceName,
                ds.APIName,
                rank() over (ORDER by AllTxn.TotalTxns desc) as AllDigitalVolRank,
                rank() over (ORDER by APITxn.TotalTxns desc) as APITxnVolRank,
                rank() over (ORDER by PortalTxn.TotalTxns desc) as PortalTxnVolRank,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                AllTxn.TotalTxns as TotalTxns,
                round((1.0 * APITxn.TotalTxns) / AllTxn.TotalTxns, 2) * 100 as APIPercentOfTxns
            from
                DigitalService ds
            left outer join
            (select serviceName, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s group by serviceName) as AllTxn on ds.APIName = AllTxn.serviceName
            left outer join
            (select serviceName, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s and businessChannel = 'APIGWY' group by serviceName) as APITxn on AllTxn.serviceName = APITxn.serviceName
            left outer join
            (select serviceName, sum(Total) as TotalTxns from DigitalUsage du where period = %(period)s and businessChannel = 'ServicePortal' group by serviceName) as PortalTxn on AllTxn.serviceName = PortalTxn.serviceName
            order by AllTxn.TotalTxns desc
            """

        cursor.execute(sql, {"period": period})
        records = cursor.fetchall()

    response = json.dumps({"data": records}, default=myconverter)
    return response


# ######################## Get Digital Usage for an RSP ######################


# ****************************************************************************
# Get the list of Digital Services
# ****************************************************************************
def GetDigitalServices():

    logger.info("Inside GetDigitalServices")

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate the query
        sql = """
            select
                ServiceCode,
                ServiceName,
                APIName
            from
                DigitalService
            where 1=1
            """

        cursor.execute(sql)
        records = cursor.fetchall()

    return records


# ****************************************************************************
# Get Digital Usage for an RSP
# ****************************************************************************
def GetDigitalUsageForRSP(accessSeekerId: str):

    logger.info("Inside GetDigitalUsageForRSP")

    # Get the RSP total digital usage
    digitalUsageAllRecords = GetDigitalUsageRecordsForRSP(accessSeekerId)

    digitalUsage = {}
    digitalUsage["All"] = {
        "ServiceName": "All Services",
        "APIName": "N/A",
        "UsageRecords": digitalUsageAllRecords,
    }

    # Get the list of digital services
    digitalServices = GetDigitalServices()

    # Loop through each service and get the usage for that service
    for aDigitalService in digitalServices:

        serviceCode = aDigitalService["ServiceCode"]
        serviceName = aDigitalService["ServiceName"]
        apiName = aDigitalService["APIName"]

        # Get Usage for the service
        usageRecords = GetDigitalUsageRecordsForRSP(accessSeekerId, apiName)

        digitalUsage[serviceCode] = {
            "ServiceName": serviceName,
            "APIName": apiName,
            "UsageRecords": usageRecords,
        }

    response = json.dumps({"DigitalUsage": digitalUsage}, default=myconverter)
    return response


# ****************************************************************************
# Get Digital Usage Records for an RSP and Service (optional)
# ****************************************************************************
def GetDigitalUsageRecordsForRSP(accessSeekerId: str, serviceName: str = None):

    logger.info("Inside GetDigitalUsageForRSP")

    # If the serviceName is None, we want to get the total usage for the RSP
    if serviceName is None:
        sql = """
            select
                distinct(AllPeriods.Period) as Period,
                AllTxn.TotalTxns as TotalTxns,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                APITxn.SuccessTxns as SuccessAPITxns,
                APITxn.BusExceptionCount as APIBusExceptionCount,
                APITxn.TechExceptionCount as APITechExceptionCount
            from
                DigitalUsage AllPeriods
            left outer join
            (select Period, accessSeekerId, sum(Total) as TotalTxns from DigitalUsage du group by Period, accessSeekerId) as AllTxn on AllPeriods.Period = AllTxn.Period
            left outer join
            (select Period, accessSeekerId, sum(Total) as TotalTxns, sum(Success) as SuccessTxns, sum(BusinessException) as BusExceptionCount, sum(TechnicalException) as TechExceptionCount from DigitalUsage du where businessChannel = 'APIGWY' group by Period, accessSeekerId) as APITxn on AllPeriods.Period = APITxn.Period and AllTxn.accessSeekerId = APITxn.accessSeekerId
            left outer join
            (select Period, accessSeekerId, sum(Total) as TotalTxns from DigitalUsage du where businessChannel = 'ServicePortal' group by Period, accessSeekerId) as PortalTxn on AllPeriods.Period = PortalTxn.Period and AllTxn.accessSeekerId = PortalTxn.accessSeekerId
            where AllTxn.accessSeekerId = %(accessSeekerId)s
            """
    # Else the service has been provided so get the usage for that specific service
    else:

        # Generate the query
        sql = """
            select
                distinct(AllPeriods.Period) as Period,
                AllTxn.TotalTxns as TotalTxns,
                APITxn.TotalTxns as TotalAPITxns,
                PortalTxn.TotalTxns as TotalPortalTxns,
                APITxn.SuccessTxns as SuccessAPITxns,
                APITxn.BusExceptionCount as APIBusExceptionCount,
                APITxn.TechExceptionCount as APITechExceptionCount
            from
                DigitalUsage AllPeriods
            left outer join
            (select Period, accessSeekerId, serviceName, sum(Total) as TotalTxns from DigitalUsage du where servicename=%(serviceName)s group by Period, accessSeekerId, serviceName) as AllTxn on AllPeriods.Period = AllTxn.Period
            left outer join
            (select Period, accessSeekerId, serviceName, sum(Total) as TotalTxns, sum(Success) as SuccessTxns, sum(BusinessException) as BusExceptionCount, sum(TechnicalException) as TechExceptionCount from DigitalUsage du where servicename=%(serviceName)s and businessChannel = 'APIGWY' group by Period, accessSeekerId, serviceName) as APITxn on AllPeriods.Period = APITxn.Period and AllTxn.accessSeekerId = APITxn.accessSeekerId
            left outer join
            (select Period, accessSeekerId, serviceName, sum(Total) as TotalTxns from DigitalUsage du where servicename=%(serviceName)s and businessChannel = 'ServicePortal' group by Period, accessSeekerId, serviceName) as PortalTxn on AllPeriods.Period = PortalTxn.Period and AllTxn.accessSeekerId = PortalTxn.accessSeekerId
            where AllTxn.accessSeekerId = %(accessSeekerId)s
            """

    conn = get_connection()

    # Get the records from the database
    with conn.cursor() as cursor:

        cursor.execute(
            sql, {"accessSeekerId": accessSeekerId, "serviceName": serviceName}
        )
        records = cursor.fetchall()

    # Return the records
    return records


# ######################## Get Digital Usage for Services ####################


# ****************************************************************************
# Get Digital Usage for Services
# ****************************************************************************
def GetDigitalUsageForServices():

    logger.info("Inside GetDigitalUsageForServices")

    # Initialise variables
    digitalUsage = {}

    # Get the list of digital services
    digitalServices = GetDigitalServices()

    # Loop through each service and get the usage for that service
    for aDigitalService in digitalServices:

        serviceCode = aDigitalService["ServiceCode"]
        serviceName = aDigitalService["ServiceName"]
        apiName = aDigitalService["APIName"]

        # Get Usage for the service
        usageRecords = GetDigitalUsageRecordsForService(apiName)

        digitalUsage[serviceCode] = {
            "ServiceName": serviceName,
            "APIName": apiName,
            "UsageRecords": usageRecords,
        }

    response = json.dumps({"DigitalUsage": digitalUsage}, default=myconverter)
    return response


# ****************************************************************************
# Get Digital Usage Records for a Service
# ****************************************************************************
def GetDigitalUsageRecordsForService(serviceName: str):

    logger.info("Inside GetDigitalUsageRecordsForService")

    sql = """
    select
        distinct(AllPeriods.Period) as Period,
        AllTxn.TotalTxns as TotalTxns,
        APITxn.TotalTxns as TotalAPITxns,
        PortalTxn.TotalTxns as TotalPortalTxns,
        APITxn.SuccessTxns as SuccessAPITxns,
        APITxn.BusExceptionCount as APIBusExceptionCount,
        APITxn.TechExceptionCount as APITechExceptionCount
    from
        DigitalUsage AllPeriods
    left outer join
    (select Period, serviceName, sum(Total) as TotalTxns from DigitalUsage du where servicename=%(serviceName)s group by Period, serviceName) as AllTxn on AllPeriods.Period = AllTxn.Period
    left outer join
    (select Period, serviceName, sum(Total) as TotalTxns, sum(Success) as SuccessTxns, sum(BusinessException) as BusExceptionCount, sum(TechnicalException) as TechExceptionCount from DigitalUsage du where servicename=%(serviceName)s and businessChannel = 'APIGWY' group by Period, serviceName) as APITxn on AllPeriods.Period = APITxn.Period
    left outer join
    (select Period, serviceName, sum(Total) as TotalTxns from DigitalUsage du where servicename=%(serviceName)s and businessChannel = 'ServicePortal' group by Period, serviceName) as PortalTxn on AllPeriods.Period = PortalTxn.Period
    """

    conn = get_connection()

    # Get the records from the database
    with conn.cursor() as cursor:

        cursor.execute(sql, {"serviceName": serviceName})
        records = cursor.fetchall()

    # Return the records
    return records


# ****************************************************************************
# Get details of the logged in user
# Updated to use authentication middleware for JWT-based user context
# ****************************************************************************
def GetLoggedInUser(request=None):

    logger.info("Inside GetLoggedInUser")

    # Import here to avoid circular imports
    from auth_middleware import get_current_user

    try:
        # Get current user from authentication middleware
        user = get_current_user()
        userName = user.get("username", "unknown")

        logger.debug(f"Retrieved logged in user: {userName}")
        return userName

    except Exception as e:
        logger.warning(f"Error getting logged in user: {str(e)}")
        return "unknown"


def myconverter(o):
    if isinstance(o, Decimal):
        return o.__float__()
