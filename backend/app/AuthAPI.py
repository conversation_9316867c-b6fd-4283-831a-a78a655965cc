"""
Authentication API Blueprint for RSPi Application

This blueprint provides authentication endpoints with environment-based behavior:
- Development mode: Authentication bypass with mock responses
- Live mode: LDAP authentication with JWT token generation

Endpoints:
- POST /api/v1/auth/login - User authentication
- POST /api/v1/auth/logout - User logout (token invalidation)
- GET /api/v1/auth/me - Get current user information
- GET /api/v1/auth/validate - Validate current token
"""

import logging
from typing import Dict, Optional

from auth_middleware import generate_jwt_token, get_current_user, require_auth, validate_jwt_token
from config import get_app_config
from flask import Blueprint, jsonify, request
from ldap_auth import ad_auth, validate_ldap_connection

logger = logging.getLogger(__name__)

# Create Authentication Blueprint
auth_bp = Blueprint("auth", __name__, url_prefix="/api/v1/auth")


@auth_bp.route("/login", methods=["POST"])
def login():
    """
    Authenticate user and return JWT token.
    
    Request Body:
        {
            "username": "string",
            "password": "string"
        }
        
    Response (Success):
        {
            "success": true,
            "token": "jwt_token_string",
            "user": {
                "username": "string",
                "displayName": "string",
                "email": "string",
                "groups": ["group1", "group2"]
            }
        }
        
    Response (Error):
        {
            "success": false,
            "error": "error_message"
        }
    """
    try:
        config = get_app_config()
        
        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                "success": False,
                "error": "Request body must be JSON"
            }), 400
        
        username = data.get("username", "").strip()
        password = data.get("password", "")
        
        if not username or not password:
            return jsonify({
                "success": False,
                "error": "Username and password are required"
            }), 400
        
        logger.info(f"Login attempt for user: {username}")
        
        if not config.auth_enabled:
            # Development mode - bypass authentication
            logger.info("Authentication bypassed (development mode)")
            
            dev_user = {
                "username": username,
                "displayName": f"Dev User ({username})",
                "email": f"{username}@nbnco.com.au",
                "groups": ["dev-group"]
            }
            
            return jsonify({
                "success": True,
                "token": "dev-token",
                "user": dev_user
            })
        
        # Live mode - LDAP authentication
        logger.info(f"Attempting LDAP authentication for user: {username}")
        
        user_info = ad_auth(username, password)
        if user_info:
            # Authentication successful - generate JWT token
            try:
                # Prepare user data for token
                token_user_info = {
                    "username": username,
                    "displayName": user_info["displayName"],
                    "mail": user_info["mail"],
                    "groups": user_info["groups"]
                }
                
                token = generate_jwt_token(token_user_info)
                
                # Prepare response user data
                response_user = {
                    "username": username,
                    "displayName": user_info["displayName"],
                    "email": user_info["mail"],
                    "groups": user_info["groups"]
                }
                
                logger.info(f"Login successful for user: {username}")
                
                return jsonify({
                    "success": True,
                    "token": token,
                    "user": response_user
                })
                
            except Exception as e:
                logger.error(f"Token generation failed for user {username}: {str(e)}")
                return jsonify({
                    "success": False,
                    "error": "Authentication system error"
                }), 500
        else:
            # Authentication failed
            logger.warning(f"LDAP authentication failed for user: {username}")
            return jsonify({
                "success": False,
                "error": "Invalid username or password"
            }), 401
            
    except Exception as e:
        logger.error(f"Login endpoint error: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500


@auth_bp.route("/logout", methods=["POST"])
@require_auth
def logout():
    """
    Logout user (invalidate token).
    
    Note: In a stateless JWT implementation, logout is handled client-side
    by removing the token. This endpoint provides a consistent API.
    
    Response:
        {
            "success": true,
            "message": "Logged out successfully"
        }
    """
    try:
        user = get_current_user()
        logger.info(f"User logout: {user.get('username')}")
        
        return jsonify({
            "success": True,
            "message": "Logged out successfully"
        })
        
    except Exception as e:
        logger.error(f"Logout endpoint error: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500


@auth_bp.route("/me", methods=["GET"])
@require_auth
def get_current_user_info():
    """
    Get current authenticated user information.
    
    Response:
        {
            "success": true,
            "user": {
                "username": "string",
                "displayName": "string",
                "email": "string",
                "groups": ["group1", "group2"]
            }
        }
    """
    try:
        user = get_current_user()
        
        return jsonify({
            "success": True,
            "user": user
        })
        
    except Exception as e:
        logger.error(f"Get current user endpoint error: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500


@auth_bp.route("/validate", methods=["GET"])
def validate_token():
    """
    Validate JWT token without requiring authentication middleware.
    
    Headers:
        Authorization: Bearer <token>
        
    Response (Valid):
        {
            "success": true,
            "valid": true,
            "user": {
                "username": "string",
                "displayName": "string",
                "email": "string",
                "groups": ["group1", "group2"]
            }
        }
        
    Response (Invalid):
        {
            "success": true,
            "valid": false,
            "error": "error_message"
        }
    """
    try:
        config = get_app_config()
        
        if not config.auth_enabled:
            # Development mode - always valid
            return jsonify({
                "success": True,
                "valid": True,
                "user": {
                    "username": "dev-user",
                    "displayName": "Development User",
                    "email": "<EMAIL>",
                    "groups": ["dev-group"]
                }
            })
        
        # Live mode - validate JWT token
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return jsonify({
                "success": True,
                "valid": False,
                "error": "Missing or invalid Authorization header"
            })
        
        token = auth_header.split(" ")[1]
        user_info = validate_jwt_token(token)
        
        if user_info:
            return jsonify({
                "success": True,
                "valid": True,
                "user": user_info
            })
        else:
            return jsonify({
                "success": True,
                "valid": False,
                "error": "Invalid or expired token"
            })
            
    except Exception as e:
        logger.error(f"Token validation endpoint error: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500


@auth_bp.route("/health", methods=["GET"])
def auth_health():
    """
    Authentication system health check.
    
    Response:
        {
            "success": true,
            "auth_enabled": boolean,
            "ldap_connectivity": boolean (only in live mode)
        }
    """
    try:
        config = get_app_config()
        
        health_info = {
            "success": True,
            "auth_enabled": config.auth_enabled
        }
        
        if config.auth_enabled:
            # Test LDAP connectivity in live mode
            health_info["ldap_connectivity"] = validate_ldap_connection()
        
        return jsonify(health_info)
        
    except Exception as e:
        logger.error(f"Auth health endpoint error: {str(e)}")
        return jsonify({
            "success": False,
            "error": "Internal server error"
        }), 500
