import logging

from config import get_app_config
from DBHandler import get_connection
from insight_generator import InsightGenerator

logger = logging.getLogger(__name__)


class CioRspExecutiveBusSvc:

    @staticmethod
    def GetDigitalUsageHistory(start_period=None, end_period=None):
        """Get digital usage history data for trends analysis"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Query to get aggregated data by period and channel
            query = """
            WITH PeriodData AS (
                SELECT DISTINCT Period
                FROM DigitalUsageDetailView
                ORDER BY SUBSTRING(Period, -2) DESC,
                    CASE
                        WHEN SUBSTRING(Period, 1, 3) = 'Jan' THEN '01'
                        WHEN SUBSTRING(Period, 1, 3) = 'Feb' THEN '02'
                        WHEN SUBSTRING(Period, 1, 3) = 'Mar' THEN '03'
                        WHEN SUBSTRING(Period, 1, 3) = 'Apr' THEN '04'
                        WHEN SUBSTRING(Period, 1, 3) = 'May' THEN '05'
                        WHEN SUBSTRING(Period, 1, 3) = 'Jun' THEN '06'
                        WHEN SUBSTRING(Period, 1, 3) = 'Jul' THEN '07'
                        WHEN SUBSTRING(Period, 1, 3) = 'Aug' THEN '08'
                        WHEN SUBSTRING(Period, 1, 3) = 'Sep' THEN '09'
                        WHEN SUBSTRING(Period, 1, 3) = 'Oct' THEN '10'
                        WHEN SUBSTRING(Period, 1, 3) = 'Nov' THEN '11'
                        WHEN SUBSTRING(Period, 1, 3) = 'Dec' THEN '12'
                    END DESC
            )
            SELECT
                p.Period,
                COALESCE(AllTxn.TotalTxns, 0) as TotalTxns,
                COALESCE(APITxn.TotalTxns, 0) as TotalAPITxns,
                COALESCE(PortalTxn.TotalTxns, 0) as TotalPortalTxns
            FROM
                PeriodData p
            LEFT OUTER JOIN
                (SELECT Period, SUM(Total) as TotalTxns
                 FROM DigitalUsageDetailView
                 GROUP BY Period) as AllTxn
            ON p.Period = AllTxn.Period
            LEFT OUTER JOIN
                (SELECT Period, SUM(Total) as TotalTxns
                 FROM DigitalUsageDetailView
                 WHERE businessChannel = 'APIGWY'
                 GROUP BY Period) as APITxn
            ON p.Period = APITxn.Period
            LEFT OUTER JOIN
                (SELECT Period, SUM(Total) as TotalTxns
                 FROM DigitalUsageDetailView
                 WHERE businessChannel = 'ServicePortal'
                 GROUP BY Period) as PortalTxn
            ON p.Period = PortalTxn.Period
            WHERE AllTxn.TotalTxns IS NOT NULL
            """

            # Add date range filtering if provided
            params = []
            if start_period and end_period:
                query += " AND p.Period BETWEEN %s AND %s"
                params.extend([start_period, end_period])
                logger.info(
                    f"Applying date range filter: {start_period} to {end_period}"
                )

            query += " ORDER BY p.Period"

            cursor.execute(query, params)
            results = cursor.fetchall()

            logger.info(f"Retrieved {len(results)} digital usage history records")
            return results

        except Exception as e:
            logger.error(f"Error getting digital usage history: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetDigitalUsagePeriods():
        """Get available periods for digital usage data"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            query = """
            SELECT DISTINCT Period
            FROM DigitalUsageDetailView
            ORDER BY SUBSTRING(Period, -2) DESC,
                CASE
                    WHEN SUBSTRING(Period, 1, 3) = 'Jan' THEN '01'
                    WHEN SUBSTRING(Period, 1, 3) = 'Feb' THEN '02'
                    WHEN SUBSTRING(Period, 1, 3) = 'Mar' THEN '03'
                    WHEN SUBSTRING(Period, 1, 3) = 'Apr' THEN '04'
                    WHEN SUBSTRING(Period, 1, 3) = 'May' THEN '05'
                    WHEN SUBSTRING(Period, 1, 3) = 'Jun' THEN '06'
                    WHEN SUBSTRING(Period, 1, 3) = 'Jul' THEN '07'
                    WHEN SUBSTRING(Period, 1, 3) = 'Aug' THEN '08'
                    WHEN SUBSTRING(Period, 1, 3) = 'Sep' THEN '09'
                    WHEN SUBSTRING(Period, 1, 3) = 'Oct' THEN '10'
                    WHEN SUBSTRING(Period, 1, 3) = 'Nov' THEN '11'
                    WHEN SUBSTRING(Period, 1, 3) = 'Dec' THEN '12'
                END DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            logger.info(f"Retrieved {len(results)} available periods")
            return results

        except Exception as e:
            logger.error(f"Error getting digital usage periods: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetDigitalServiceUsage(period):
        """Get digital service usage data for a specific period"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Enhanced SQL query to include total transactions for percentage calculation
            query = """
            WITH ServiceTotals AS (
                SELECT
                    serviceName,
                    SUM(Total) as TotalTxns
                FROM DigitalUsageDetailView
                WHERE period = %s
                GROUP BY serviceName
            )
            SELECT
               a.APICode,
               a.APIName as ServiceName,
               CASE WHEN a.UsedForConnect = 'Y' THEN 1 ELSE 0 END as UsedForConnect,
               CASE WHEN a.UsedForAssure = 'Y' THEN 1 ELSE 0 END as UsedForAssure,
               a.LatestVersion,
               ASCert.certcount as APICertCount,
               (ASCert.certcount - IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0)) as OnLatestVersionCount,
               IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0) as NotOnLatestVersionCount,
               ROUND((1.0 * (ASCert.certcount - IFNULL(ASNotOnLatestVersion.notonlatestversioncount, 0)) / ASCert.certcount),2) as CertOnLatestVersionPercent,
               APIUsage.utilcount as APIUtilCount,
               COALESCE(APIUsage.TotalTxnCount, 0) as TotalAPITxns,
               COALESCE(PortalUsage.TotalTxnCount, 0) as TotalPortalTxns,
               CASE
                   WHEN COALESCE(st.TotalTxns, 0) > 0 THEN ROUND(100.0 * COALESCE(APIUsage.TotalTxnCount, 0) / st.TotalTxns, 1)
                   ELSE 0
               END as APIPercentage
            FROM APIs a
            LEFT JOIN (SELECT CertifiedAPIName, count(*) as certcount FROM AccessSeeker_CertifiedAPIs GROUP BY CertifiedAPIName) as ASCert
                ON a.APIName = ASCert.CertifiedAPIName
            LEFT JOIN (SELECT serviceName, count(distinct(accessSeekerId)) as utilcount, sum(Total) as TotalTxnCount
                      FROM DigitalUsageDetailView duv
                      WHERE businessChannel = 'APIGWY' AND period = %s
                      GROUP BY serviceName) as APIUsage
                ON a.UsageReportAPIName = APIUsage.serviceName
            LEFT JOIN (SELECT serviceName, count(distinct(accessSeekerId)) as utilcount, sum(Total) as TotalTxnCount
                      FROM DigitalUsageDetailView duv
                      WHERE businessChannel = 'ServicePortal' AND period = %s
                      GROUP BY serviceName) as PortalUsage
                ON a.UsageReportAPIName = PortalUsage.serviceName
            LEFT JOIN (SELECT CertifiedAPIName, count(*) as notonlatestversioncount
                      FROM AccessSeeker_CertifiedAPIs asca
                      JOIN APIs a ON asca.CertifiedAPIName = a.APIName
                      WHERE asca.LatestCertifiedVersion != a.LatestVersion
                      GROUP BY CertifiedAPIName) as ASNotOnLatestVersion
                ON a.APIName = ASNotOnLatestVersion.CertifiedAPIName
            LEFT JOIN ServiceTotals st ON a.UsageReportAPIName = st.serviceName
            WHERE (a.UsedForConnect = 'Y' OR a.UsedForAssure = 'Y')
            AND (COALESCE(APIUsage.TotalTxnCount, 0) > 0 OR COALESCE(PortalUsage.TotalTxnCount, 0) > 0)
            """

            cursor.execute(query, (period, period, period))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} digital service usage records for period {period}"
            )
            return results

        except Exception as e:
            logger.error(
                f"Error getting digital service usage for period {period}: {str(e)}"
            )
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPAPIAdoption(period):
        """Get RSP API adoption and utilization data for a specific period"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Query to get API adoption and utilization data
            query = """
            WITH APIUtilization AS (
                SELECT
                    a.APIName,
                    COUNT(DISTINCT duv.accessSeekerId) as UtilCount
                FROM APIs a
                LEFT JOIN DigitalUsageDetailView duv
                    ON a.UsageReportAPIName = duv.serviceName
                    AND duv.period = %s
                    AND duv.businessChannel = 'APIGWY'
                GROUP BY a.APIName
            )
            SELECT
                a.APIName,
                COUNT(DISTINCT asca.AccessSeekerId) as CertCount,
                COALESCE(au.UtilCount, 0) as UtilCount
            FROM APIs a
            LEFT JOIN AccessSeeker_CertifiedAPIs asca
                ON a.APIName = asca.CertifiedAPIName
            LEFT JOIN APIUtilization au
                ON a.APIName = au.APIName
            GROUP BY a.APIName, au.UtilCount
            HAVING CertCount > 0 OR UtilCount > 0
            ORDER BY CertCount DESC
            """

            cursor.execute(query, (period,))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP API adoption records for period {period}"
            )
            return results

        except Exception as e:
            logger.error(
                f"Error getting RSP API adoption for period {period}: {str(e)}"
            )
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPDigitalUsage(period):
        """Get RSP digital usage data for a specific period"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Query to get RSP usage data with additional metrics
            query = """
            WITH RSPTotals AS (
                SELECT
                    RSPName,
                    CAST(SUM(Total) as SIGNED) as TotalTxns,
                    ROW_NUMBER() OVER (ORDER BY SUM(Total) DESC) as DigitalVolRank,
                    ROUND(SUM(CASE WHEN businessChannel = 'APIGWY' THEN Total ELSE 0 END) * 100.0 / NULLIF(SUM(Total), 0), 1) as APIPercentage
                FROM DigitalUsageDetailView
                WHERE Period = %s
                GROUP BY RSPName
                HAVING TotalTxns > 0
            )
            SELECT
                rt.DigitalVolRank,
                rt.RSPName,
                COALESCE(rt.APIPercentage, 0) as APIPercentage,
                COALESCE(acs.ServiceCountSept24, 0) as TotalServices,
                CASE
                    WHEN COALESCE(acs.ServiceCountSept24, 0) > 0
                    THEN ROUND(CAST(rt.TotalTxns AS DECIMAL(10,2)) / NULLIF(acs.ServiceCountSept24, 0), 1)
                    ELSE 0
                END as TxnPerService,
                COALESCE(api.TotalAPITxns, 0) as TotalAPITxns,
                COALESCE(portal.TotalPortalTxns, 0) as TotalPortalTxns,
                rt.TotalTxns
            FROM RSPTotals rt
            LEFT JOIN (
                SELECT
                    RSPName,
                    CAST(SUM(Total) as SIGNED) as TotalAPITxns
                FROM DigitalUsageDetailView
                WHERE Period = %s
                AND businessChannel = 'APIGWY'
                GROUP BY RSPName
            ) api ON rt.RSPName = api.RSPName
            LEFT JOIN (
                SELECT
                    RSPName,
                    CAST(SUM(Total) as SIGNED) as TotalPortalTxns
                FROM DigitalUsageDetailView
                WHERE Period = %s
                AND businessChannel = 'ServicePortal'
                GROUP BY RSPName
            ) portal ON rt.RSPName = portal.RSPName
            LEFT JOIN AccessSeekers acs ON rt.RSPName = acs.RSPName
            ORDER BY rt.TotalTxns DESC
            """

            cursor.execute(query, (period, period, period))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP digital usage records for period {period}"
            )
            return results

        except Exception as e:
            logger.error(
                f"Error getting RSP digital usage for period {period}: {str(e)}"
            )
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPAPIPercentage(period):
        """Get RSP API percentage data for a specific period"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Query to get API percentage data with ranks
            query = """
            WITH RSPTotals AS (
                SELECT
                    RSPName,
                    SUM(CASE WHEN businessChannel = 'APIGWY' THEN Total ELSE 0 END) as APITxns,
                    SUM(Total) as TotalTxns,
                    RANK() OVER (ORDER BY SUM(Total) DESC) as DigitalVolRank
                FROM DigitalUsageDetailView
                WHERE Period = %s
                GROUP BY RSPName
                HAVING TotalTxns > 0
            ),
            ServiceCounts AS (
                SELECT
                    RSPName,
                    ServiceCountSept24,
                    RANK() OVER (ORDER BY ServiceCountSept24 DESC) as ServiceCountRank
                FROM AccessSeekers
            )
            SELECT
                rt.DigitalVolRank as DigitalVolRank,
                rt.RSPName as RSPName,
                ROUND(CAST(rt.APITxns AS DECIMAL(10,2)) * 100 / rt.TotalTxns, 1) as APIPercentage,
                sc.ServiceCountRank as ServiceCountRank
            FROM RSPTotals rt
            LEFT JOIN ServiceCounts sc ON rt.RSPName = sc.RSPName
            ORDER BY rt.DigitalVolRank ASC
            """

            cursor.execute(query, (period,))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP API percentage records for period {period}"
            )
            return results

        except Exception as e:
            logger.error(
                f"Error getting RSP API percentage for period {period}: {str(e)}"
            )
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GenerateAIInsights(digital_usage_data, comparison_data=None):
        """Generate AI insights using Ollama LLM with fallback to statistical analysis"""
        try:
            logger.info("Starting Ollama-based AI insights generation")

            if not digital_usage_data or len(digital_usage_data) == 0:
                logger.warning("No digital usage data provided for insights generation")
                return "No data available for analysis."

            # Initialize Ollama-based insight generator with configuration
            app_config = get_app_config()
            insight_generator = InsightGenerator(
                ollama_url=app_config.ollama_url, model=app_config.ollama_model
            )

            # Check if Ollama is available
            if insight_generator.check_ollama_availability():
                logger.info("Ollama service is available, using AI-powered insights")
                insights = insight_generator.generate_insights(
                    digital_usage_data, comparison_data
                )
                return insights
            else:
                logger.warning(
                    "Ollama service unavailable, falling back to statistical analysis"
                )
                return CioRspExecutiveBusSvc._generate_statistical_insights(
                    digital_usage_data, comparison_data
                )

        except Exception as e:
            logger.error(f"Error generating AI insights: {str(e)}")
            logger.info("Falling back to statistical analysis due to error")
            return CioRspExecutiveBusSvc._generate_statistical_insights(
                digital_usage_data, comparison_data
            )

    @staticmethod
    def _generate_statistical_insights(digital_usage_data, comparison_data=None):
        """Generate statistical insights as fallback when Ollama is unavailable"""
        try:
            logger.info("Generating statistical insights")

            insights = []

            # Convert data to proper format for analysis
            data_df = []
            for item in digital_usage_data:
                data_df.append(
                    {
                        "Period": item.get("Period", ""),
                        "TotalTxns": int(item.get("TotalTxns", 0)),
                        "TotalAPITxns": int(item.get("TotalAPITxns", 0)),
                        "TotalPortalTxns": int(item.get("TotalPortalTxns", 0)),
                    }
                )

            # Sort data by period for trend analysis
            data_df.sort(key=lambda x: x["Period"])

            # Generate volume trend insights
            if len(data_df) >= 2:
                latest = data_df[-1]
                previous = data_df[-2]

                # Calculate growth rates
                total_growth = (
                    (
                        (latest["TotalTxns"] - previous["TotalTxns"])
                        / previous["TotalTxns"]
                    )
                    * 100
                    if previous["TotalTxns"] > 0
                    else 0
                )
                api_growth = (
                    (
                        (latest["TotalAPITxns"] - previous["TotalAPITxns"])
                        / previous["TotalAPITxns"]
                    )
                    * 100
                    if previous["TotalAPITxns"] > 0
                    else 0
                )
                portal_growth = (
                    (
                        (latest["TotalPortalTxns"] - previous["TotalPortalTxns"])
                        / previous["TotalPortalTxns"]
                    )
                    * 100
                    if previous["TotalPortalTxns"] > 0
                    else 0
                )

                insights.append(
                    f"📈 **Volume Trends ({previous['Period']} to {latest['Period']})**"
                )

                if abs(total_growth) > 5:
                    direction = "increased" if total_growth > 0 else "decreased"
                    insights.append(
                        f"• Total transactions {direction} by {abs(total_growth):.1f}% ({previous['TotalTxns']:,} → {latest['TotalTxns']:,})"
                    )

                if abs(api_growth) > 5:
                    direction = "increased" if api_growth > 0 else "decreased"
                    insights.append(
                        f"• API transactions {direction} by {abs(api_growth):.1f}% ({previous['TotalAPITxns']:,} → {latest['TotalAPITxns']:,})"
                    )

                if abs(portal_growth) > 5:
                    direction = "increased" if portal_growth > 0 else "decreased"
                    insights.append(
                        f"• Portal transactions {direction} by {abs(portal_growth):.1f}% ({previous['TotalPortalTxns']:,} → {latest['TotalPortalTxns']:,})"
                    )

                # Channel mix analysis
                latest_api_pct = (
                    (latest["TotalAPITxns"] / latest["TotalTxns"]) * 100
                    if latest["TotalTxns"] > 0
                    else 0
                )
                previous_api_pct = (
                    (previous["TotalAPITxns"] / previous["TotalTxns"]) * 100
                    if previous["TotalTxns"] > 0
                    else 0
                )
                mix_shift = latest_api_pct - previous_api_pct

                insights.append(f"\n🔄 **Channel Mix Analysis**")
                insights.append(
                    f"• Current API usage: {latest_api_pct:.1f}% of total transactions"
                )

                if abs(mix_shift) > 2:
                    direction = "towards API" if mix_shift > 0 else "towards Portal"
                    insights.append(
                        f"• Channel mix shifted {abs(mix_shift):.1f}% {direction}"
                    )

            # Generate long-term trend insights if enough data
            if len(data_df) >= 6:
                insights.append(f"\n📊 **Long-term Trends (6-month analysis)**")

                # Calculate 6-month growth
                six_months_ago = data_df[-6]
                latest = data_df[-1]

                long_term_growth = (
                    (
                        (latest["TotalTxns"] - six_months_ago["TotalTxns"])
                        / six_months_ago["TotalTxns"]
                    )
                    * 100
                    if six_months_ago["TotalTxns"] > 0
                    else 0
                )

                if abs(long_term_growth) > 10:
                    direction = "growth" if long_term_growth > 0 else "decline"
                    insights.append(
                        f"• Strong {direction} trend: {abs(long_term_growth):.1f}% over 6 months"
                    )

                # Calculate average monthly growth rate
                monthly_growth_rates = []
                for i in range(1, len(data_df)):
                    if data_df[i - 1]["TotalTxns"] > 0:
                        growth = (
                            (data_df[i]["TotalTxns"] - data_df[i - 1]["TotalTxns"])
                            / data_df[i - 1]["TotalTxns"]
                        ) * 100
                        monthly_growth_rates.append(growth)

                if monthly_growth_rates:
                    avg_monthly_growth = sum(monthly_growth_rates) / len(
                        monthly_growth_rates
                    )
                    if abs(avg_monthly_growth) > 2:
                        direction = "growing" if avg_monthly_growth > 0 else "declining"
                        insights.append(
                            f"• Average monthly {direction} rate: {abs(avg_monthly_growth):.1f}%"
                        )

            # Add comparison insights if provided
            if comparison_data:
                insights.append(f"\n🔍 **Comparative Analysis**")

                if "totalPctChange" in comparison_data:
                    total_change = comparison_data["totalPctChange"]
                    if abs(total_change) > 5:
                        direction = "increased" if total_change > 0 else "decreased"
                        insights.append(
                            f"• Total volume {direction} by {abs(total_change):.1f}% between selected periods"
                        )

                if (
                    "apiPctChange" in comparison_data
                    and "portalPctChange" in comparison_data
                ):
                    api_change = comparison_data["apiPctChange"]
                    portal_change = comparison_data["portalPctChange"]

                    if abs(api_change - portal_change) > 10:
                        better_channel = (
                            "API" if api_change > portal_change else "Portal"
                        )
                        worse_channel = (
                            "Portal" if api_change > portal_change else "API"
                        )
                        diff = abs(api_change - portal_change)
                        insights.append(
                            f"• {better_channel} outperformed {worse_channel} by {diff:.1f} percentage points"
                        )

            # Generate recommendations
            insights.append(f"\n💡 **Key Recommendations**")

            if len(data_df) >= 2:
                latest = data_df[-1]
                api_percentage = (
                    (latest["TotalAPITxns"] / latest["TotalTxns"]) * 100
                    if latest["TotalTxns"] > 0
                    else 0
                )

                if api_percentage < 50:
                    insights.append(
                        f"• Consider promoting API adoption - currently at {api_percentage:.1f}%"
                    )
                elif api_percentage > 80:
                    insights.append(
                        f"• Excellent API adoption rate of {api_percentage:.1f}% - maintain current strategies"
                    )

                # Volume-based recommendations
                if latest["TotalTxns"] > 100000:
                    insights.append(
                        f"• High transaction volume ({latest['TotalTxns']:,}) - monitor performance and scalability"
                    )
                elif latest["TotalTxns"] < 10000:
                    insights.append(
                        f"• Low transaction volume ({latest['TotalTxns']:,}) - focus on user engagement and adoption"
                    )

            # Join all insights
            result = "\n".join(insights)

            logger.info(f"Successfully generated {len(insights)} insights")
            return result

        except Exception as e:
            logger.error(f"Error generating AI insights: {str(e)}")
            return f"Error generating insights: {str(e)}"
