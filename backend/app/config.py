from typing import Annotated, Any

from pydantic import Field, SkipValidation
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppConfig(BaseSettings):
    model_config: SettingsConfigDict = SettingsConfigDict(
        frozen=True, env_file=".env.local", env_file_encoding="utf-8"
    )

    cors_origins: str
    logging_config_path: str

    db_host: str
    db_port: int
    db_user: str
    db_password: str
    db_name: str

    # Ollama configuration
    ollama_url: str = "http://localhost:11434"
    ollama_model: str = "llama3.2"

    # Authentication configuration
    auth_enabled: bool = True
    jwt_secret_key: str = "your-secret-key-change-in-production"
    jwt_expiration_hours: int = 8

    # LDAP configuration (only used when auth_enabled=True)
    ldap_host: str = "SVEDC2000004PR.nbnco.local"
    ldap_base_dn: str = "DC=nbnco,DC=local"


def get_app_config() -> AppConfig:
    return AppConfig()
