import json
import logging
from datetime import datetime, timedelta
from urllib.parse import unquote

from auth_middleware import get_current_user
from config import get_app_config
from DBHandler import get_connection
from insight_generator import InsightGenerator

logger = logging.getLogger(__name__)


def GetLoggedInUser(request=None):
    """Get the current logged-in user from authentication context"""
    try:
        user = get_current_user()
        return user if user else "unknown"
    except Exception as e:
        logger.warning(f"Could not get logged-in user: {str(e)}")
        return "unknown"


def convert_date_format(date_input):
    """Convert various date formats to MySQL DATE format (YYYY-MM-DD)"""
    if not date_input:
        return None

    try:
        # Handle different input types
        if hasattr(date_input, "strftime"):
            # It's already a date/datetime object
            return date_input.strftime("%Y-%m-%d")

        # Convert to string if not already
        date_string = str(date_input)

        # URL decode if needed
        decoded_date = unquote(date_string)
        logger.info(f"Converting date: {date_string} -> {decoded_date}")

        # Check if it's already in YYYY-MM-DD format
        if len(decoded_date) == 10 and decoded_date.count("-") == 2:
            try:
                # Validate it's a proper date
                datetime.strptime(decoded_date, "%Y-%m-%d")
                logger.info(f"Date already in correct format: {decoded_date}")
                return decoded_date
            except ValueError:
                pass

        # Try to parse different date formats
        date_formats = [
            "%Y-%m-%d",  # Already in correct format
            "%a, %d %b %Y %H:%M:%S %Z",  # Sun, 03 Aug 2025 00:00:00 GMT
            "%a, %d %b %Y %H:%M:%S",  # Sun, 03 Aug 2025 00:00:00
            "%Y-%m-%dT%H:%M:%S.%fZ",  # ISO format with microseconds
            "%Y-%m-%dT%H:%M:%SZ",  # ISO format
            "%Y-%m-%d %H:%M:%S",  # MySQL datetime format
        ]

        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(decoded_date, fmt)
                mysql_date = parsed_date.strftime("%Y-%m-%d")
                logger.info(f"Successfully converted {decoded_date} to {mysql_date}")
                return mysql_date
            except ValueError:
                continue

        # If no format worked, log error and return None
        logger.error(f"Could not parse date format: {decoded_date}")
        return None

    except Exception as e:
        logger.error(f"Error converting date {date_input}: {str(e)}")
        return None


class RSPPlatformsWeeklyBusSvc:

    @staticmethod
    def GetRSPPlatformsWeeklyHistory(start_week=None, end_week=None):
        """Get RSP platforms weekly history data for trends analysis"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            # Base query for weekly platform metrics
            query = """
            SELECT 
                WeekEnding,
                WeekNumber,
                Year,
                PlatformServiceType,
                CurrentWeekTarget,
                CurrentWeekActual,
                LastWeekTarget,
                LastWeekActual,
                LastMonthTarget,
                LastMonthActual,
                RollingAverage,
                ChangeFromLastWeek,
                IncidentCount,
                ServiceAvailability,
                APITransactionSuccess,
                PortalAvailability,
                Status,
                Notes
            FROM RSPPlatformsWeekly
            WHERE Status = 'Active'
            """

            # Add date range filtering if provided
            params = []
            if start_week and end_week:
                # Convert date formats to MySQL format
                start_date = convert_date_format(start_week)
                end_date = convert_date_format(end_week)

                if start_date and end_date:
                    query += " AND WeekEnding BETWEEN %s AND %s"
                    params.extend([start_date, end_date])
                    logger.info(
                        f"Applying date range filter: {start_date} to {end_date}"
                    )
                else:
                    logger.warning(
                        f"Could not convert date range: {start_week} to {end_week}"
                    )

            query += " ORDER BY WeekEnding DESC, PlatformServiceType"

            cursor.execute(query, params)
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP platforms weekly history records"
            )
            return results

        except Exception as e:
            logger.error(f"Error getting RSP platforms weekly history: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPPlatformsWeeklyPeriods():
        """Get available weeks for RSP platforms weekly data"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            query = """
            SELECT DISTINCT 
                WeekEnding,
                CONCAT('Week ', WeekNumber, ', ', Year) as WeekLabel,
                WeekNumber,
                Year
            FROM RSPPlatformsWeekly
            WHERE Status = 'Active'
            ORDER BY WeekEnding DESC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            logger.info(f"Retrieved {len(results)} available weeks")
            return results

        except Exception as e:
            logger.error(f"Error getting RSP platforms weekly periods: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPPlatformsWeeklyByWeek(week_ending):
        """Get RSP platforms weekly data for a specific week"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            query = """
            SELECT 
                WeekEnding,
                WeekNumber,
                Year,
                PlatformServiceType,
                CurrentWeekTarget,
                CurrentWeekActual,
                LastWeekTarget,
                LastWeekActual,
                LastMonthTarget,
                LastMonthActual,
                RollingAverage,
                ChangeFromLastWeek,
                IncidentCount,
                ServiceAvailability,
                APITransactionSuccess,
                PortalAvailability,
                Status,
                Notes
            FROM RSPPlatformsWeekly
            WHERE WeekEnding = %s AND Status = 'Active'
            ORDER BY PlatformServiceType
            """

            # Convert date format to MySQL format
            converted_week = convert_date_format(week_ending)
            if not converted_week:
                logger.error(f"Could not convert week ending date: {week_ending}")
                return []

            cursor.execute(query, (converted_week,))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP platforms weekly records for week {converted_week}"
            )
            return results

        except Exception as e:
            logger.error(
                f"Error getting RSP platforms weekly data for week {week_ending}: {str(e)}"
            )
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GetRSPPlatformsWeeklyTrends(weeks_back=12):
        """Get RSP platforms weekly trends for the last N weeks"""
        try:
            conn = get_connection()
            if conn is None:
                logger.error("Database connection failed")
                return None

            cursor = conn.cursor()

            query = """
            SELECT 
                WeekEnding,
                PlatformServiceType,
                CurrentWeekActual as ActualValue,
                CurrentWeekTarget as TargetValue,
                IncidentCount,
                ServiceAvailability
            FROM RSPPlatformsWeekly
            WHERE Status = 'Active'
            AND WeekEnding >= DATE_SUB(CURDATE(), INTERVAL %s WEEK)
            ORDER BY WeekEnding DESC, PlatformServiceType
            """

            cursor.execute(query, (weeks_back,))
            results = cursor.fetchall()

            logger.info(
                f"Retrieved {len(results)} RSP platforms weekly trend records for last {weeks_back} weeks"
            )
            return results

        except Exception as e:
            logger.error(f"Error getting RSP platforms weekly trends: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()

    @staticmethod
    def GenerateAIInsights(platforms_data, comparison_data=None):
        """Generate AI insights for RSP platforms weekly data"""
        try:
            logger.info("Generating AI insights for RSP platforms weekly data")

            insight_generator = InsightGenerator()

            # Check if Ollama is available
            if insight_generator.check_ollama_availability():
                logger.info("Ollama service is available, using AI-powered insights")
                insights = insight_generator.generate_platforms_insights(
                    platforms_data, comparison_data
                )
                return insights
            else:
                logger.warning(
                    "Ollama service unavailable, falling back to statistical analysis"
                )
                return RSPPlatformsWeeklyBusSvc._generate_statistical_insights(
                    platforms_data, comparison_data
                )

        except Exception as e:
            logger.error(f"Error generating AI insights: {str(e)}")
            logger.info("Falling back to statistical analysis due to error")
            return RSPPlatformsWeeklyBusSvc._generate_statistical_insights(
                platforms_data, comparison_data
            )

    @staticmethod
    def _generate_statistical_insights(platforms_data, comparison_data=None):
        """Generate statistical insights when AI is unavailable"""
        try:
            if not platforms_data:
                return "No platform data available for analysis."

            insights = []
            insights.append("## RSP Platforms Weekly Statistical Analysis")
            insights.append("")

            # Calculate basic statistics
            total_platforms = len(
                set(item.get("PlatformServiceType", "") for item in platforms_data)
            )
            avg_availability = sum(
                float(item.get("CurrentWeekActual", 0)) for item in platforms_data
            ) / len(platforms_data)
            total_incidents = sum(
                int(item.get("IncidentCount", 0)) for item in platforms_data
            )

            insights.append(f"**Platform Overview:**")
            insights.append(f"- Total Platform Types: {total_platforms}")
            insights.append(f"- Average Availability: {avg_availability:.2f}%")
            insights.append(f"- Total Incidents: {total_incidents}")
            insights.append("")

            # Identify best and worst performing platforms
            sorted_platforms = sorted(
                platforms_data,
                key=lambda x: float(x.get("CurrentWeekActual", 0)),
                reverse=True,
            )
            if sorted_platforms:
                best_platform = sorted_platforms[0]
                worst_platform = sorted_platforms[-1]

                insights.append(f"**Performance Highlights:**")
                insights.append(
                    f"- Best Performing: {best_platform.get('PlatformServiceType', 'Unknown')} ({best_platform.get('CurrentWeekActual', 0)}%)"
                )
                insights.append(
                    f"- Needs Attention: {worst_platform.get('PlatformServiceType', 'Unknown')} ({worst_platform.get('CurrentWeekActual', 0)}%)"
                )
                insights.append("")

            insights.append("**Recommendations:**")
            insights.append("- Monitor platforms with availability below 99%")
            insights.append("- Investigate root causes of incidents")
            insights.append("- Implement proactive monitoring for critical services")

            return "\n".join(insights)

        except Exception as e:
            logger.error(f"Error generating statistical insights: {str(e)}")
            return "Unable to generate statistical insights due to an error."


# Standard CRUD operations following established patterns
def GetRSPPlatformsWeeklys(request):
    """Get list of RSP platforms weekly records with AG Grid support"""
    try:
        from AGGridServices import (
            generateLimitOffsetSQL,
            generateOrderBySQL,
            generateWhereSQL,
        )

        payload = request.json if request.json else {}

        conn = get_connection()
        if conn is None:
            logger.error("Database connection failed")
            return {"error": "Database connection failed"}

        with conn.cursor() as cursor:
            sql = """
            SELECT SQL_CALC_FOUND_ROWS 
                id, WeekEnding, WeekNumber, Year, PlatformServiceType,
                CurrentWeekTarget, CurrentWeekActual, LastWeekTarget, LastWeekActual,
                LastMonthTarget, LastMonthActual, RollingAverage, ChangeFromLastWeek,
                IncidentCount, ServiceAvailability, APITransactionSuccess, PortalAvailability,
                Status, Notes, created, created_by, modified, modified_by
            FROM RSPPlatformsWeekly
            WHERE 1=1
            """

            # Add AG Grid filtering, sorting, and pagination
            sql += generateWhereSQL(payload)
            sql += generateOrderBySQL(payload)
            sql += generateLimitOffsetSQL(payload)

            cursor.execute(sql)
            records = cursor.fetchall()

            # Get total count for pagination
            cursor.execute("SELECT FOUND_ROWS() as totalRecords")
            total_records = cursor.fetchone()["totalRecords"]

        logger.info(f"Retrieved {len(records)} RSP platforms weekly records")
        return {"totalRecords": total_records, "records": records}

    except Exception as e:
        logger.error(f"Error getting RSP platforms weekly records: {str(e)}")
        return {"error": "Internal server error"}
    finally:
        if conn:
            conn.close()


def GetRSPPlatformsWeekly(record_id):
    """Get a single RSP platforms weekly record by ID"""
    try:
        conn = get_connection()
        if conn is None:
            logger.error("Database connection failed")
            return None

        with conn.cursor() as cursor:
            sql = """
            SELECT id, WeekEnding, WeekNumber, Year, PlatformServiceType,
                   CurrentWeekTarget, CurrentWeekActual, LastWeekTarget, LastWeekActual,
                   LastMonthTarget, LastMonthActual, RollingAverage, ChangeFromLastWeek,
                   IncidentCount, ServiceAvailability, APITransactionSuccess, PortalAvailability,
                   Status, Notes, created, created_by, modified, modified_by
            FROM RSPPlatformsWeekly 
            WHERE id = %s
            """
            cursor.execute(sql, (record_id,))
            record = cursor.fetchone()

        logger.info(f"Retrieved RSP platforms weekly record with ID: {record_id}")
        return record

    except Exception as e:
        logger.error(f"Error getting RSP platforms weekly record {record_id}: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()


def AddRSPPlatformsWeekly(payload):
    """Add a new RSP platforms weekly record"""
    try:
        logged_in_user = GetLoggedInUser()

        conn = get_connection()
        if conn is None:
            logger.error("Database connection failed")
            return None

        with conn.cursor() as cursor:
            sql = """
            INSERT INTO RSPPlatformsWeekly (
                WeekEnding, WeekNumber, Year, PlatformServiceType,
                CurrentWeekTarget, CurrentWeekActual, LastWeekTarget, LastWeekActual,
                LastMonthTarget, LastMonthActual, RollingAverage, ChangeFromLastWeek,
                IncidentCount, ServiceAvailability, APITransactionSuccess, PortalAvailability,
                Status, Notes, created, created_by
            ) VALUES (
                %(WeekEnding)s, %(WeekNumber)s, %(Year)s, %(PlatformServiceType)s,
                %(CurrentWeekTarget)s, %(CurrentWeekActual)s, %(LastWeekTarget)s, %(LastWeekActual)s,
                %(LastMonthTarget)s, %(LastMonthActual)s, %(RollingAverage)s, %(ChangeFromLastWeek)s,
                %(IncidentCount)s, %(ServiceAvailability)s, %(APITransactionSuccess)s, %(PortalAvailability)s,
                %(Status)s, %(Notes)s, NOW(), %(created_by)s
            )
            """

            # Add user context
            payload["created_by"] = logged_in_user

            cursor.execute(sql, payload)
            new_id = cursor.lastrowid

        # Return the newly created record
        new_record = GetRSPPlatformsWeekly(new_id)
        logger.info(f"Added new RSP platforms weekly record with ID: {new_id}")
        return new_record

    except Exception as e:
        logger.error(f"Error adding RSP platforms weekly record: {str(e)}")
        return None
    finally:
        if conn:
            conn.close()


def UpdateRSPPlatformsWeekly(record_id, payload):
    """Update an existing RSP platforms weekly record"""
    try:
        logged_in_user = GetLoggedInUser()

        conn = get_connection()
        if conn is None:
            logger.error("Database connection failed")
            return None

        with conn.cursor() as cursor:
            # Build dynamic update query based on provided fields
            update_fields = []
            update_values = {}

            updatable_fields = [
                "WeekEnding",
                "WeekNumber",
                "Year",
                "PlatformServiceType",
                "CurrentWeekTarget",
                "CurrentWeekActual",
                "LastWeekTarget",
                "LastWeekActual",
                "LastMonthTarget",
                "LastMonthActual",
                "RollingAverage",
                "ChangeFromLastWeek",
                "IncidentCount",
                "ServiceAvailability",
                "APITransactionSuccess",
                "PortalAvailability",
                "Status",
                "Notes",
            ]

            for field in updatable_fields:
                if field in payload:
                    update_fields.append(f"{field} = %({field})s")
                    update_values[field] = payload[field]

            if not update_fields:
                logger.warning("No valid fields to update")
                return None

            # Add audit fields
            update_fields.extend(["modified = NOW()", "modified_by = %(modified_by)s"])
            update_values["modified_by"] = logged_in_user
            update_values["id"] = record_id

            sql = f"""
            UPDATE RSPPlatformsWeekly 
            SET {', '.join(update_fields)}
            WHERE id = %(id)s
            """

            cursor.execute(sql, update_values)

            if cursor.rowcount == 0:
                logger.warning(
                    f"No RSP platforms weekly record found with ID: {record_id}"
                )
                return None

        # Return the updated record
        updated_record = GetRSPPlatformsWeekly(record_id)
        logger.info(f"Updated RSP platforms weekly record with ID: {record_id}")
        return updated_record

    except Exception as e:
        logger.error(
            f"Error updating RSP platforms weekly record {record_id}: {str(e)}"
        )
        return None
    finally:
        if conn:
            conn.close()
