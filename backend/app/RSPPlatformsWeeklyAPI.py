import logging

from flask import Blueprint, jsonify, request
from RSPPlatformsWeeklyBusSvc import (
    AddRSPPlatformsWeekly,
    GetRSPPlatformsWeekly,
    GetRSPPlatformsWeeklys,
    RSPPlatformsWeeklyBusSvc,
    UpdateRSPPlatformsWeekly,
)

logger = logging.getLogger(__name__)

# Create Blueprint for RSP Platforms Weekly API
rsp_platforms_weekly_bp = Blueprint("rsp_platforms_weekly", __name__)


@rsp_platforms_weekly_bp.route("/RSPPlatformsWeekly/History", methods=["GET"])
def get_rsp_platforms_weekly_history():
    """Get RSP platforms weekly history data for trends analysis"""
    try:
        # Get optional date range parameters
        start_week = request.args.get("startWeek")
        end_week = request.args.get("endWeek")

        logger.info(
            f"Getting RSP platforms weekly history with date range: {start_week} to {end_week}"
        )

        data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyHistory(
            start_week, end_week
        )

        if data is None:
            logger.error("Failed to retrieve RSP platforms weekly history")
            return (
                jsonify({"error": "Failed to retrieve RSP platforms weekly history"}),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP platforms weekly history records"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_platforms_weekly_history: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route("/RSPPlatformsWeekly/Periods", methods=["GET"])
def get_rsp_platforms_weekly_periods():
    """Get available weeks for RSP platforms weekly data"""
    try:
        logger.info("Getting RSP platforms weekly periods")

        data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyPeriods()

        if data is None:
            logger.error("Failed to retrieve RSP platforms weekly periods")
            return (
                jsonify({"error": "Failed to retrieve RSP platforms weekly periods"}),
                500,
            )

        logger.info(f"Successfully retrieved {len(data)} RSP platforms weekly periods")
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_platforms_weekly_periods: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route(
    "/RSPPlatformsWeekly/ByWeek/<week_ending>", methods=["GET"]
)
def get_rsp_platforms_weekly_by_week(week_ending):
    """Get RSP platforms weekly data for a specific week"""
    try:
        logger.info(f"Getting RSP platforms weekly data for week: {week_ending}")

        data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyByWeek(week_ending)

        if data is None:
            logger.error(
                f"Failed to retrieve RSP platforms weekly data for week {week_ending}"
            )
            return (
                jsonify(
                    {
                        "error": f"Failed to retrieve RSP platforms weekly data for week {week_ending}"
                    }
                ),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP platforms weekly records for week {week_ending}"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(
            f"Error in get_rsp_platforms_weekly_by_week for week {week_ending}: {str(e)}"
        )
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route("/RSPPlatformsWeekly/Trends", methods=["GET"])
def get_rsp_platforms_weekly_trends():
    """Get RSP platforms weekly trends for the last N weeks"""
    try:
        # Get optional weeks_back parameter (default to 12 weeks)
        weeks_back = request.args.get("weeksBack", 12, type=int)

        logger.info(f"Getting RSP platforms weekly trends for last {weeks_back} weeks")

        data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyTrends(weeks_back)

        if data is None:
            logger.error("Failed to retrieve RSP platforms weekly trends")
            return (
                jsonify({"error": "Failed to retrieve RSP platforms weekly trends"}),
                500,
            )

        logger.info(
            f"Successfully retrieved {len(data)} RSP platforms weekly trend records"
        )
        return jsonify({"data": data}), 200

    except Exception as e:
        logger.error(f"Error in get_rsp_platforms_weekly_trends: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route("/RSPPlatformsWeekly/GenerateInsights", methods=["POST"])
def generate_insights():
    """Generate AI insights for RSP platforms weekly data"""
    try:
        logger.info("Generating AI insights for RSP platforms weekly data")

        # Get the request data
        request_data = request.get_json()
        if not request_data:
            return jsonify({"error": "No data provided"}), 400

        platforms_data = request_data.get("platformsData", [])
        comparison_data = request_data.get("comparisonData")

        if not platforms_data:
            return jsonify({"error": "No platforms data provided"}), 400

        # Generate insights using the business service
        insights = RSPPlatformsWeeklyBusSvc.GenerateAIInsights(
            platforms_data, comparison_data
        )

        if not insights:
            logger.error("Failed to generate insights")
            return jsonify({"error": "Failed to generate insights"}), 500

        logger.info("Successfully generated AI insights for RSP platforms weekly data")
        return jsonify({"insights": insights}), 200

    except Exception as e:
        logger.error(f"Error in generate_insights: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route(
    "/RSPPlatformsWeekly/GenerateStatisticalAnalysis", methods=["POST"]
)
def generate_statistical_analysis():
    """Generate statistical analysis for RSP platforms weekly data"""
    try:
        logger.info("Generating statistical analysis for RSP platforms weekly data")

        # Get the request data
        request_data = request.get_json()
        if not request_data:
            return jsonify({"error": "No data provided"}), 400

        platforms_data = request_data.get("platformsData", [])
        comparison_data = request_data.get("comparisonData")

        if not platforms_data:
            return jsonify({"error": "No platforms data provided"}), 400

        # Generate statistical analysis using the business service
        analysis = RSPPlatformsWeeklyBusSvc._generate_statistical_insights(
            platforms_data, comparison_data
        )

        if not analysis:
            logger.error("Failed to generate statistical analysis")
            return jsonify({"error": "Failed to generate statistical analysis"}), 500

        logger.info(
            "Successfully generated statistical analysis for RSP platforms weekly data"
        )
        return jsonify({"analysis": analysis}), 200

    except Exception as e:
        logger.error(f"Error in generate_statistical_analysis: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# Standard CRUD API endpoints following /api/v1/ pattern
@rsp_platforms_weekly_bp.route("/api/v1/rsp-platforms-weekly/list", methods=["POST"])
def get_rsp_platforms_weekly_list():
    """Get list of RSP platforms weekly records with filtering, sorting, and pagination"""
    try:
        logger.info("Getting RSP platforms weekly list")
        result = GetRSPPlatformsWeeklys(request)

        if "error" in result:
            return jsonify(result), 500

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error in get_rsp_platforms_weekly_list: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route(
    "/api/v1/rsp-platforms-weekly/<record_id>", methods=["GET"]
)
def get_rsp_platforms_weekly_record(record_id):
    """Get a single RSP platforms weekly record by ID"""
    try:
        record = GetRSPPlatformsWeekly(record_id)
        if record:
            return jsonify({"data": record})
        else:
            return (
                jsonify(
                    {
                        "error": f"RSP platforms weekly record with ID {record_id} not found"
                    }
                ),
                404,
            )
    except Exception as e:
        logger.error(f"Error in get_rsp_platforms_weekly_record: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route("/api/v1/rsp-platforms-weekly", methods=["POST"])
def create_rsp_platforms_weekly_record():
    """Create a new RSP platforms weekly record"""
    try:
        record_data = request.json
        if not record_data:
            return jsonify({"error": "No data provided"}), 400

        added_record = AddRSPPlatformsWeekly(record_data)
        if added_record:
            return jsonify({"data": added_record}), 201
        else:
            return (
                jsonify({"error": "Failed to create RSP platforms weekly record"}),
                500,
            )
    except Exception as e:
        logger.error(f"Error in create_rsp_platforms_weekly_record: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route(
    "/api/v1/rsp-platforms-weekly/<record_id>", methods=["PATCH"]
)
def update_rsp_platforms_weekly_record(record_id):
    """Update an existing RSP platforms weekly record"""
    try:
        patch_values = request.json
        if not patch_values:
            return jsonify({"error": "No data provided"}), 400

        updated_record = UpdateRSPPlatformsWeekly(record_id, patch_values)
        if updated_record:
            return jsonify({"data": updated_record})
        else:
            return (
                jsonify(
                    {
                        "error": f"RSP platforms weekly record with ID {record_id} not found"
                    }
                ),
                404,
            )
    except Exception as e:
        logger.error(f"Error in update_rsp_platforms_weekly_record: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


# Additional analytics endpoints
@rsp_platforms_weekly_bp.route(
    "/api/v1/rsp-platforms-weekly/analytics/summary", methods=["GET"]
)
def get_platforms_summary():
    """Get summary analytics for RSP platforms weekly data"""
    try:
        # Get optional week parameter
        week_ending = request.args.get("weekEnding")

        if week_ending:
            data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyByWeek(week_ending)
        else:
            # Get latest week data
            periods = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyPeriods()
            if periods and len(periods) > 0:
                latest_week = periods[0]["WeekEnding"]
                data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyByWeek(latest_week)
            else:
                data = []

        if data is None:
            return jsonify({"error": "Failed to retrieve platforms summary"}), 500

        # Calculate summary metrics
        summary = {
            "totalPlatforms": len(data),
            "averageAvailability": (
                sum(float(item.get("CurrentWeekActual", 0)) for item in data)
                / len(data)
                if data
                else 0
            ),
            "totalIncidents": sum(int(item.get("IncidentCount", 0)) for item in data),
            "platformsAboveTarget": len(
                [
                    item
                    for item in data
                    if float(item.get("CurrentWeekActual", 0))
                    >= float(item.get("CurrentWeekTarget", 0))
                ]
            ),
            "platformsBelowTarget": len(
                [
                    item
                    for item in data
                    if float(item.get("CurrentWeekActual", 0))
                    < float(item.get("CurrentWeekTarget", 0))
                ]
            ),
        }

        return jsonify({"data": summary}), 200

    except Exception as e:
        logger.error(f"Error in get_platforms_summary: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500


@rsp_platforms_weekly_bp.route(
    "/api/v1/rsp-platforms-weekly/analytics/performance", methods=["GET"]
)
def get_platforms_performance():
    """Get performance analytics for RSP platforms weekly data"""
    try:
        # Get optional parameters
        weeks_back = request.args.get("weeksBack", 4, type=int)

        data = RSPPlatformsWeeklyBusSvc.GetRSPPlatformsWeeklyTrends(weeks_back)

        if data is None:
            return jsonify({"error": "Failed to retrieve platforms performance"}), 500

        # Group data by platform type for trend analysis
        platform_trends = {}
        for item in data:
            platform_type = item.get("PlatformServiceType", "Unknown")
            if platform_type not in platform_trends:
                platform_trends[platform_type] = []
            platform_trends[platform_type].append(
                {
                    "WeekEnding": item.get("WeekEnding"),
                    "ActualValue": item.get("ActualValue"),
                    "TargetValue": item.get("TargetValue"),
                    "IncidentCount": item.get("IncidentCount"),
                    "ServiceAvailability": item.get("ServiceAvailability"),
                }
            )

        return jsonify({"data": platform_trends}), 200

    except Exception as e:
        logger.error(f"Error in get_platforms_performance: {str(e)}")
        return jsonify({"error": "Internal server error"}), 500
