"""
Authentication Middleware for RSPi Application

This module provides JWT-based authentication middleware with environment-based behavior:
- Development mode: Authentication bypass with default user
- Live mode: JWT token validation with LDAP integration

The middleware integrates seamlessly with existing Flask blueprints and business services.
"""

import logging
from datetime import datetime, timedelta
from functools import wraps
from typing import Dict, Optional

import jwt
from flask import g, jsonify, request

from config import get_app_config

logger = logging.getLogger(__name__)


def require_auth(f):
    """
    Decorator to require authentication for Flask routes.
    
    Behavior depends on AUTH_ENABLED environment variable:
    - If False (dev mode): Sets default development user in g.current_user
    - If True (live mode): Validates JWT token and extracts user information
    
    Usage:
        @app.route('/protected')
        @require_auth
        def protected_route():
            user = get_current_user()
            return f"Hello {user['username']}"
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        config = get_app_config()
        
        if not config.auth_enabled:
            # Development mode - bypass authentication
            logger.debug("Authentication bypassed (development mode)")
            g.current_user = {
                "username": "dev-user",
                "displayName": "Development User",
                "email": "<EMAIL>",
                "groups": []
            }
            return f(*args, **kwargs)
        
        # Live mode - validate JWT token
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            logger.warning("Missing or invalid Authorization header")
            return jsonify({"error": "Authentication required"}), 401
        
        try:
            # Extract token from "Bearer <token>" format
            token = auth_header.split(" ")[1]
            
            # Decode and validate JWT token
            payload = jwt.decode(
                token, 
                config.jwt_secret_key, 
                algorithms=["HS256"]
            )
            
            # Set current user context
            g.current_user = {
                "username": payload.get("username"),
                "displayName": payload.get("displayName"),
                "email": payload.get("email"),
                "groups": payload.get("groups", [])
            }
            
            logger.debug(f"JWT authentication successful for user: {payload.get('username')}")
            return f(*args, **kwargs)
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            return jsonify({"error": "Token expired"}), 401
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {str(e)}")
            return jsonify({"error": "Invalid token"}), 401
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return jsonify({"error": "Authentication failed"}), 401
    
    return decorated_function


def get_current_user() -> Dict[str, any]:
    """
    Get the current authenticated user from Flask's g context.
    
    Returns:
        Dict containing user information:
        {
            "username": str,
            "displayName": str,
            "email": str,
            "groups": List[str]
        }
        
        Returns default user info if no user is set in context.
    """
    return getattr(g, 'current_user', {
        "username": "unknown",
        "displayName": "Unknown User",
        "email": "<EMAIL>",
        "groups": []
    })


def generate_jwt_token(user_info: Dict[str, any]) -> str:
    """
    Generate a JWT token for authenticated user.
    
    Args:
        user_info (Dict): User information from LDAP authentication
        
    Returns:
        str: JWT token string
        
    Raises:
        Exception: If token generation fails
    """
    try:
        config = get_app_config()
        
        # Create JWT payload
        payload = {
            "username": user_info.get("username"),
            "displayName": user_info.get("displayName"),
            "email": user_info.get("mail", user_info.get("email")),
            "groups": user_info.get("groups", []),
            "iat": datetime.utcnow(),
            "exp": datetime.utcnow() + timedelta(hours=config.jwt_expiration_hours)
        }
        
        # Generate JWT token
        token = jwt.encode(payload, config.jwt_secret_key, algorithm="HS256")
        
        logger.info(f"JWT token generated for user: {user_info.get('username')}")
        return token
        
    except Exception as e:
        logger.error(f"JWT token generation failed: {str(e)}")
        raise


def validate_jwt_token(token: str) -> Optional[Dict[str, any]]:
    """
    Validate a JWT token and return user information.
    
    Args:
        token (str): JWT token string
        
    Returns:
        Dict containing user information if valid, None if invalid
    """
    try:
        config = get_app_config()
        
        payload = jwt.decode(
            token, 
            config.jwt_secret_key, 
            algorithms=["HS256"]
        )
        
        return {
            "username": payload.get("username"),
            "displayName": payload.get("displayName"),
            "email": payload.get("email"),
            "groups": payload.get("groups", [])
        }
        
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token validation failed: Token expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"JWT token validation failed: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"JWT token validation error: {str(e)}")
        return None


def is_token_expired(token: str) -> bool:
    """
    Check if a JWT token is expired without validating signature.
    
    Args:
        token (str): JWT token string
        
    Returns:
        bool: True if token is expired, False otherwise
    """
    try:
        # Decode without verification to check expiration
        payload = jwt.decode(token, options={"verify_signature": False})
        exp_timestamp = payload.get("exp")
        
        if exp_timestamp:
            exp_datetime = datetime.fromtimestamp(exp_timestamp)
            return datetime.utcnow() > exp_datetime
            
        return True  # No expiration claim means expired
        
    except Exception:
        return True  # Invalid token format means expired
