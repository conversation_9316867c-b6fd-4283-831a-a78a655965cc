# ****************************************************************************
# Author: Name
# Desc: Business Services for Note
#
#   Record Manager functions
#   - GetNotes           --> Get List
#   - GetNote            --> Get Record
#   - AddNote            --> Add Record
#   - UpdateNote         --> Update Record
#   - DeleteNote         --> Delete Record
#
#   Other functions for the Business Service
#   - BusinessFunction1
#   - BusinessFunction2
#
# ****************************************************************************

import json
import logging

from AGGridServices import generateLimitOffsetSQL, generateOrderBySQL, generateWhereSQL
from DBHandler import get_connection
from flask import Response, jsonify, request

logger = logging.getLogger()


# ****************************************************************************
# Get List of Note records
# ****************************************************************************
def GetNotes(request):

    logger.info("Inside GetNotes")

    # Get the payload which contains the parameters to query data
    # including AG Grid filter model, sort model and paging details
    payload = request.json

    conn = get_connection()

    with conn.cursor() as cursor:

        sql = """select
                    SQL_CALC_FOUND_ROWS 
                    Note.id,
                    Note.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Note.Type,
                    Note.Title,
                    Note.Sequence,
                    Note.created_by,
                    Note.created,
                    Note.modified_by,
                    Note.modified
                from
                    Note 
                    left outer join AccessSeeker on Note.AccessSeekerRecordId = AccessSeeker.id"""

        # Select records based on the filtering criteria
        sql = sql + generateWhereSQL(payload)

        # Sort records based on the sort criteria
        sql = sql + generateOrderBySQL(payload)

        # Limit the results based on the paging parameters
        sql = sql + generateLimitOffsetSQL(payload)

        cursor.execute(sql)
        records = cursor.fetchall()

        # Get the total number of records
        sql = "SELECT FOUND_ROWS() as totalRecords"
        cursor.execute(sql)
        totalRecordsResult = cursor.fetchone()
        totalRecords = totalRecordsResult["totalRecords"]

    response = jsonify({"totalRecords": totalRecords, "records": records})
    return response


# ****************************************************************************
# Get a specific Note Record
# ****************************************************************************
def GetNote(Id):

    logger.info("Inside GetNote. Id: " + str(Id))

    conn = get_connection()

    with conn.cursor() as cursor:

        sql = """select
                    Note.id,
                    Note.AccessSeekerRecordId,
                    AccessSeeker.AccessSeekerId,
                    Note.Type,
                    Note.Title,
                    Note.Sequence,
                    Note.Description,
                    Note.created_by,
                    Note.created,
                    Note.modified_by,
                    Note.modified
                from
                    Note 
                    left outer join AccessSeeker on Note.AccessSeekerRecordId = AccessSeeker.id
                 where Note.id = %(Id)s"""

        cursor.execute(sql, {"Id": Id})

        # Get record
        record = cursor.fetchone()

    return record


# ****************************************************************************
# Add a new Note record
# ****************************************************************************
def AddNote(note):

    logger.info("Inside AddNote")

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlFieldList = ""
    sqlFieldValues = ""
    sqlQueryParams = {}

    if "AccessSeekerRecordId" in note:
        sqlFieldList += "AccessSeekerRecordId, "
        sqlFieldValues += "%(AccessSeekerRecordId)s, "
        sqlQueryParams["AccessSeekerRecordId"] = note["AccessSeekerRecordId"]
    if "Type" in note:
        sqlFieldList += "Type, "
        sqlFieldValues += "%(Type)s, "
        sqlQueryParams["Type"] = note["Type"]
    if "Title" in note:
        sqlFieldList += "Title, "
        sqlFieldValues += "%(Title)s, "
        sqlQueryParams["Title"] = note["Title"]
    if "Sequence" in note:
        sqlFieldList += "Sequence, "
        sqlFieldValues += "%(Sequence)s, "
        sqlQueryParams["Sequence"] = note["Sequence"]
    if "Description" in note:
        sqlFieldList += "Description, "
        sqlFieldValues += "%(Description)s, "
        sqlQueryParams["Description"] = note["Description"]

    sqlFieldList += "created_by, "
    sqlFieldValues += "%(LoggedInUser)s, "
    sqlFieldList += "created "
    sqlFieldValues += "NOW() "

    sqlQueryParams["LoggedInUser"] = LoggedInUser

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "INSERT INTO Note (" + sqlFieldList + ") VALUES (" + sqlFieldValues + ")"

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)

        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            addedNote = GetNote(cursor.lastrowid)

        # Else record was not successfully added
        else:

            # Get the updated record
            addedNote = None

    return addedNote


# ****************************************************************************
# Update a Note Record
# ****************************************************************************
def UpdateNote(id, patchValues):

    logger.info("Inside UpdateNote")

    # Initialise Variables
    LoggedInUser = GetLoggedInUser(request)

    sqlQueryParams = {}

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate insert SQL
        sql = "UPDATE Note SET "

        if "AccessSeekerRecordId" in patchValues:
            sql += "AccessSeekerRecordId = %(AccessSeekerRecordId)s, "
            sqlQueryParams["AccessSeekerRecordId"] = patchValues["AccessSeekerRecordId"]
        if "Type" in patchValues:
            sql += "Type = %(Type)s, "
            sqlQueryParams["Type"] = patchValues["Type"]
        if "Title" in patchValues:
            sql += "Title = %(Title)s, "
            sqlQueryParams["Title"] = patchValues["Title"]
        if "Sequence" in patchValues:
            sql += "Sequence = %(Sequence)s, "
            sqlQueryParams["Sequence"] = patchValues["Sequence"]
        if "Description" in patchValues:
            sql += "Description = %(Description)s, "
            sqlQueryParams["Description"] = patchValues["Description"]

        sql += "modified_by = %(LoggedInUser)s, "
        sql += "modified = NOW() "
        sql += "WHERE id = %(Id)s "

        logger.info("UpdateNote SQL: " + sql)

        sqlQueryParams["Id"] = id
        sqlQueryParams["LoggedInUser"] = LoggedInUser

        # Execute insert SQL with appropriate parameters
        cursor.execute(sql, sqlQueryParams)

        # If record sucessfully added
        if cursor.rowcount > 0:

            # Get the updated record
            updatedNote = GetNote(id)

        # Else record was not successfully updated
        else:

            # Get the updated record
            updatedNote = None

    return updatedNote


# ****************************************************************************
# Delete a Note Record
# ****************************************************************************
def DeletedNote(request):

    logger.info("Inside DeletedNote")

    # Get the message body which contains the data to update
    payload = request.json

    # Get the Id of the record to delete
    Id = payload["id"]

    conn = get_connection()

    with conn.cursor() as cursor:

        # Generate delete SQL
        sql = """DELETE FROM Note 
                 WHERE id = %(Id)s"""

        # Execute delete SQL with appropriate parameters
        cursor.execute(sql, {"Id": Id})

        # If record sucessfully deleted
        if cursor.rowcount > 0:

            # Generate the success response message
            response = jsonify(
                {
                    "data": {"id": str(Id)},
                    "message": "Successfully deleted Note Record: " + str(Id),
                }
            )

        # Else record was not successfully deleted
        else:

            logger.info("Error deleting Note with id = " + str(Id))

            # Generate the error response
            errResponseMsg = json.dumps(
                {"code": 1, "error": "Error deleting Note with id = " + str(Id)}
            )

            # Return a 500 error response
            return Response(errResponseMsg, status=500, mimetype="application/json")

    return response


# ****************************************************************************
# Get details of the logged in user
# Updated to use authentication middleware for JWT-based user context
# ****************************************************************************
def GetLoggedInUser(request=None):

    logger.info("Inside GetLoggedInUser")

    # Import here to avoid circular imports
    from auth_middleware import get_current_user

    try:
        # Get current user from authentication middleware
        user = get_current_user()
        userName = user.get("username", "unknown")

        logger.debug(f"Retrieved logged in user: {userName}")
        return userName

    except Exception as e:
        logger.warning(f"Error getting logged in user: {str(e)}")
        return "unknown"
