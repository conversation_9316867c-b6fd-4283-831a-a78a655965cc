# Ollama Setup for AI Insights

This document provides instructions for setting up Ollama to enable AI-powered insights in the CIO RSP Executive Digital Usage component.

## Prerequisites

- macOS, Linux, or Windows
- At least 8GB RAM (16GB recommended)
- Internet connection for model downloads

## Installation

### macOS
```bash
# Install Ollama using Homebrew
brew install ollama

# Or download from https://ollama.ai/download
```

### Linux
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh
```

### Windows
Download and install from: https://ollama.ai/download

## Setup

### 1. Start Ollama Service
```bash
# Start Ollama service (runs on http://localhost:11434 by default)
ollama serve
```

### 2. Pull Required Model
```bash
# Pull the Llama 3.2 model (default model used by the application)
ollama pull llama3.2

# Alternative models you can use:
# ollama pull llama3.1
# ollama pull mistral
# ollama pull codellama
```

### 3. Test Ollama Installation
```bash
# Test that Ollama is working
curl http://localhost:11434/api/tags

# Should return a JSON response with available models
```

### 4. Configure Application (Optional)

The application uses these default settings:
- **Ollama URL**: `http://localhost:11434`
- **Model**: `llama3.2`

To customize, set environment variables:
```bash
export OLLAMA_URL="http://localhost:11434"
export OLLAMA_MODEL="llama3.2"
```

## Usage

### Starting the Service
1. Start Ollama service: `ollama serve`
2. Start the backend Flask application
3. Navigate to the CIO RSP Executive Digital Usage page
4. Click "Generate Insights" to get AI-powered analysis

### Model Performance

| Model | Size | RAM Required | Speed | Quality |
|-------|------|--------------|-------|---------|
| llama3.2 | ~2GB | 8GB | Fast | High |
| llama3.1 | ~4GB | 16GB | Medium | Very High |
| mistral | ~4GB | 16GB | Medium | High |

## Troubleshooting

### Ollama Service Not Available
- **Error**: "Ollama service unavailable, falling back to statistical analysis"
- **Solution**: Ensure Ollama is running with `ollama serve`

### Model Not Found
- **Error**: Model download or loading issues
- **Solution**: Pull the model with `ollama pull llama3.2`

### Memory Issues
- **Error**: Out of memory errors
- **Solution**: Use a smaller model or increase system RAM

### Port Conflicts
- **Error**: Port 11434 already in use
- **Solution**: Stop other services or configure Ollama to use a different port

## Advanced Configuration

### Custom Model
To use a different model, update the environment variable:
```bash
export OLLAMA_MODEL="mistral"
```

### Custom Port
To run Ollama on a different port:
```bash
OLLAMA_HOST=0.0.0.0:11435 ollama serve
export OLLAMA_URL="http://localhost:11435"
```

### Performance Tuning
For better performance on high-end systems:
```bash
# Set GPU layers (if you have a compatible GPU)
export OLLAMA_NUM_GPU_LAYERS=35

# Increase context window
export OLLAMA_NUM_CTX=4096
```

## Fallback Behavior

If Ollama is not available, the application automatically falls back to statistical analysis:
- Volume trend calculations
- Channel mix analysis
- Growth rate computations
- Basic recommendations

This ensures the application continues to work even without AI capabilities.

## Security Considerations

- Ollama runs locally, so no data is sent to external services
- All AI processing happens on your local machine
- Network traffic is only between your browser and local services

## Support

For Ollama-specific issues:
- Documentation: https://ollama.ai/docs
- GitHub: https://github.com/ollama/ollama

For application-specific issues:
- Check the backend logs for detailed error messages
- Verify Ollama service status with `curl http://localhost:11434/api/tags`
