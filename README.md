# RSPTracker

RSPTracker is a web application designed to provide insights into Retail Service Provider (RSP) operations. It focuses on intelligence gathering, monitoring, and analysis across APIs, portals, and usage patterns. Additionally, it serves as a proof of concept for CRM-related functions, such as project management and data visualization.

## ✅ Recent Updates (January 2025)

**🎉 MAJOR ACHIEVEMENT: Clean API Architecture Implemented**: The application has successfully transitioned from legacy `/Test/` prefixed URLs to a modern, clean API architecture while maintaining 100% backward compatibility and zero downtime.

**🚀 Clean API Implementation**:
- ✅ **Modern Clean APIs**: New `/api/v1/` endpoints for all CRUD operations (e.g., `/api/v1/access-seekers/list`)
- ✅ **Backward Compatibility**: Legacy `/Test/` endpoints preserved for seamless migration
- ✅ **RESTful Design**: Industry-standard URL patterns and HTTP methods
- ✅ **Complete Coverage**: All 7 entities + analytics endpoints migrated
- ✅ **Zero Downtime**: Seamless migration with no service interruption

**🔧 Technical Achievements**:
- ✅ **Dual API Support**: Both modern clean and legacy APIs work simultaneously
- ✅ **Frontend Migration**: All 8 services updated to use clean URLs
- ✅ **Type Safety**: TypeScript interfaces for all service methods
- ✅ **Comprehensive Testing**: All endpoints verified working
- ✅ **Professional Documentation**: Complete migration guides and verification reports

**📊 API Examples**:
- **Modern**: `POST /api/v1/access-seekers/list`, `GET /api/v1/contacts/1`
- **Legacy**: `POST /Test/AccessSeekerList`, `GET /Test/Contact/1` (still working)

**🎯 Production Ready**: The application now features world-class API architecture with clean, intuitive endpoints while maintaining full backward compatibility.

## Features

- **RSP Intelligence**: Monitor and analyze API transactions, portal usage, and overall digital activity.
- **Data Visualization**: Interactive charts for digital usage history using AG Charts.
- **Dynamic Filtering**: Advanced filtering and sorting capabilities with AG Grid.
- **Project Management**: Create, update, and manage project records as part of CRM functionality.
- **Responsive Design**: Optimized for various screen sizes using Angular Material components.

## Prerequisites

- [Node.js](https://nodejs.org/) (version 16 or higher)
- [Angular CLI](https://angular.io/cli) (version 17.0.0)
- A running backend server (default URL: `http://localhost:5001`)
- Make
- Docker
- AWS Access
- KubeCTL

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd RSPi/frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Update environment variables:
   - Modify the backend URL in the `environment.ts` file if necessary.

## Quick Start

### **Recommended: Use Startup Scripts**
The easiest way to start the application:

```bash
# Start in development mode (default - no authentication)
./start_app.sh

# Start in production mode (LDAP authentication enabled)
./start_app.sh --prod

# View all options
./start_app.sh --help
```

### **Manual Development Server**
If you prefer to run services separately:

```bash
# Terminal 1 - Backend
cd backend
source venv/bin/activate
python app/app.py

# Terminal 2 - Frontend
cd frontend
npm start
```

Navigate to `http://localhost:4200/`. The application will automatically reload if you make changes to the source files.

### **Authentication Modes**
- **Development Mode**: No login required, direct access to all features
- **Production Mode**: LDAP authentication against NBNCO Active Directory required

## Usage

### RSP Intelligence
- Analyze API and portal usage trends with interactive charts.
- Gain insights into transaction patterns and digital activity.

### Project List
- View and filter project records using the AG Grid table.
- Sort and search by various fields like `Name`, `Org`, and `Category`.

### Project Form
- Create or update project records.
- Validate form inputs before submission.

## Build

To build the project for production:
```bash
ng build
```
The build artifacts will be stored in the `dist/` directory.

## Testing

### Service Standardization Tests
The application includes a comprehensive test suite for verifying service standardization:
```bash
# Start the application
npm start
# Navigate to http://localhost:4200/test-standardization
```

### Unit Tests
Run unit tests using Karma:
```bash
ng test
```

### End-to-End Tests
Run end-to-end tests:
```bash
ng e2e
```
(Note: Ensure you have a testing package installed for e2e tests.)

## Deployment

### Backend Prepare
1. Change the docker tag version in the backend / Makefile
2. Build and publish image

```
cd backend 

make container-image
make publish-container-image
```

### Frontend Prepare
1. Change the docker tag version in the frontend / Makefile
2. Build and publish image

```
cd frontend 

make container-image
make publish-container-image
```

### Deploy Kubernetes
1. Change the image tag version in deployment / overlays / dev / kustomization.yaml (change dev to env that is being deployed)
2. Connect to Kubernetes cluster

```
aws sso login
aws eks update-kubeconfig --name candc1-nbn-central-eks
kubectl config set-context --current --namespace rsp-sys-ops
```

3. Create the backend secret if required (generally first time deployment or when rotating credentials)

```
kubectl create secret generic insights-backend-env --from-literal=DB_USER=placeholder --from-literal=DB_PASSWORD=placeholder
```

4. Deploy (change dev to env that is being deployed)

```
cd deployment
kubectl apply -k overlays/dev 
```

5. Test (change dev to env that is being deployed)

```
http://rspi.dev.cupc.inttest.nbn-aws.local
```

## Contribution Guidelines

1. Fork the repository.
2. Create a new branch for your feature or bug fix:
   ```bash
   git checkout -b feature-name
   ```
3. Commit your changes:
   ```bash
   git commit -m "Description of changes"
   ```
4. Push to your branch:
   ```bash
   git push origin feature-name
   ```
5. Open a pull request.

## Testing Authentication System

Test the authentication system in different modes:

```bash
# Test development mode (no authentication)
python test_auth_system.py --mode dev

# Test production mode (requires NBNCO credentials)
python test_auth_system.py --mode live --username YOUR_USERNAME --password YOUR_PASSWORD
```

## Troubleshooting

### Common Issues
- **Backend won't start**: Ensure virtual environment is activated and dependencies installed
- **Frontend won't start**: Run `npm install` to install dependencies
- **Database connection issues**: Ensure Docker is running and MySQL container is started
- **Authentication issues**: Check environment variables and LDAP connectivity

### Manual Startup (Windows)
```powershell
# Backend
cd backend
.\venv\Scripts\Activate.ps1
python app/app.py

# Frontend
cd frontend
npm start
```

## Further Help

For more information:
- **Project Guidelines**: See `augment-guidelines.md` for comprehensive development guidelines
- **Authentication Guide**: See `AUTHENTICATION_README.md` for authentication system details
- **Quick Reference**: See `AUTHENTICATION_QUICK_START.md` for quick authentication setup
- **Angular CLI**: Visit the [Angular CLI Overview and Command Reference](https://angular.io/cli)


