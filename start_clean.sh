#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Application Clean Start ===${NC}"

# Function to clean up on exit
cleanup() {
    echo -e "\n${BLUE}Shutting down servers...${NC}"

    # Kill backend process if it exists
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        if ps -p $BACKEND_PID > /dev/null; then
            kill $BACKEND_PID
            echo -e "${GREEN}Backend server stopped${NC}"
        fi
        rm backend.pid
    fi

    # Kill frontend process if it exists
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        if ps -p $FRONTEND_PID > /dev/null; then
            kill $FRONTEND_PID
            echo -e "${GREEN}Frontend server stopped${NC}"
        fi
        rm frontend.pid
    fi

    echo -e "${GREEN}Cleanup complete${NC}"
    exit 0
}

# Set up trap to catch Ctrl+C and other termination signals
trap cleanup SIGINT SIGTERM

# Step 1: Kill any existing processes
echo -e "${YELLOW}Stopping any existing processes...${NC}"
pkill -f "flask run" || true
pkill -f "ng serve" || true
sleep 2

# Step 2: Verify MySQL container
echo -e "${YELLOW}Checking MySQL container...${NC}"
if ! docker ps | grep -q "local-mysql"; then
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Starting existing MySQL container...${NC}"
        docker start local-mysql
        sleep 5
    else
        echo -e "${RED}MySQL container not found. Please run ./emergency_fix.sh first.${NC}"
        exit 1
    fi
fi
echo -e "${GREEN}MySQL container is running.${NC}"

# Step 3: Start Backend
echo -e "${YELLOW}Starting backend server...${NC}"
cd backend || exit 1

# Activate virtual environment
source venv/bin/activate

# Set environment variables for backend
export CORS_ORIGINS=http://localhost:4200
export LOGGING_CONFIG_PATH=logging.yaml
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=password
export DB_NAME=mydb

# Start backend with activated virtual environment
cd app
python app.py &
BACKEND_PID=$!
echo $BACKEND_PID > ../../backend.pid

cd ../..

# Wait for backend to be ready
echo -e "${YELLOW}Waiting for backend to start...${NC}"
for i in {1..15}; do
    if curl -s http://localhost:5002 > /dev/null; then
        echo -e "${GREEN}✓ Backend is running on http://localhost:5002${NC}"
        break
    fi
    if [ $i -eq 15 ]; then
        echo -e "${YELLOW}Backend may not be fully initialized yet.${NC}"
    fi
    sleep 1
    echo -n "."
done
echo ""

# Step 4: Start Frontend
echo -e "${YELLOW}Starting frontend server...${NC}"
cd frontend || exit 1

# Start Angular with basic options for reliable startup
npx ng serve --host 0.0.0.0 --port 4200 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../frontend.pid

cd ..

# Wait for frontend to be ready
echo -e "${YELLOW}Waiting for frontend to start...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:4200 > /dev/null; then
        echo -e "${GREEN}✓ Frontend is running on http://localhost:4200${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${YELLOW}Frontend may still be building. Check frontend.log for progress.${NC}"
    fi
    sleep 2
    echo -n "."
done
echo ""

# Step 5: Open browser
if command -v open &> /dev/null; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    open http://localhost:4200
elif command -v xdg-open &> /dev/null; then
    echo -e "${BLUE}Opening application in browser...${NC}"
    xdg-open http://localhost:4200
else
    echo -e "${YELLOW}Please open http://localhost:4200 in your browser.${NC}"
fi

# Final status
echo -e "\n${BLUE}=== Application Started Successfully ===${NC}"
echo -e "${GREEN}Frontend: http://localhost:4200${NC}"
echo -e "${GREEN}Backend:  http://localhost:5002${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop both servers${NC}"

# Keep the script running
echo -e "\n${BLUE}=== Servers are running (Press Ctrl+C to stop) ===${NC}"
echo -e "${YELLOW}Backend logs: Check terminal output above${NC}"
echo -e "${YELLOW}Frontend logs: Check browser console for any issues${NC}"
echo -e "${YELLOW}Application URL: http://localhost:4200${NC}"

# Wait for user to stop
wait
