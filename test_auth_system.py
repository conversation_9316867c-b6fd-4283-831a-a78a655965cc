#!/usr/bin/env python3
"""
Authentication System Test Script for RSPi Application

This script tests the authentication system in both development and live modes.
It validates the environment-based behavior and ensures all components work correctly.

Usage:
    python test_auth_system.py [--mode dev|live]
"""

import json
import os
import sys
from typing import Any, Dict

import requests

# Test configuration
DEV_CONFIG = {
    "base_url": "http://localhost:5001",
    "auth_enabled": False,
    "test_username": "dev-user",
    "test_password": "any-password",
}

LIVE_CONFIG = {
    "base_url": "http://localhost:5001",
    "auth_enabled": True,
    "test_username": "your-nbnco-username",  # Replace with actual username
    "test_password": "your-nbnco-password",  # Replace with actual password
}


class AuthSystemTester:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config["base_url"]
        self.session = requests.Session()
        self.token = None

    def test_health_endpoint(self) -> bool:
        """Test authentication system health endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/auth/health")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Auth health check passed: {data}")
                return data.get("success", False)
            else:
                print(f"❌ Auth health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Auth health check error: {e}")
            return False

    def test_login(self) -> bool:
        """Test login functionality"""
        try:
            login_data = {
                "username": self.config["test_username"],
                "password": self.config["test_password"],
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/auth/login", json=login_data
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.token = data.get("token")
                    user = data.get("user", {})
                    print(f"✅ Login successful: {user.get('displayName', 'Unknown')}")
                    return True
                else:
                    print(f"❌ Login failed: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ Login request failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False

    def test_protected_endpoint(self) -> bool:
        """Test access to protected endpoint"""
        try:
            headers = {}
            if self.token and self.config["auth_enabled"]:
                headers["Authorization"] = f"Bearer {self.token}"

            response = self.session.get(
                f"{self.base_url}/api/v1/auth/me", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    user = data.get("user", {})
                    print(
                        f"✅ Protected endpoint access successful: {user.get('username', 'Unknown')}"
                    )
                    return True
                else:
                    print(
                        f"❌ Protected endpoint failed: {data.get('error', 'Unknown error')}"
                    )
                    return False
            else:
                print(f"❌ Protected endpoint request failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Protected endpoint error: {e}")
            return False

    def test_token_validation(self) -> bool:
        """Test token validation endpoint"""
        try:
            headers = {}
            if self.token and self.config["auth_enabled"]:
                headers["Authorization"] = f"Bearer {self.token}"

            response = self.session.get(
                f"{self.base_url}/api/v1/auth/validate", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    print(f"✅ Token validation successful")
                    return True
                else:
                    print(
                        f"❌ Token validation failed: {data.get('error', 'Invalid token')}"
                    )
                    return False
            else:
                print(f"❌ Token validation request failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Token validation error: {e}")
            return False

    def test_logout(self) -> bool:
        """Test logout functionality"""
        try:
            headers = {}
            if self.token and self.config["auth_enabled"]:
                headers["Authorization"] = f"Bearer {self.token}"

            response = self.session.post(
                f"{self.base_url}/api/v1/auth/logout", headers=headers
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    print(f"✅ Logout successful")
                    self.token = None
                    return True
                else:
                    print(f"❌ Logout failed: {data.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"❌ Logout request failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Logout error: {e}")
            return False

    def run_all_tests(self) -> bool:
        """Run all authentication tests"""
        mode = "Development" if not self.config["auth_enabled"] else "Live"
        print(f"\n🧪 Testing Authentication System - {mode} Mode")
        print("=" * 50)

        tests = [
            ("Health Check", self.test_health_endpoint),
            ("Login", self.test_login),
            ("Protected Endpoint", self.test_protected_endpoint),
            ("Token Validation", self.test_token_validation),
            ("Logout", self.test_logout),
        ]

        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name} test...")
            if test_func():
                passed += 1
            else:
                print(f"   Test failed!")

        print(f"\n📊 Test Results: {passed}/{total} tests passed")

        if passed == total:
            print(
                f"🎉 All tests passed! Authentication system is working correctly in {mode} mode."
            )
            return True
        else:
            print(
                f"⚠️  Some tests failed. Please check the authentication system configuration."
            )
            return False


def main():
    """Main test function"""
    import argparse

    parser = argparse.ArgumentParser(description="Test RSPi Authentication System")
    parser.add_argument(
        "--mode",
        choices=["dev", "live"],
        default="dev",
        help="Test mode: dev (development) or live (production)",
    )
    parser.add_argument("--username", help="Username for live mode testing")
    parser.add_argument("--password", help="Password for live mode testing")

    args = parser.parse_args()

    config = DEV_CONFIG if args.mode == "dev" else LIVE_CONFIG.copy()

    # Override credentials if provided
    if args.username:
        config["test_username"] = args.username
    if args.password:
        config["test_password"] = args.password

    print(f"🚀 Starting Authentication System Tests")
    print(f"Mode: {args.mode}")
    print(f"Base URL: {config['base_url']}")
    print(f"Auth Enabled: {config['auth_enabled']}")

    if args.mode == "live":
        print("\n⚠️  Live mode testing requires valid NBNCO credentials.")
        if config["test_username"] == "your-nbnco-username":
            print("❌ Please provide NBNCO credentials:")
            print(
                "   python test_auth_system.py --mode live --username YOUR_USERNAME --password YOUR_PASSWORD"
            )
            return False
        else:
            print(f"✅ Testing with username: {config['test_username']}")

    tester = AuthSystemTester(config)
    return tester.run_all_tests()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
