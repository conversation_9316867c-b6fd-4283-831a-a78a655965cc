#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Backend API Check Tool ===${NC}"

# Check if backend is running
echo -e "${YELLOW}Checking if Flask process is running...${NC}"
if pgrep -f "flask run" > /dev/null; then
    echo -e "${GREEN}✓ Flask process is running${NC}"
    
    # Get process details
    echo -e "${YELLOW}Flask process details:${NC}"
    ps -ef | grep "flask run" | grep -v grep
else
    echo -e "${RED}✗ Flask process is not running${NC}"
fi

# Check if port 5001 is in use
echo -e "${YELLOW}Checking if port 5001 is in use...${NC}"
if command -v lsof &> /dev/null; then
    if lsof -i :5001 | grep -q LISTEN; then
        echo -e "${GREEN}✓ Port 5001 is in use${NC}"
        lsof -i :5001 | grep LISTEN
    else
        echo -e "${RED}✗ Port 5001 is not in use${NC}"
    fi
else
    echo -e "${YELLOW}Cannot check port status (lsof not found)${NC}"
fi

# Test basic backend connection
echo -e "${YELLOW}Testing basic backend connection...${NC}"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5001)
if [ "$RESPONSE" != "000" ]; then
    echo -e "${GREEN}✓ Backend is responding with HTTP code: $RESPONSE${NC}"
else
    echo -e "${RED}✗ Backend is not responding${NC}"
fi

# Test specific API endpoints
echo -e "${YELLOW}Testing API endpoint: /CioRspExecutive/DigitalUsagePeriods${NC}"
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5001/CioRspExecutive/DigitalUsagePeriods)
if [ "$RESPONSE" != "000" ]; then
    echo -e "${GREEN}✓ CIO RSP Executive endpoint is responding with HTTP code: $RESPONSE${NC}"

    # Get actual response
    echo -e "${YELLOW}Response from /CioRspExecutive/DigitalUsagePeriods:${NC}"
    curl -s http://localhost:5001/CioRspExecutive/DigitalUsagePeriods | head -c 300
    echo -e "\n"
else
    echo -e "${RED}✗ CIO RSP Executive endpoint is not responding${NC}"
fi

# Check backend log file
echo -e "${YELLOW}Checking backend log file...${NC}"
if [ -f "backend.log" ]; then
    echo -e "${GREEN}✓ Backend log file exists${NC}"
    echo -e "${YELLOW}Last 20 lines of backend.log:${NC}"
    tail -n 20 backend.log
else
    echo -e "${RED}✗ Backend log file does not exist${NC}"
fi

# Check backend environment file
echo -e "${YELLOW}Checking backend environment file...${NC}"
if [ -f "backend/.env" ]; then
    echo -e "${GREEN}✓ Backend .env file exists${NC}"
    echo -e "${YELLOW}Backend .env contents:${NC}"
    cat backend/.env | grep -v PASSWORD
else
    echo -e "${RED}✗ Backend .env file does not exist${NC}"
fi

echo -e "\n${BLUE}=== Backend Check Complete ===${NC}"
