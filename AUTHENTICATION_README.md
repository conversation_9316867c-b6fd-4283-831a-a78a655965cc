# RSPi Authentication System

## Overview

The RSPi application implements a comprehensive environment-based authentication system that provides:

- **Development Mode**: Complete authentication bypass for seamless development
- **Live Mode**: Full LDAP authentication against NBNCO Active Directory with JWT tokens

## Architecture

### Backend Components

1. **AuthAPI.py** - Authentication endpoints
2. **auth_middleware.py** - JWT validation and user context
3. **ldap_auth.py** - NBNCO Active Directory integration
4. **Enhanced CORS** - Support for authentication headers

### Frontend Components

1. **AuthService** - Token and user management
2. **authGuard** - Route protection
3. **authInterceptor** - HTTP request enhancement
4. **LoginComponent** - Material Design login interface

## Configuration

### Development Mode

**Backend (.env.local):**
```bash
AUTH_ENABLED=false
JWT_SECRET_KEY=dev-secret-key
```

**Frontend (.env):**
```bash
NG_APP_AUTH_ENABLED=false
```

### Live Mode

**Backend (.env.production):**
```bash
AUTH_ENABLED=true
JWT_SECRET_KEY=your-production-secret-key
JWT_EXPIRATION_HOURS=8
LDAP_HOST=SVEDC2000004PR.nbnco.local
LDAP_BASE_DN=DC=nbnco,DC=local
```

**Frontend (.env.production):**
```bash
NG_APP_AUTH_ENABLED=true
```

## Installation

### Backend Dependencies

```bash
cd backend
source venv/bin/activate
pip install ldap3==2.9.1 PyJWT==2.8.0 cryptography==41.0.7
```

### Frontend Dependencies

```bash
cd frontend
npm install @auth0/angular-jwt@^5.2.0
```

## Usage

### Development Mode

1. Set environment variables for development mode
2. Start the application normally
3. No login required - direct access to all features
4. User context shows "dev-user"

### Live Mode

1. Set environment variables for live mode
2. Start the application
3. Users are redirected to login page
4. Enter NBNCO credentials for authentication
5. JWT token manages session state

## API Endpoints

### Authentication Endpoints

- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/logout` - Session termination
- `GET /api/v1/auth/me` - Current user information
- `GET /api/v1/auth/validate` - Token validation
- `GET /api/v1/auth/health` - System health check

### Protected Endpoints

All existing API endpoints are now protected by authentication middleware:
- Development mode: No protection (bypass)
- Live mode: Requires valid JWT token

## Testing

### Automated Testing

```bash
# Test development mode
python test_auth_system.py --mode dev

# Test live mode (requires NBNCO credentials)
python test_auth_system.py --mode live
```

### Manual Testing

1. **Development Mode:**
   - Access application directly without login
   - Verify user shows as "dev-user (DEV)"
   - Confirm all features work normally

2. **Live Mode:**
   - Verify redirect to login page
   - Test LDAP authentication with valid credentials
   - Confirm JWT token in browser storage
   - Test automatic logout on token expiration

## Security Features

### JWT Token Security
- Configurable expiration (default: 8 hours)
- Secure secret key for production
- Automatic cleanup on expiration

### LDAP Integration
- SSL/TLS encrypted connection
- NBNCO domain authentication
- User attribute extraction (name, email, groups)

### CORS Configuration
- Authentication header support
- Credential support for secure requests
- Origin-based access control

## Troubleshooting

### Common Issues

1. **LDAP Connection Errors:**
   - Verify LDAP_HOST and LDAP_BASE_DN settings
   - Check network connectivity to domain controller
   - Ensure SSL/TLS certificates are valid

2. **JWT Token Issues:**
   - Verify JWT_SECRET_KEY is set correctly
   - Check token expiration settings
   - Clear browser storage if tokens are corrupted

3. **CORS Errors:**
   - Verify CORS_ORIGINS includes frontend URL
   - Check authentication headers are allowed
   - Ensure credentials support is enabled

### Debug Mode

Enable debug logging in backend:
```python
import logging
logging.getLogger('ldap3').setLevel(logging.DEBUG)
logging.getLogger('auth_middleware').setLevel(logging.DEBUG)
```

## Migration Guide

### From No Authentication

1. Update environment variables
2. Install new dependencies
3. Restart application
4. Test in development mode first
5. Configure live mode settings
6. Deploy with authentication enabled

### Backward Compatibility

- All existing API endpoints remain functional
- User context automatically injected
- No database schema changes required
- Existing frontend components work unchanged

## Best Practices

1. **Development:**
   - Always use development mode for local development
   - Test authentication flows before deployment
   - Keep JWT secrets secure and unique

2. **Production:**
   - Use strong JWT secret keys
   - Configure appropriate token expiration
   - Monitor authentication logs
   - Regularly update LDAP certificates

3. **Security:**
   - Never commit production secrets to version control
   - Use environment-specific configuration files
   - Implement proper error handling
   - Log authentication events for audit trails
