#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to open URL in browser
open_url() {
    local url=$1
    
    echo -e "${BLUE}Opening $url in browser...${NC}"
    
    # Check for different operating systems
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$url"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command_exists xdg-open; then
            xdg-open "$url"
        elif command_exists gnome-open; then
            gnome-open "$url"
        else
            echo -e "${YELLOW}Could not detect browser. Please open this URL manually:${NC}"
            echo -e "${GREEN}$url${NC}"
        fi
    else
        # Other OS
        echo -e "${YELLOW}Could not detect operating system. Please open this URL manually:${NC}"
        echo -e "${GREEN}$url${NC}"
    fi
}

# Check if backend is running
echo -e "${BLUE}Checking if backend is running...${NC}"
if curl -s http://localhost:5000 > /dev/null; then
    echo -e "${GREEN}Backend is running.${NC}"
else
    echo -e "${YELLOW}Backend does not appear to be running.${NC}"
    echo -e "${YELLOW}Make sure you've started the application with ./start.sh${NC}"
    
    # Ask if user wants to start the application
    echo -e "${YELLOW}Would you like to start the application now? (y/n)${NC}"
    read -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}Starting application...${NC}"
        ./start.sh &
        
        # Wait for backend to start
        echo -e "${YELLOW}Waiting for backend to start...${NC}"
        for i in {1..30}; do
            if curl -s http://localhost:5000 > /dev/null; then
                echo -e "${GREEN}Backend started successfully.${NC}"
                break
            fi
            
            if [ $i -eq 30 ]; then
                echo -e "${RED}Timed out waiting for backend to start.${NC}"
                echo -e "${YELLOW}Please check for errors in the application logs.${NC}"
                exit 1
            fi
            
            echo -n "."
            sleep 1
        done
        echo
    else
        echo -e "${YELLOW}Please start the application with ./start.sh before opening the browser.${NC}"
        exit 1
    fi
fi

# Check if frontend is running
echo -e "${BLUE}Checking if frontend is running...${NC}"
if curl -s http://localhost:4200 > /dev/null; then
    echo -e "${GREEN}Frontend is running.${NC}"
else
    echo -e "${YELLOW}Frontend does not appear to be running.${NC}"
    echo -e "${YELLOW}Make sure you've started the application with ./start.sh${NC}"
    exit 1
fi

# Open frontend in browser
open_url "http://localhost:4200"

echo -e "${GREEN}Application opened in browser.${NC}"
echo -e "${YELLOW}If the browser didn't open automatically, please navigate to:${NC}"
echo -e "${GREEN}http://localhost:4200${NC}"
