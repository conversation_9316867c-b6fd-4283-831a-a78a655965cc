#!/bin/bash

# RSPi Production Mode Startup Script
# This script starts the RSPi application in production mode with full LDAP authentication
# and comprehensive security validation.

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5001
FRONTEND_PORT=4200
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
BACKEND_LOG="backend.log"
FRONTEND_LOG="frontend.log"
BACKEND_PID="backend.pid"
FRONTEND_PID="frontend.pid"

echo -e "${PURPLE}========================================${NC}"
echo -e "${PURPLE}  RSPi Production Mode Startup${NC}"
echo -e "${PURPLE}========================================${NC}"
echo -e "${RED}🔐 Authentication: ENABLED (Production Mode)${NC}"
echo -e "${RED}👤 User Context: LDAP Authentication Required${NC}"
echo -e "${RED}🔐 LDAP: NBNCO Active Directory${NC}"
echo ""

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local service_name=$2
    echo -e "${YELLOW}⚠️  Port $port is in use. Stopping existing $service_name...${NC}"
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
    sleep 2
}

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down RSPi Production Mode...${NC}"
    
    if [ -f "$BACKEND_PID" ]; then
        local backend_pid=$(cat "$BACKEND_PID")
        if kill -0 "$backend_pid" 2>/dev/null; then
            echo -e "${YELLOW}   Stopping backend (PID: $backend_pid)...${NC}"
            kill "$backend_pid" 2>/dev/null || true
        fi
        rm -f "$BACKEND_PID"
    fi
    
    if [ -f "$FRONTEND_PID" ]; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            echo -e "${YELLOW}   Stopping frontend (PID: $frontend_pid)...${NC}"
            kill "$frontend_pid" 2>/dev/null || true
        fi
        rm -f "$FRONTEND_PID"
    fi
    
    # Kill any remaining processes on our ports
    kill_port $BACKEND_PORT "backend"
    kill_port $FRONTEND_PORT "frontend"
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Security validation function
validate_production_config() {
    echo -e "${BLUE}🔒 Validating production security configuration...${NC}"
    
    local errors=0
    
    # Check JWT secret key
    if [ -f "$BACKEND_DIR/.env.production" ]; then
        local jwt_secret=$(grep "JWT_SECRET_KEY=" "$BACKEND_DIR/.env.production" | cut -d'=' -f2)
        if [ "$jwt_secret" = "your-production-secret-key-change-this" ] || [ ${#jwt_secret} -lt 32 ]; then
            echo -e "${RED}❌ SECURITY WARNING: JWT_SECRET_KEY is not properly configured${NC}"
            echo -e "${YELLOW}   Please set a strong, unique secret key in .env.production${NC}"
            errors=$((errors + 1))
        else
            echo -e "${GREEN}✅ JWT secret key is properly configured${NC}"
        fi
    else
        echo -e "${RED}❌ Production environment file (.env.production) not found${NC}"
        errors=$((errors + 1))
    fi
    
    # Check LDAP connectivity
    echo -e "${BLUE}🔍 Testing LDAP connectivity...${NC}"
    if timeout 5 nc -z SVEDC2000004PR.nbnco.local 636 2>/dev/null; then
        echo -e "${GREEN}✅ LDAP server is reachable${NC}"
    else
        echo -e "${YELLOW}⚠️  LDAP server connectivity test failed${NC}"
        echo -e "${YELLOW}   This may be normal if not on NBNCO network${NC}"
    fi
    
    # Check database configuration
    if [ -f "$BACKEND_DIR/.env.production" ]; then
        local db_password=$(grep "DB_PASSWORD=" "$BACKEND_DIR/.env.production" | cut -d'=' -f2)
        if [ "$db_password" = "password" ] || [ ${#db_password} -lt 8 ]; then
            echo -e "${YELLOW}⚠️  Database password appears to be default or weak${NC}"
            echo -e "${YELLOW}   Consider using a stronger database password${NC}"
        fi
    fi
    
    if [ $errors -gt 0 ]; then
        echo -e "${RED}❌ Production validation failed with $errors error(s)${NC}"
        echo -e "${YELLOW}💡 To continue anyway, set SKIP_VALIDATION=true${NC}"
        if [ "$SKIP_VALIDATION" != "true" ]; then
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Production security validation passed${NC}"
    fi
}

echo -e "${BLUE}🔍 Checking dependencies...${NC}"

# Check if Docker is running (for MySQL)
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Check MySQL container
echo -e "${BLUE}🗄️  Checking MySQL container...${NC}"
if ! docker ps | grep -q mysql; then
    echo -e "${YELLOW}⚠️  MySQL container not running. Starting...${NC}"
    if ! docker run -d --name mysql-rspi -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password -e MYSQL_DATABASE=mydb mysql:8.0 >/dev/null 2>&1; then
        echo -e "${RED}❌ Failed to start MySQL container${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ MySQL container started${NC}"
    sleep 5  # Wait for MySQL to initialize
else
    echo -e "${GREEN}✅ MySQL container is running${NC}"
fi

# Validate production configuration
validate_production_config

# Check for port conflicts
if check_port $BACKEND_PORT; then
    kill_port $BACKEND_PORT "backend"
fi

if check_port $FRONTEND_PORT; then
    kill_port $FRONTEND_PORT "frontend"
fi

echo -e "${BLUE}🚀 Starting RSPi Production Mode...${NC}"

# Setup backend
echo -e "${BLUE}📦 Setting up backend...${NC}"
cd "$BACKEND_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found. Creating...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo -e "${BLUE}📥 Installing backend dependencies...${NC}"
pip install -q -r requirements.txt >/dev/null 2>&1

# Setup production environment file if it doesn't exist
if [ ! -f ".env.production" ]; then
    echo -e "${YELLOW}⚠️  Creating production environment template...${NC}"
    cat > .env.production << EOF
# Production Mode Configuration - CUSTOMIZE BEFORE USE
CORS_ORIGINS=https://rspi.nbnco.com.au
LOGGING_CONFIG_PATH=logging.yaml

# Production database settings - CUSTOMIZE
DB_HOST=your-production-db-host
DB_PORT=3306
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
DB_NAME=rspi_production

# Authentication settings (Live Mode - ENABLED)
AUTH_ENABLED=true
JWT_SECRET_KEY=your-production-secret-key-change-this
JWT_EXPIRATION_HOURS=8

# LDAP settings (used in live mode)
LDAP_HOST=SVEDC2000004PR.nbnco.local
LDAP_BASE_DN=DC=nbnco,DC=local

# Ollama configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2
EOF
    echo -e "${RED}❌ Production environment file created but needs customization${NC}"
    echo -e "${YELLOW}   Please edit .env.production with your production settings${NC}"
    exit 1
fi

# Use production environment
cp .env.production .env.local

# Start backend server
echo -e "${BLUE}🔧 Starting backend server...${NC}"
nohup python app/app.py > "../$BACKEND_LOG" 2>&1 &
echo $! > "../$BACKEND_PID"
echo -e "${GREEN}✅ Backend server started on http://localhost:$BACKEND_PORT${NC}"

# Wait for backend to be ready
echo -e "${BLUE}⏳ Waiting for backend to be ready...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:$BACKEND_PORT/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is ready!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Backend failed to start within 30 seconds${NC}"
        cleanup
        exit 1
    fi
    sleep 1
    echo -n "."
done

# Test authentication endpoint
echo -e "${BLUE}🔐 Testing authentication system...${NC}"
if curl -s http://localhost:$BACKEND_PORT/api/v1/auth/health >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Authentication system is responding${NC}"
else
    echo -e "${RED}❌ Authentication system is not responding${NC}"
    cleanup
    exit 1
fi

cd ..

# Setup frontend
echo -e "${BLUE}📦 Setting up frontend...${NC}"
cd "$FRONTEND_DIR"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}⚠️  Node modules not found. Installing...${NC}"
    npm install >/dev/null 2>&1
fi

# Setup production environment file if it doesn't exist
if [ ! -f ".env.production" ]; then
    echo -e "${YELLOW}⚠️  Creating frontend production environment template...${NC}"
    cat > .env.production << EOF
# Production Mode Configuration - CUSTOMIZE BEFORE USE
NG_APP_BASE_URL=https://rspi.nbnco.com.au/api
NG_APP_AUTH_ENABLED=true
EOF
fi

# Use production environment
cp .env.production .env

# Start frontend server
echo -e "${BLUE}🔧 Starting frontend server...${NC}"
nohup npm start > "../$FRONTEND_LOG" 2>&1 &
echo $! > "../$FRONTEND_PID"
echo -e "${GREEN}✅ Frontend server started on http://localhost:$FRONTEND_PORT${NC}"

# Wait for frontend to be ready
echo -e "${BLUE}⏳ Waiting for frontend to be ready...${NC}"
for i in {1..60}; do
    if curl -s http://localhost:$FRONTEND_PORT/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend is ready!${NC}"
        break
    fi
    if [ $i -eq 60 ]; then
        echo -e "${RED}❌ Frontend failed to start within 60 seconds${NC}"
        cleanup
        exit 1
    fi
    sleep 1
    if [ $((i % 10)) -eq 0 ]; then
        echo -n "."
    fi
done

cd ..

echo ""
echo -e "${GREEN}🎉 RSPi Production Mode Started Successfully!${NC}"
echo -e "${PURPLE}========================================${NC}"
echo -e "${CYAN}📱 Frontend: http://localhost:$FRONTEND_PORT${NC}"
echo -e "${CYAN}🔧 Backend:  http://localhost:$BACKEND_PORT${NC}"
echo -e "${RED}👤 User:     LDAP Authentication Required${NC}"
echo -e "${RED}🔐 Auth:     ENABLED (Login required)${NC}"
echo -e "${PURPLE}========================================${NC}"
echo ""
echo -e "${BLUE}📋 Production Mode Features:${NC}"
echo -e "   • Full LDAP authentication against NBNCO AD"
echo -e "   • JWT token-based session management"
echo -e "   • Protected routes requiring valid authentication"
echo -e "   • Automatic logout on token expiration"
echo -e "   • Comprehensive security validation"
echo ""
echo -e "${RED}⚠️  SECURITY REMINDERS:${NC}"
echo -e "   • Ensure JWT_SECRET_KEY is unique and strong"
echo -e "   • Verify LDAP connectivity to NBNCO domain"
echo -e "   • Monitor authentication logs for security events"
echo -e "   • Use HTTPS in production deployment"
echo ""
echo -e "${YELLOW}💡 To switch to development mode, use: ./start_dev.sh${NC}"
echo -e "${YELLOW}🛑 Press Ctrl+C to stop all services${NC}"
echo ""

# Display logs
echo -e "${BLUE}📊 Displaying combined logs (press Ctrl+C to stop)...${NC}"
echo -e "${PURPLE}=== Combined Logs (Backend + Frontend) ===${NC}"
echo ""

# Follow logs until interrupted
tail -f "$BACKEND_LOG" "$FRONTEND_LOG" 2>/dev/null || cleanup
