#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== RSPi Application Fix Script ===${NC}"

# Step 1: Kill any existing processes
echo -e "${YELLOW}Killing any existing processes...${NC}"
pkill -f "flask run" || true
pkill -f "ng serve" || true
sleep 2

# Step 2: Verify MySQL container
echo -e "${YELLOW}Checking MySQL container...${NC}"
if ! docker ps | grep -q "local-mysql"; then
    if docker ps -a | grep -q "local-mysql"; then
        echo -e "${YELLOW}Starting existing MySQL container...${NC}"
        docker start local-mysql
    else
        echo -e "${YELLOW}Creating new MySQL container...${NC}"
        docker run --name local-mysql \
            -e MYSQL_ROOT_PASSWORD=password \
            -e MYSQL_DATABASE=mydb \
            -p 3306:3306 \
            -d mysql:8.0
    fi
    sleep 5
fi

# Verify MySQL is running
if ! docker ps | grep -q "local-mysql"; then
    echo -e "${RED}Failed to start MySQL container. Please check Docker.${NC}"
    exit 1
fi
echo -e "${GREEN}MySQL container is running.${NC}"

# Step 3: Start Backend (Flask)
echo -e "${YELLOW}Starting backend server...${NC}"
cd backend || exit 1

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}Creating Python virtual environment...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source venv/bin/activate

# Install dependencies
echo -e "${YELLOW}Installing backend dependencies...${NC}"
pip install python-dotenv
if [ ! -f "requirements.txt" ]; then
    echo -e "${YELLOW}requirements.txt not found, installing Flask manually...${NC}"
    pip install Flask pymysql
else
    pip install -r requirements.txt
fi

# Set up environment file
echo -e "${YELLOW}Setting up backend environment file...${NC}"
if [ -f ".env.local" ]; then
    cp .env.local .env
else
    # Create minimal .env file
    cat > .env << EOL
CORS_ORIGINS=http://localhost:4200
LOGGING_CONFIG_PATH=logging.yaml
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=mydb
EOL
fi

# Start Flask on port 5001 (to avoid conflict with AirPlay on macOS)
echo -e "${GREEN}Starting Flask server on port 5001...${NC}"
export FLASK_APP=app/app.py
export FLASK_ENV=development
export FLASK_DEBUG=1

# Start in background
flask run --host=0.0.0.0 --port=5001 > ../backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../backend.pid

# Return to root directory
cd ..

# Wait for backend to be ready
echo -e "${YELLOW}Waiting for backend to start...${NC}"
for i in {1..15}; do
    if curl -s http://localhost:5001 > /dev/null; then
        echo -e "${GREEN}Backend is running!${NC}"
        break
    fi
    if [ $i -eq 15 ]; then
        echo -e "${YELLOW}Backend may not be fully initialized yet. Check backend.log for errors.${NC}"
    fi
    sleep 1
    echo -n "."
done
echo ""

# Step 4: Start Frontend (Angular)
echo -e "${YELLOW}Starting frontend server...${NC}"
cd frontend || exit 1

# Set up environment file for frontend
echo -e "${YELLOW}Setting up frontend environment file...${NC}"
echo "NG_APP_BASE_URL=http://localhost:5001" > .env

# Start Angular in a separate terminal
echo -e "${GREEN}Starting Angular server on port 4200...${NC}"
npm start > ../frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../frontend.pid

# Return to root directory
cd ..

# Wait for frontend to be ready
echo -e "${YELLOW}Waiting for frontend to start (this may take a minute)...${NC}"
for i in {1..60}; do
    if curl -s http://localhost:4200 > /dev/null; then
        echo -e "${GREEN}Frontend is running!${NC}"
        break
    fi
    if [ $i -eq 60 ]; then
        echo -e "${YELLOW}Frontend may not be fully initialized yet. Check frontend.log for errors.${NC}"
    fi
    sleep 1
    echo -n "."
done
echo ""

# Final status
echo -e "${BLUE}=== Application Status ===${NC}"
echo -e "${YELLOW}Backend:${NC} http://localhost:5001"
echo -e "${YELLOW}Frontend:${NC} http://localhost:4200"
echo -e "${YELLOW}MySQL:${NC} localhost:3306 (user: root, password: password)"
echo -e "${YELLOW}Backend log:${NC} backend.log"
echo -e "${YELLOW}Frontend log:${NC} frontend.log"
echo -e "${GREEN}Application should now be accessible at http://localhost:4200${NC}"
