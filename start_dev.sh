#!/bin/bash

# RSPi Development Mode Startup Script
# This script starts the RSPi application in development mode with authentication disabled
# for seamless local development and testing.

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BACKEND_PORT=5001
FRONTEND_PORT=4200
BACKEND_DIR="backend"
FRONTEND_DIR="frontend"
BACKEND_LOG="backend.log"
FRONTEND_LOG="frontend.log"
BACKEND_PID="backend.pid"
FRONTEND_PID="frontend.pid"

echo -e "${PURPLE}========================================${NC}"
echo -e "${PURPLE}  RSPi Development Mode Startup${NC}"
echo -e "${PURPLE}========================================${NC}"
echo -e "${YELLOW}🔧 Authentication: DISABLED (Development Mode)${NC}"
echo -e "${YELLOW}👤 User Context: dev-user (bypass)${NC}"
echo -e "${YELLOW}🔐 LDAP: Not required${NC}"
echo ""

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill process on port
kill_port() {
    local port=$1
    local service_name=$2
    echo -e "${YELLOW}⚠️  Port $port is in use. Stopping existing $service_name...${NC}"
    lsof -ti:$port | xargs kill -9 2>/dev/null || true
    sleep 2
}

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down RSPi Development Mode...${NC}"
    
    if [ -f "$BACKEND_PID" ]; then
        local backend_pid=$(cat "$BACKEND_PID")
        if kill -0 "$backend_pid" 2>/dev/null; then
            echo -e "${YELLOW}   Stopping backend (PID: $backend_pid)...${NC}"
            kill "$backend_pid" 2>/dev/null || true
        fi
        rm -f "$BACKEND_PID"
    fi
    
    if [ -f "$FRONTEND_PID" ]; then
        local frontend_pid=$(cat "$FRONTEND_PID")
        if kill -0 "$frontend_pid" 2>/dev/null; then
            echo -e "${YELLOW}   Stopping frontend (PID: $frontend_pid)...${NC}"
            kill "$frontend_pid" 2>/dev/null || true
        fi
        rm -f "$FRONTEND_PID"
    fi
    
    # Kill any remaining processes on our ports
    kill_port $BACKEND_PORT "backend"
    kill_port $FRONTEND_PORT "frontend"
    
    echo -e "${GREEN}✅ Cleanup complete${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

echo -e "${BLUE}🔍 Checking dependencies...${NC}"

# Check if Docker is running (for MySQL)
if ! docker info >/dev/null 2>&1; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Check MySQL container
echo -e "${BLUE}🗄️  Checking MySQL container...${NC}"
if ! docker ps | grep -q mysql; then
    echo -e "${YELLOW}⚠️  MySQL container not running. Starting...${NC}"
    if ! docker run -d --name mysql-rspi -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password -e MYSQL_DATABASE=mydb mysql:8.0 >/dev/null 2>&1; then
        echo -e "${RED}❌ Failed to start MySQL container${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ MySQL container started${NC}"
    sleep 5  # Wait for MySQL to initialize
else
    echo -e "${GREEN}✅ MySQL container is running${NC}"
fi

# Check for port conflicts
if check_port $BACKEND_PORT; then
    kill_port $BACKEND_PORT "backend"
fi

if check_port $FRONTEND_PORT; then
    kill_port $FRONTEND_PORT "frontend"
fi

echo -e "${BLUE}🚀 Starting RSPi Development Mode...${NC}"

# Setup backend
echo -e "${BLUE}📦 Setting up backend...${NC}"
cd "$BACKEND_DIR"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}⚠️  Virtual environment not found. Creating...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/update dependencies
echo -e "${BLUE}📥 Installing backend dependencies...${NC}"
pip install -q python-dotenv cryptography PyJWT ldap3 >/dev/null 2>&1 || true

# Setup development environment file
echo -e "${BLUE}⚙️  Configuring development environment...${NC}"
cat > .env.local << EOF
# Development Mode Configuration - Generated by start_dev.sh
CORS_ORIGINS=http://localhost:4200
LOGGING_CONFIG_PATH=logging.yaml

# Local database settings
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=mydb

# Authentication settings (Development Mode - DISABLED)
AUTH_ENABLED=false
JWT_SECRET_KEY=dev-secret-key-change-in-production
JWT_EXPIRATION_HOURS=8

# LDAP settings (not used in dev mode)
LDAP_HOST=SVEDC2000004PR.nbnco.local
LDAP_BASE_DN=DC=nbnco,DC=local

# Ollama configuration
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2
EOF

# Start backend server
echo -e "${BLUE}🔧 Starting backend server...${NC}"
nohup python app/app.py > "../$BACKEND_LOG" 2>&1 &
echo $! > "../$BACKEND_PID"
echo -e "${GREEN}✅ Backend server started on http://localhost:$BACKEND_PORT${NC}"

# Wait for backend to be ready
echo -e "${BLUE}⏳ Waiting for backend to be ready...${NC}"
for i in {1..30}; do
    if curl -s http://localhost:$BACKEND_PORT/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is ready!${NC}"
        break
    fi
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Backend failed to start within 30 seconds${NC}"
        cleanup
        exit 1
    fi
    sleep 1
    echo -n "."
done

cd ..

# Setup frontend
echo -e "${BLUE}📦 Setting up frontend...${NC}"
cd "$FRONTEND_DIR"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}⚠️  Node modules not found. Installing...${NC}"
    npm install >/dev/null 2>&1
fi

# Setup development environment file
echo -e "${BLUE}⚙️  Configuring frontend development environment...${NC}"
cat > .env << EOF
# Development Mode Configuration - Generated by start_dev.sh
NG_APP_BASE_URL=http://localhost:5001
NG_APP_AUTH_ENABLED=false
EOF

# Start frontend server
echo -e "${BLUE}🔧 Starting frontend server...${NC}"
nohup npm start > "../$FRONTEND_LOG" 2>&1 &
echo $! > "../$FRONTEND_PID"
echo -e "${GREEN}✅ Frontend server started on http://localhost:$FRONTEND_PORT${NC}"

# Wait for frontend to be ready
echo -e "${BLUE}⏳ Waiting for frontend to be ready...${NC}"
for i in {1..60}; do
    if curl -s http://localhost:$FRONTEND_PORT/ >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend is ready!${NC}"
        break
    fi
    if [ $i -eq 60 ]; then
        echo -e "${RED}❌ Frontend failed to start within 60 seconds${NC}"
        cleanup
        exit 1
    fi
    sleep 1
    if [ $((i % 10)) -eq 0 ]; then
        echo -n "."
    fi
done

cd ..

echo ""
echo -e "${GREEN}🎉 RSPi Development Mode Started Successfully!${NC}"
echo -e "${PURPLE}========================================${NC}"
echo -e "${CYAN}📱 Frontend: http://localhost:$FRONTEND_PORT${NC}"
echo -e "${CYAN}🔧 Backend:  http://localhost:$BACKEND_PORT${NC}"
echo -e "${YELLOW}👤 User:     dev-user (Development Mode)${NC}"
echo -e "${YELLOW}🔐 Auth:     DISABLED (No login required)${NC}"
echo -e "${PURPLE}========================================${NC}"
echo ""
echo -e "${BLUE}📋 Development Mode Features:${NC}"
echo -e "   • No authentication required"
echo -e "   • Direct access to all application features"
echo -e "   • User context shows 'dev-user (DEV)'"
echo -e "   • LDAP authentication bypassed"
echo -e "   • All API calls proceed without token validation"
echo ""
echo -e "${YELLOW}💡 To switch to production mode, use: ./start_prod.sh${NC}"
echo -e "${YELLOW}🛑 Press Ctrl+C to stop all services${NC}"
echo ""

# Open browser (optional)
if command -v open >/dev/null 2>&1; then
    echo -e "${BLUE}🌐 Opening application in browser...${NC}"
    sleep 2
    open "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1 &
fi

# Display logs
echo -e "${BLUE}📊 Displaying combined logs (press Ctrl+C to stop)...${NC}"
echo -e "${PURPLE}=== Combined Logs (Backend + Frontend) ===${NC}"
echo ""

# Follow logs until interrupted
tail -f "$BACKEND_LOG" "$FRONTEND_LOG" 2>/dev/null || cleanup
