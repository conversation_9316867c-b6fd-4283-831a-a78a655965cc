import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';

import { CioRspExecutiveDigitalUsageComponent } from '../cio-rsp-executive-digital-usage/cio-rsp-executive-digital-usage.component';

@Component({
  selector: 'app-cio-rsp-executive',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    CioRspExecutiveDigitalUsageComponent
  ],
  templateUrl: './cio-rsp-executive.component.html',
  styleUrl: './cio-rsp-executive.component.scss'
})
export class CioRspExecutiveComponent {
  // This component now serves as a simple container for the digital usage component
}
