import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgCharts } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IRSPAPIAdoption } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-rsp-adoption',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgCharts,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-rsp-adoption.component.html',
  styleUrl: './cio-rsp-executive-rsp-adoption.component.scss'
})
export class CioRspExecutiveRspAdoptionComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  rspAdoptionData: IRSPAPIAdoption[] = [];

  // Chart options - Initialize with empty defaults to prevent undefined errors
  options: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'APIName', headerName: 'API Name', sortable: true, filter: true },
    { field: 'CertCount', headerName: 'Certified RSPs', sortable: true, filter: true },
    { field: 'UtilCount', headerName: 'Utilizing RSPs', sortable: true, filter: true },
    {
      field: 'UtilizationRate',
      headerName: 'Utilization Rate',
      sortable: true,
      filter: true,
      valueGetter: params => {
        if (params.data.CertCount > 0) {
          return (params.data.UtilCount / params.data.CertCount) * 100;
        }
        return 0;
      },
      valueFormatter: params => {
        const value = parseFloat(params.value);
        return !isNaN(value) ? `${value.toFixed(1)}%` : '0%';
      }
    }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadRSPAdoptionData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadRSPAdoptionData();
    }
  }

  // Load RSP adoption data
  loadRSPAdoptionData(): void {
    this.cioRspExecutiveService.getRSPAPIAdoption(this.period).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.rspAdoptionData = response.data;
          this.generateChart();
        }
      },
      error: (error) => {
        console.error('Error loading RSP adoption data:', error);
      }
    });
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating RSP adoption chart with data:', this.rspAdoptionData);

    if (!this.rspAdoptionData || this.rspAdoptionData.length === 0) {
      console.warn('No RSP adoption data available for chart');
      return;
    }

    // Sort data by certified count
    const sortedData = [...this.rspAdoptionData].sort((a, b) => b.CertCount - a.CertCount);

    // Create enhanced chart configuration
    this.options = {
      title: {
        text: `RSP API Adoption & Utilisation (${this.period})`,
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'CertCount',
          yName: 'Certified RSPs',
          fill: '#ff6b6b',
          stroke: '#ff6b6b',
          strokeWidth: 1
        },
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'UtilCount',
          yName: 'Utilizing RSPs',
          fill: '#4ecdc4',
          stroke: '#4ecdc4',
          strokeWidth: 1
        }
      ],
      legend: {
        position: 'right',
        spacing: 20
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: {
            text: 'API Name',
            fontSize: 12,
            fontWeight: 'bold'
          },
          label: {
            rotation: 45
          }
        },
        {
          type: 'number',
          position: 'left',
          title: {
            text: 'Number of RSPs',
            fontSize: 12,
            fontWeight: 'bold'
          }
        }
      ],
      tooltip: {
        enabled: true,
        renderer: (params: any) => {
          const data = params.datum;
          const utilizationRate = data.CertCount > 0 ? ((data.UtilCount / data.CertCount) * 100).toFixed(1) : '0';
          return {
            content: `${data.APIName}<br/>${params.yName}: ${params.yValue}<br/>Utilization Rate: ${utilizationRate}%`
          };
        }
      }
    };

    console.log('RSP adoption chart options set:', this.options);
  }
}
