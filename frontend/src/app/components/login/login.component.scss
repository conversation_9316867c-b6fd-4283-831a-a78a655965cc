// Login Component Styles - Professional Design
// Modern, clean, and responsive login interface

// Global box-sizing for all elements
* {
  box-sizing: border-box;
}

// Color Variables
$primary-color: #673ab7;
$primary-light: #9c27b0;
$primary-dark: #512da8;
$accent-color: #ff4081;
$background-gradient-start: #667eea;
$background-gradient-end: #764ba2;
$card-background: #ffffff;
$text-primary: #212121;
$text-secondary: #757575;
$text-hint: #9e9e9e;
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;
$dev-mode-color: #2196f3;
$prod-mode-color: #4caf50;

// Main Container
.login-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, $background-gradient-start 0%, $background-gradient-end 100%);
  padding: 20px;
  overflow: hidden;

  // Animated background shapes
  .background-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.shape-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.shape-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }
}

// Floating animation
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

// Login Wrapper
.login-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  box-sizing: border-box;
}

// Login Card
.login-card {
  background: $card-background;
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 12px 24px rgba(0, 0, 0, 0.08);
  }
}

// Header Section
.login-header {
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  color: white;
  padding: 28px 28px 20px;
  text-align: center;
  box-sizing: border-box;

  .logo-section {
    margin-bottom: 16px;

    .logo-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      margin-bottom: 16px;

      .main-icon {
        font-size: 32px;
        width: 32px;
        height: 32px;
      }
    }

    .app-title {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      letter-spacing: -0.5px;
    }

    .app-subtitle {
      margin: 0;
      font-size: 14px;
      opacity: 0.9;
      font-weight: 400;
    }
  }

  .auth-mode-indicator {
    mat-chip-set {
      justify-content: center;
    }

    mat-chip {
      font-size: 12px;
      font-weight: 500;
      border-radius: 16px;
      
      &.development-mode {
        background-color: $dev-mode-color;
        color: white;
      }

      &.production-mode {
        background-color: $prod-mode-color;
        color: white;
      }

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }
}

// Content Section
.login-content {
  padding: 28px 28px 20px;
  width: 100%;
  box-sizing: border-box;

  .form-header {
    text-align: center;
    margin-bottom: 32px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
    }

    .form-subtitle {
      margin: 0;
      font-size: 14px;
      color: $text-secondary;
      line-height: 1.4;
    }
  }
}

// Form Styles
.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;

  .form-field {
    width: 100%;
    box-sizing: border-box;

    .mat-mdc-form-field {
      width: 100%;
      box-sizing: border-box;
    }

    .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
      width: 100%;
      box-sizing: border-box;
    }

    .form-input {
      font-size: 16px;
      padding: 14px 16px;
      width: 100%;
      box-sizing: border-box;
    }

    .field-icon {
      color: $text-hint;
    }

    .password-toggle {
      color: $text-hint;
      
      &:hover {
        color: $primary-color;
      }
    }

    // Error state styling
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        color: $error-color;
      }
    }
  }

  .login-actions {
    margin-top: 16px;

    .login-button {
      width: 100%;
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 12px;
      background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
      box-shadow: 0 4px 12px rgba($primary-color, 0.3);
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba($primary-color, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      .button-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .login-spinner {
          width: 20px;
          height: 20px;
        }

        .login-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }

        .button-text {
          font-weight: 600;
        }
      }
    }
  }
}

// Footer Section
.login-footer {
  padding: 20px 28px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e0;
  box-sizing: border-box;

  .dev-notice, .prod-notice {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 16px;

    .notice-icon {
      font-size: 20px;
      width: 20px;
      height: 20px;
      margin-top: 2px;
    }

    .notice-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;

      strong {
        font-size: 14px;
        font-weight: 600;
      }

      span {
        font-size: 12px;
        opacity: 0.8;
      }
    }
  }

  .dev-notice {
    background-color: #e3f2fd;
    color: #1565c0;
    border: 1px solid #bbdefb;
  }

  .prod-notice {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
  }

  .footer-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: $text-hint;

    .copyright {
      font-weight: 500;
    }

    .version {
      font-weight: 400;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .login-container {
    padding: 16px;
  }

  .login-wrapper {
    max-width: 100%;
  }

  .login-header {
    padding: 24px 24px 20px;

    .logo-section {
      .logo-icon {
        width: 56px;
        height: 56px;

        .main-icon {
          font-size: 28px;
          width: 28px;
          height: 28px;
        }
      }

      .app-title {
        font-size: 24px;
      }
    }
  }

  .login-content {
    padding: 24px 24px 20px;

    .form-header {
      margin-bottom: 24px;

      h2 {
        font-size: 20px;
      }
    }
  }

  .login-footer {
    padding: 20px 24px;

    .footer-links {
      flex-direction: column;
      gap: 8px;
      text-align: center;
    }
  }
}

@media (max-width: 480px) {
  .background-shapes .shape {
    display: none; // Hide background shapes on very small screens
  }

  .login-form {
    gap: 16px;

    .login-actions .login-button {
      height: 48px;
      font-size: 14px;
    }
  }
}
