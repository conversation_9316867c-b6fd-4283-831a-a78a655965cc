<div class="login-container">
  <!-- Background Elements -->
  <div class="background-shapes">
    <div class="shape shape-1"></div>
    <div class="shape shape-2"></div>
    <div class="shape shape-3"></div>
  </div>

  <!-- Login Card -->
  <div class="login-wrapper">
    <mat-card class="login-card">
      <!-- Header Section -->
      <div class="login-header">
        <div class="logo-section">
          <div class="logo-icon">
            <mat-icon class="main-icon">business</mat-icon>
          </div>
          <h1 class="app-title">RSP Tracker</h1>
          <p class="app-subtitle">Retail Service Provider Management</p>
        </div>
        
        <div class="auth-mode-indicator">
          <mat-chip-set>
            <mat-chip [class]="environment.auth_enabled ? 'production-mode' : 'development-mode'">
              <mat-icon>{{ environment.auth_enabled ? 'security' : 'code' }}</mat-icon>
              {{ environment.auth_enabled ? 'Production Mode' : 'Development Mode' }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <!-- Login Form Section -->
      <mat-card-content class="login-content">
        <div class="form-header">
          <h2>Welcome Back</h2>
          <p class="form-subtitle">
            {{ environment.auth_enabled 
              ? 'Please sign in with your NBNCO credentials' 
              : 'Development mode - any credentials will work' }}
          </p>
        </div>

        <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
          <!-- Username Field -->
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Username</mat-label>
            <input 
              matInput 
              formControlName="username" 
              placeholder="Enter your username"
              [disabled]="loading"
              autocomplete="username"
              class="form-input">
            <mat-icon matSuffix class="field-icon">person</mat-icon>
            <mat-error *ngIf="loginForm.get('username')?.hasError('required')">
              Username is required
            </mat-error>
          </mat-form-field>

          <!-- Password Field -->
          <mat-form-field appearance="outline" class="form-field">
            <mat-label>Password</mat-label>
            <input 
              matInput 
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password" 
              placeholder="Enter your password"
              [disabled]="loading"
              autocomplete="current-password"
              class="form-input">
            <button 
              mat-icon-button 
              matSuffix 
              type="button"
              (click)="hidePassword = !hidePassword"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hidePassword"
              class="password-toggle">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
              Password is required
            </mat-error>
          </mat-form-field>

          <!-- Login Button -->
          <div class="login-actions">
            <button 
              mat-raised-button 
              color="primary" 
              type="submit" 
              [disabled]="loginForm.invalid || loading"
              class="login-button">
              <div class="button-content">
                <mat-spinner 
                  *ngIf="loading" 
                  diameter="20" 
                  class="login-spinner"
                  color="accent">
                </mat-spinner>
                <mat-icon *ngIf="!loading" class="login-icon">login</mat-icon>
                <span class="button-text">
                  {{ loading ? 'Signing in...' : 'Sign In' }}
                </span>
              </div>
            </button>
          </div>
        </form>
      </mat-card-content>

      <!-- Footer Section -->
      <mat-card-footer class="login-footer">
        <!-- Development Mode Notice -->
        <div *ngIf="!environment.auth_enabled" class="dev-notice">
          <mat-icon class="notice-icon">info</mat-icon>
          <div class="notice-content">
            <strong>Development Mode Active</strong>
            <span>Authentication is bypassed for local development</span>
          </div>
        </div>

        <!-- Production Mode Notice -->
        <div *ngIf="environment.auth_enabled" class="prod-notice">
          <mat-icon class="notice-icon">shield</mat-icon>
          <div class="notice-content">
            <strong>Secure Authentication</strong>
            <span>Connected to NBNCO Active Directory</span>
          </div>
        </div>

        <!-- Footer Links -->
        <div class="footer-links">
          <span class="copyright">© 2025 NBNCO Limited</span>
          <span class="version">RSP Tracker v2.0</span>
        </div>
      </mat-card-footer>
    </mat-card>
  </div>
</div>
