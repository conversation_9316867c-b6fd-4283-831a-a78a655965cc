import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { AgCharts } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';
import { ColDef } from 'ag-grid-community';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { IDigitalServiceUsage } from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-service-usage',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    AgCharts,
    AgGridAngular
  ],
  templateUrl: './cio-rsp-executive-service-usage.component.html',
  styleUrl: './cio-rsp-executive-service-usage.component.scss'
})
export class CioRspExecutiveServiceUsageComponent implements OnInit, OnChanges {
  @Input() period: string = '';

  // Data for the chart
  serviceUsageData: IDigitalServiceUsage[] = [];

  // Chart options - Initialize with empty defaults to prevent undefined errors
  options: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  // AG Grid column definitions
  columnDefs: ColDef[] = [
    { field: 'ServiceName', headerName: 'Service Name', sortable: true, filter: true },
    {
      field: 'TotalAPITxns',
      headerName: 'API Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => {
        const value = parseInt(params.value);
        return !isNaN(value) ? value.toLocaleString() : '0';
      }
    },
    {
      field: 'TotalPortalTxns',
      headerName: 'Portal Transactions',
      sortable: true,
      filter: true,
      valueFormatter: params => {
        const value = parseInt(params.value);
        return !isNaN(value) ? value.toLocaleString() : '0';
      }
    },
    {
      field: 'APIPercentage',
      headerName: 'API %',
      sortable: true,
      filter: true,
      valueFormatter: params => {
        const value = parseFloat(params.value);
        return !isNaN(value) ? `${value.toFixed(1)}%` : '0%';
      }
    }
  ];

  // AG Grid default column definitions
  defaultColDef: ColDef = {
    flex: 1,
    minWidth: 100,
    resizable: true
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor(private cioRspExecutiveService: CioRspExecutiveService) { }

  ngOnInit(): void {
    if (this.period) {
      this.loadServiceUsageData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['period'] && !changes['period'].firstChange) {
      this.loadServiceUsageData();
    }
  }

  // Load service usage data
  loadServiceUsageData(): void {
    this.cioRspExecutiveService.getDigitalServiceUsage(this.period).subscribe({
      next: (response) => {
        if (response && response.data) {
          // Convert string values to numbers for chart compatibility
          this.serviceUsageData = response.data.map((item: any) => ({
            ...item,
            TotalAPITxns: parseInt(item.TotalAPITxns) || 0,
            TotalPortalTxns: parseInt(item.TotalPortalTxns) || 0,
            TotalTxns: parseInt(item.TotalTxns) || 0
          }));
          console.log('Processed service usage data:', this.serviceUsageData);
          this.generateChart();
        }
      },
      error: (error) => {
        console.error('Error loading service usage data:', error);
      }
    });
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating service usage chart with data:', this.serviceUsageData);

    if (!this.serviceUsageData || this.serviceUsageData.length === 0) {
      console.warn('No service usage data available for chart');
      return;
    }

    // Sort data by total transactions
    const sortedData = [...this.serviceUsageData].sort((a, b) =>
      (b.TotalAPITxns + b.TotalPortalTxns) - (a.TotalAPITxns + a.TotalPortalTxns)
    );

    // Create simple chart configuration
    this.options = {
      data: sortedData,
      series: [
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions'
        },
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom'
        },
        {
          type: 'number',
          position: 'left'
        }
      ]
    };

    console.log('Service usage chart options set:', this.options);
  }

  // Format numbers for display
  formatNumber(value: number): string {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K';
    }
    return value.toString();
  }
}
