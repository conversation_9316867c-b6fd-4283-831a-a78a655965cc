// Page container with proper spacing from navigation
:host {
  display: block;
  padding: 24px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

// Professional page header styling
.page-header {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;

  // Header content container
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80px;

  .header-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;

    h1 {
      margin: 0;
      color: #1a1a1a;
      font-size: 32px;
      font-weight: 700;
      line-height: 1.2;
      letter-spacing: -0.5px;
    }

    // Professional subtitle styling
    .header-subtitle {
      color: #6c757d;
      font-size: 16px;
      font-weight: 400;
      margin: 0;
      line-height: 1.4;
      opacity: 0.9;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;
  }
}

// Professional header controls container
.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;

  .main-period-selector {
    .period-selector-field {
      min-width: 200px;

      // Professional Material Design styling
      ::ng-deep {
        .mat-mdc-form-field-subscript-wrapper {
          display: none;
        }

        .mat-mdc-text-field-wrapper {
          background-color: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #dee2e6;
          transition: all 0.2s ease;

          &:hover {
            border-color: #1976d2;
            background-color: #ffffff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
          }
        }

        .mat-mdc-form-field-focus-overlay {
          background-color: transparent;
        }

        .mat-mdc-select-value {
          color: #1a1a1a;
          font-weight: 500;
          font-size: 14px;
        }

        .mat-mdc-form-field-label {
          color: #6c757d;
          font-weight: 400;
          font-size: 14px;
        }

        // Focused state
        &.mat-focused {
          .mat-mdc-text-field-wrapper {
            border-color: #1976d2;
            background-color: #ffffff;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
          }

          .mat-mdc-form-field-label {
            color: #1976d2;
          }
        }
      }
    }
  }
}

// Professional document export buttons
.document-export-buttons {
  display: flex;
  gap: 12px;
  align-items: center;

  .header-export-button {
    min-width: 130px;
    height: 44px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    text-transform: none;
    letter-spacing: 0.25px;

    // Primary button (PowerPoint)
    &.mat-mdc-raised-button.mat-primary {
      background-color: #1976d2;
      color: white;
      box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);

      &:hover:not(:disabled) {
        background-color: #1565c0;
        box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
        transform: translateY(-1px);
      }
    }

    // Accent button (PDF)
    &.mat-mdc-raised-button.mat-accent {
      background-color: #dc3545;
      color: white;
      box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);

      &:hover:not(:disabled) {
        background-color: #c82333;
        box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
        transform: translateY(-1px);
      }
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// Document export styling (legacy - keep for other sections)
.document-export-container {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-top: 10px;

  h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 18px;
    font-weight: 500;
    text-align: center;
  }
}

// Header document export buttons (new compact style)
.header-controls .document-export-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

// Legacy document export buttons (for other sections)
.document-export-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.document-export-button {
  min-width: 180px;
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  mat-icon {
    margin-right: 8px;
    font-size: 20px;
  }
}

.export-progress-container {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.export-progress-bar {
  margin-bottom: 10px;
  height: 8px;
  border-radius: 4px;
}

.export-status {
  text-align: center;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

// Period comparison controls styling (persistent across tabs)
.period-comparison-section {
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  h4 {
    margin: 0 0 12px 0;
    color: #1976d2;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .comparison-selectors {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    align-items: center;

    .period-selector {
      min-width: 160px;
      flex: 1;
      max-width: 200px;
    }

    .filter-info {
      color: #666;
      font-size: 13px;
      font-style: italic;
      margin-left: 16px;
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 6px;

      .period-count {
        color: #1976d2;
        font-weight: 500;
        font-style: normal;
        background: #e3f2fd;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
      }
    }
  }
}

// Chart controls styling (more compact)
.chart-controls-container {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 8px 12px;
  margin-bottom: 8px;
  border: 1px solid #e9ecef;

  .controls-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    align-items: flex-start;
  }

  .chart-options-controls {
    flex: 1;
    min-width: 100%;

    h4 {
      margin: 0 0 6px 0;
      color: #495057;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }

  .options-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .options-checkboxes {
    display: flex;
    flex-direction: row;
    gap: 14px;
    flex-wrap: wrap;
    align-items: center;

    mat-checkbox {
      font-size: 12px;

      ::ng-deep .mdc-form-field {
        font-size: 12px;
      }

      ::ng-deep .mdc-checkbox {
        padding: 6px;
      }
    }

    .projection-controls {
      transition: opacity 0.3s ease;
      margin-left: 8px;

      .projection-selector {
        min-width: 120px;
        max-width: 140px;

        ::ng-deep .mat-mdc-form-field {
          font-size: 12px;
        }

        ::ng-deep .mat-mdc-form-field-infix {
          min-height: 40px;
        }

        ::ng-deep .mat-mdc-form-field-label {
          font-size: 12px;
        }
      }
    }
  }
}

// Professional export functionality styling
.export-buttons-container {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  align-items: center;
  flex-wrap: wrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

// Professional export button styling (consistent with header buttons)
.export-button {
  min-width: 140px;
  height: 40px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: 0.25px;

  // Primary export button styling for raised buttons
  &.mat-mdc-raised-button {
    background-color: #1976d2;
    color: white;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);

    &:hover:not(:disabled) {
      background-color: #1565c0;
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
      transform: translateY(-1px);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  mat-icon {
    margin-right: 8px;
    font-size: 18px;
  }
}

// Small export button variant
.small-export-button {
  min-width: 120px;
  height: 36px;
  font-size: 13px;

  mat-icon {
    font-size: 16px;
  }
}

// Export all button variant (success color)
.export-all-button {
  &.mat-mdc-raised-button {
    background-color: #388e3c;
    color: white;
    box-shadow: 0 2px 4px rgba(56, 142, 60, 0.3);
    margin-left: auto;

    &:hover:not(:disabled) {
      background-color: #2e7d32;
      box-shadow: 0 4px 8px rgba(56, 142, 60, 0.4);
      transform: translateY(-1px);
    }
  }
}

// Professional action button styling (for insights and analysis buttons)
.professional-action-button {
  min-width: 160px;
  height: 44px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.2s ease;
  text-transform: none;
  letter-spacing: 0.25px;

  // Primary action button styling
  &.mat-mdc-raised-button.mat-primary {
    background-color: #1976d2;
    color: white;
    box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);

    &:hover:not(:disabled) {
      background-color: #1565c0;
      box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
      transform: translateY(-1px);
    }
  }

  // Secondary action button styling
  &.mat-mdc-raised-button.mat-accent {
    background-color: #388e3c;
    color: white;
    box-shadow: 0 2px 4px rgba(56, 142, 60, 0.3);

    &:hover:not(:disabled) {
      background-color: #2e7d32;
      box-shadow: 0 4px 8px rgba(56, 142, 60, 0.4);
      transform: translateY(-1px);
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  mat-icon {
    margin-right: 8px;
    font-size: 18px;
  }
}

.service-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
    flex-grow: 1;
  }

  .export-buttons-container {
    margin-bottom: 0;
    background: transparent;
    padding: 0;
  }
}

// Success/Error snackbar styling
::ng-deep {
  .success-snackbar {
    background: #4caf50 !important;
    color: white !important;
  }

  .error-snackbar {
    background: #f44336 !important;
    color: white !important;
  }

  .info-snackbar {
    background: #2196f3 !important;
    color: white !important;
  }
}

.chart-options {
  margin-bottom: 20px;

  .option-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;

    .projection-months {
      width: 180px;
    }
  }
}

.chart-container {
  height: 500px;
  width: 100%;
  margin-bottom: 30px;
  display: block;
  overflow: visible;

  ag-charts {
    display: block;
    width: 100%;
    height: 100%;
    min-height: 500px;
  }
}

.comparison-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  h3 {
    margin-bottom: 20px;
    color: #0066cc;
  }

  .month-selection {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    mat-form-field {
      width: 200px;
    }
  }

  .key-insights {
    margin-top: 30px;

    .insights-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .insight-column {
        background-color: #f5f5f5;
        padding: 15px;
        border-radius: 5px;

        h4 {
          margin-top: 0;
          margin-bottom: 15px;
          color: #0066cc;
          font-size: 16px;
        }

        .insight-item {
          margin-bottom: 20px;

          h5 {
            margin-top: 0;
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
          }

          p {
            margin: 5px 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

.data-table-container {
  margin-top: 20px;
  overflow-x: auto;

  .data-table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #e0e0e0;
    }

    th {
      background-color: #f5f5f5;
      font-weight: 500;
    }

    tr:hover {
      background-color: #f9f9f9;
    }
  }
}

.insights-container {
  margin-top: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;

  h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0066cc;
  }

  .insights-actions {
    margin-bottom: 24px;
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 16px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .insights-content {
    .ai-insights {
      background-color: #e6f3ff;
      padding: 20px;
      border-radius: 10px;
      margin: 10px 0;
      line-height: 1.6;
      font-size: 14px;
    }
  }

  .placeholder-text {
    font-style: italic;
    color: #666;
  }
}

.month-comparison-section {
  margin-bottom: 20px;

  h3 {
    margin-bottom: 15px;
    color: #333;
  }

  .month-selection {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    mat-form-field {
      min-width: 200px;
    }
  }
}

.comparison-analysis {
  margin-top: 30px;

  h3 {
    margin-bottom: 20px;
    color: #333;
  }
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.insight-card {
  .metric-comparison, .channel-mix, .channel-shift {
    margin-bottom: 15px;

    h4 {
      margin-bottom: 8px;
      color: #555;
      font-size: 14px;
    }

    p {
      margin: 4px 0;
      font-size: 13px;
    }

    .change {
      font-weight: bold;

      &.positive {
        color: #4caf50;
      }

      &.negative {
        color: #f44336;
      }
    }
  }
}

// Key Insights Tab Styles
.key-insights-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.insights-section {
  margin-bottom: 40px;
  background: #fafafa;
  border-radius: 8px;
  padding: 24px;
  border-left: 4px solid #1976d2;

  h3 {
    margin: 0 0 8px 0;
    color: #1976d2;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .section-description {
    margin: 0 0 20px 0;
    color: #666;
    font-size: 14px;
    font-style: italic;
  }
}

// Month Comparison Analysis Styles
.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.comparison-card {
  background: white;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }
}

.metric-comparison {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  strong {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
  }

  .comparison-values {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 6px;

    span {
      font-size: 13px;
      color: #666;
    }
  }

  .comparison-change {
    font-size: 13px;
    font-weight: 600;

    &.positive {
      color: #4caf50;
    }

    &.negative {
      color: #f44336;
    }
  }
}

.channel-mix {
  .mix-comparison {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-bottom: 8px;

    span {
      font-size: 13px;
      color: #666;
    }
  }

  .channel-shift {
    font-size: 13px;
    font-weight: 600;

    &.positive {
      color: #4caf50;
    }

    &.negative {
      color: #f44336;
    }
  }
}

// Observations and Insights Lists
.observations-list,
.performance-list,
.analytics-list,
.channel-mix-insights,
.summary-insights {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.observation-item,
.performance-item,
.analytics-item,
.channel-insight-item,
.summary-insight-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 16px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-left: 3px solid #e0e0e0;

  mat-icon {
    color: #1976d2;
    font-size: 20px;
    width: 20px;
    height: 20px;
    margin-top: 2px;
    flex-shrink: 0;
  }

  span {
    font-size: 14px;
    line-height: 1.5;
    color: #333;
  }

  &:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-left-color: #1976d2;
  }
}

// Channel Mix Table Styles
.channel-mix-table-container {
  margin-top: 20px;
  background: white;
  border-radius: 6px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
  }
}

.channel-mix-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;

  th {
    background: #f5f5f5;
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #ddd;
  }

  td {
    padding: 10px 8px;
    border-bottom: 1px solid #eee;
    color: #666;

    &:first-child {
      font-weight: 600;
      color: #333;
    }
  }

  tr:hover {
    background: #f9f9f9;
  }

  // Variance cell styling for colored backgrounds
  .variance-cell {
    font-weight: 600;
    text-align: center;
    border-radius: 4px;
    padding: 8px 12px !important;
    position: relative;

    // Add subtle border for better definition
    border: 1px solid rgba(0, 0, 0, 0.1);

    // Ensure text is always readable
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.3);

    // Smooth transition for hover effects
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      border-color: rgba(0, 0, 0, 0.3);
    }
  }
}

// Professional styling for variance colors (legacy - kept for compatibility)
.variance-positive {
  color: #4caf50;
  font-weight: 600;
}

.variance-negative {
  color: #f44336;
  font-weight: 600;
}

.variance-neutral {
  color: #666;
}

// Professional main section styling with consistent spacing
.main-section {
  margin-bottom: 32px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e0e0;

  mat-card-header {
    background-color: #f8f9fa;
    margin: -24px -24px 24px -24px;
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;

    mat-card-title {
      color: #1976d2;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      letter-spacing: -0.25px;
    }
  }

  mat-card-content {
    padding: 24px;
  }
}

.period-selection {
  margin-bottom: 20px;

  mat-form-field {
    min-width: 250px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;

  mat-spinner {
    margin-bottom: 20px;
  }

  p {
    color: #666;
    font-style: italic;
  }
}

.service-type-charts {
  margin-top: 30px;

  h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
  }

  .charts-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;

    .chart-half {
      height: 350px;

      ag-charts {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.placeholder-section {
  text-align: center;
  padding: 60px 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  border: 2px dashed #ddd;

  .placeholder-text {
    font-size: 24px;
    color: #999;
    margin-bottom: 10px;
    font-weight: 500;
  }

  .placeholder-description {
    color: #666;
    font-style: italic;
    margin: 0;
  }
}

// Professional responsive design
@media (max-width: 768px) {
  :host {
    padding: 16px;
  }

  .page-header {
    padding: 20px;
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
    min-height: auto;

    .header-left {
      text-align: center;

      h1 {
        font-size: 28px;
        line-height: 1.3;
      }
    }

    .header-right {
      justify-content: center;
    }

    .header-controls {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      width: 100%;
      max-width: 400px;
      margin: 0 auto;

      .main-period-selector .period-selector-field {
        min-width: 100%;
      }

      .document-export-buttons {
        justify-content: center;
        gap: 12px;

        .header-export-button {
          flex: 1;
          min-width: 120px;
        }
      }
    }
  }

  .main-section {
    margin-bottom: 24px;

    mat-card-header {
      padding: 16px 20px;

      mat-card-title {
        font-size: 18px;
      }
    }

    mat-card-content {
      padding: 20px;
    }
  }

  .insights-actions {
    flex-direction: column;
    gap: 12px;

    .professional-action-button {
      width: 100%;
      min-width: auto;
    }
  }

  .export-buttons-container {
    flex-direction: column;
    gap: 8px;

    .export-button {
      width: 100%;
      min-width: auto;
    }

    .export-all-button {
      margin-left: 0;
      order: -1; // Move to top on mobile
    }
  }

  .insights-grid {
    grid-template-columns: 1fr;
  }

  .service-type-charts .charts-row {
    grid-template-columns: 1fr;
  }

  .month-selection {
    flex-direction: column;
    gap: 10px;
  }

  .chart-options .option-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  // Enhanced data table responsive styling
  .data-table-container {
    padding: 16px;

    .data-section {
      padding: 16px;
      margin-bottom: 24px;

      h3 {
        font-size: 16px;
      }
    }

    .data-table {
      font-size: 12px;

      th, td {
        padding: 8px 12px;
      }

      // Stack table on very small screens
      @media (max-width: 480px) {
        th, td {
          padding: 6px 8px;
          font-size: 11px;
        }
      }
    }

    .summary-grid {
      grid-template-columns: 1fr;
      gap: 12px;

      .summary-card {
        padding: 12px;
      }
    }
  }
}

// Enhanced data table styling
.data-table-container {
  padding: 24px;

  .data-section {
    margin-bottom: 40px;
    background: #ffffff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e0e0e0;

    h3 {
      color: #1976d2;
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .section-description {
      color: #666;
      font-size: 14px;
      margin: 0 0 20px 0;
      font-style: italic;
    }
  }

  .data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;

    th {
      position: sticky;
      top: 0;
      background-color: #f8f9fa;
      z-index: 1;
      padding: 12px 16px;
      text-align: left;
      border-bottom: 2px solid #e0e0e0;
      font-weight: 600;
      color: #333;
      font-size: 14px;
    }

    td {
      padding: 10px 16px;
      border-bottom: 1px solid #e0e0e0;
      font-size: 13px;
      white-space: nowrap;
    }

    // Alternate row colors
    tbody tr:nth-child(even) {
      background-color: #fafafa;
    }

    tbody tr:hover {
      background-color: #e3f2fd;
    }

    // Special styling for projection rows
    .projection-row {
      background-color: #f0f8ff;
      border-left: 4px solid #1976d2;

      &:hover {
        background-color: #e6f3ff;
      }
    }

    // Growth rate styling
    .positive-growth {
      color: #2e7d32;
      font-weight: 600;

      &::before {
        content: '+';
      }
    }

    .negative-growth {
      color: #d32f2f;
      font-weight: 600;
    }

    // Confidence interval styling
    .confidence-range {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      color: #666;
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 4px 8px;
    }
  }

  // Summary grid styling
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;

    .summary-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      border-left: 4px solid #1976d2;

      h4 {
        margin: 0 0 8px 0;
        color: #1976d2;
        font-size: 14px;
        font-weight: 600;
      }

      p {
        margin: 4px 0;
        font-size: 13px;
        color: #666;

        strong {
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
}

// Tab group styling
mat-tab-group {
  margin-top: 20px;

  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-body-wrapper {
    padding-top: 20px;
  }
}

// Key Observations Section
.key-observations-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #0066cc;

  h3 {
    color: #0066cc;
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 600;
  }

  .observations-list {
    .observation-item {
      margin-bottom: 10px;
      padding: 8px 12px;
      background-color: white;
      border-radius: 4px;
      border-left: 3px solid #28a745;
      font-size: 14px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Advanced Analytics Section
.advanced-analytics-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  h3 {
    color: #0066cc;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }

  .analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;

    .analytics-column {
      h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #0066cc;
        padding-bottom: 5px;
      }

      h5 {
        color: #555;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 500;
      }

      .metric-group, .growth-metrics {
        background-color: white;
        padding: 15px;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }

      .daily-metrics, .growth-metrics {
        .metric-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          padding: 4px 0;

          &:last-child {
            margin-bottom: 0;
          }

          .metric-label {
            font-weight: 600;
            color: #555;
            min-width: 60px;
          }

          .metric-value {
            font-family: 'Courier New', monospace;
            font-size: 13px;

            &.positive {
              color: #28a745;
              font-weight: 600;
            }

            &.negative {
              color: #dc3545;
              font-weight: 600;
            }
          }
        }
      }
    }
  }
}

// Relative Performance Section
.relative-performance-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  h3 {
    color: #0066cc;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
  }

  .performance-table-container {
    background-color: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;

    .performance-table {
      width: 100%;
      border-collapse: collapse;

      th, td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
      }

      .growth-rate-cell {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #000;
        text-align: center;
        padding: 8px 12px;
        border-radius: 4px;
      }
    }
  }

  .performance-insights {
    .performance-insight {
      margin-bottom: 8px;
      padding: 8px 12px;
      background-color: white;
      border-radius: 4px;
      border-left: 3px solid #ffc107;
      font-size: 14px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Channel Mix Trendline Section
.channel-mix-trendline-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;

  h3 {
    color: #0066cc;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
  }

  .section-description {
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
    text-align: center;
  }

  .trendline-analysis-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;

    .trendline-table-container {
      h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
      }

      .channel-mix-table {
        width: 100%;
        background-color: white;
        border-radius: 6px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-collapse: collapse;

        th, td {
          padding: 10px 12px;
          text-align: center;
          border-bottom: 1px solid #e0e0e0;
          font-size: 13px;
        }

        th {
          background-color: #f5f5f5;
          font-weight: 600;
          color: #333;
        }

        .variance-cell {
          font-family: 'Courier New', monospace;
          font-weight: 600;
          color: #000;
          text-align: center;
          border-radius: 4px;
        }
      }
    }

    .trendline-insights {
      h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 16px;
        font-weight: 600;
      }

      .mix-insight {
        margin-bottom: 10px;
        padding: 10px 12px;
        background-color: white;
        border-radius: 4px;
        border-left: 3px solid #17a2b8;
        font-size: 14px;
        line-height: 1.4;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Key Insights Summary Section
.key-insights-summary {
  margin-top: 30px;
  padding: 20px;
  background-color: #e8f4fd;
  border-radius: 8px;
  border: 2px solid #0066cc;

  h3 {
    color: #0066cc;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }

  .insights-summary-list {
    .summary-insight {
      margin-bottom: 12px;
      padding: 12px 16px;
      background-color: white;
      border-radius: 6px;
      border-left: 4px solid #0066cc;
      font-size: 14px;
      line-height: 1.5;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      &:last-child {
        margin-bottom: 0;
      }
    }

    .no-insights {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 20px;
      background-color: white;
      border-radius: 6px;
    }
  }
}

// Enhanced responsive design for new sections
@media (max-width: 768px) {
  .advanced-analytics-section .analytics-grid {
    grid-template-columns: 1fr;
  }

  .channel-mix-trendline-section .trendline-analysis-grid {
    grid-template-columns: 1fr;
  }

  .channel-mix-table {
    font-size: 11px !important;

    th, td {
      padding: 6px 8px !important;
    }
  }
}

// Professional AI Insights styling
.insights-container {
  padding: 24px;

  h3 {
    color: #1976d2;
    margin-bottom: 24px;
    font-weight: 600;
    font-size: 20px;
    letter-spacing: -0.25px;
  }

  // Note: .insights-actions styling is defined above in the main section

  .insights-content {
    .loading-insights {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #0066cc;

      p {
        margin: 0;
        color: #666;
        font-style: italic;
      }
    }

    .ai-insights {
      background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
      border: 1px solid #b3d9ff;
      border-radius: 12px;
      padding: 24px;
      margin-top: 16px;
      box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);

      .insights-text {
        line-height: 1.6;
        color: #333;

        ::ng-deep {
          strong {
            color: #0066cc;
            font-weight: 600;
            font-size: 1.1em;
            display: block;
            margin: 16px 0 8px 0;
          }

          ul {
            margin: 8px 0 16px 0;
            padding-left: 0;
            list-style: none;

            li {
              margin: 6px 0;
              padding-left: 20px;
              position: relative;

              &:before {
                content: '▶';
                color: #0066cc;
                position: absolute;
                left: 0;
                top: 0;
              }
            }
          }

          br {
            line-height: 1.8;
          }
        }
      }
    }

    .insights-placeholder {
      text-align: center;
      padding: 40px 20px;
      color: #666;

      p {
        margin: 8px 0;

        &.insights-description {
          font-size: 0.9em;
          color: #888;
          font-style: italic;
        }
      }
    }
  }
}

// Advanced Statistical Forecasting Styles
.forecast-row {
  background-color: #f0f8ff;
  border-left: 4px solid #1976d2;

  td {
    font-weight: 500;
    color: #1565c0;
  }
}

.forecast-metadata {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
  }

  .metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .metadata-item {
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid #dee2e6;
      font-size: 14px;

      strong {
        color: #495057;
        font-weight: 600;
      }
    }
  }
}

.validation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;

  .validation-card {
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    h4 {
      margin: 0 0 16px 0;
      color: #495057;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 8px;
    }

    .metric-item {
      margin-bottom: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #f8f9fa;
      font-size: 14px;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      strong {
        color: #495057;
        font-weight: 600;
        display: inline-block;
        min-width: 200px;
      }

      .validation-pass {
        color: #28a745;
        font-weight: 600;
        background: #d4edda;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }

      .validation-fail {
        color: #dc3545;
        font-weight: 600;
        background: #f8d7da;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}

.model-metadata {
  margin-top: 20px;
  padding: 16px;
  background: #e3f2fd;
  border-radius: 8px;
  border: 1px solid #bbdefb;

  h4 {
    margin: 0 0 12px 0;
    color: #1565c0;
    font-size: 16px;
    font-weight: 600;
  }

  .metadata-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;

    .metadata-item {
      padding: 8px 12px;
      background: white;
      border-radius: 6px;
      border: 1px solid #90caf9;
      font-size: 14px;

      strong {
        color: #1565c0;
        font-weight: 600;
      }
    }
  }
}
