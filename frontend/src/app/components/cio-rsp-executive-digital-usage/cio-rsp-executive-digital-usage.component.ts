import { Component, OnInit, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { AgCharts } from 'ag-charts-angular';

// Document export libraries
import pptxgen from 'pptxgenjs';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

// Statistical analysis library
import * as ss from 'simple-statistics';

import { CioRspExecutiveService } from '../../services/cio-rsp-executive.service';
import { PresentationTemplateService } from '../../services/presentation-template.service';
import {
  IDigitalUsageHistory,
  IDigitalServiceUsage,
  IRSPAPIAdoption,
  IRSPDigitalUsage,
  IRSPAPIPercentage
} from '../../models/models';

@Component({
  selector: 'app-cio-rsp-executive-digital-usage',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatTabsModule,
    MatSelectModule,
    MatFormFieldModule,
    MatCheckboxModule,
    MatInputModule,
    MatButtonModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatExpansionModule,
    MatIconModule,
    MatSnackBarModule,
    MatMenuModule,
    MatProgressBarModule,
    AgCharts
  ],
  templateUrl: './cio-rsp-executive-digital-usage.component.html',
  styleUrl: './cio-rsp-executive-digital-usage.component.scss'
})
export class CioRspExecutiveDigitalUsageComponent implements OnInit, AfterViewInit {
  // Data for the chart
  digitalUsageData: IDigitalUsageHistory[] = [];

  // Chart options - Initialize with empty defaults to prevent undefined errors
  options: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  // Enhanced data for Data tab
  get enhancedDigitalUsageData(): any[] {
    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      return [];
    }

    // Get the prepared chart data which includes trends and projections
    return this.prepareChartData();
  }

  // Get only the raw historical data (no projections)
  get rawDigitalUsageData(): any[] {
    return this.enhancedDigitalUsageData.filter(item => !item.isProjection);
  }

  // Get only the trend data
  get trendData(): any[] {
    const data = this.rawDigitalUsageData;
    if (!this.showTrendlines || data.length === 0) {
      return [];
    }

    return data.map(item => ({
      Period: item.Period,
      TotalTxnsTrend: item.TotalTxnsTrend,
      TotalAPITxnsTrend: item.TotalAPITxnsTrend,
      TotalPortalTxnsTrend: item.TotalPortalTxnsTrend,
      // Calculate trend growth rates
      TotalTxnsTrendGrowth: this.calculateTrendGrowth(data, item.Period, 'TotalTxnsTrend'),
      TotalAPITxnsTrendGrowth: this.calculateTrendGrowth(data, item.Period, 'TotalAPITxnsTrend'),
      TotalPortalTxnsTrendGrowth: this.calculateTrendGrowth(data, item.Period, 'TotalPortalTxnsTrend')
    }));
  }

  // Get only the projection data
  get projectionData(): any[] {
    if (!this.showProjections) {
      return [];
    }

    const projections = this.enhancedDigitalUsageData.filter(item => item.isProjection);
    return projections.map(item => ({
      Period: item.Period,
      TotalTxns: item.TotalTxns,
      TotalAPITxns: item.TotalAPITxns,
      TotalPortalTxns: item.TotalPortalTxns,
      TotalTxnsUpper: item.TotalTxnsUpper,
      TotalTxnsLower: item.TotalTxnsLower,
      TotalAPITxnsUpper: item.TotalAPITxnsUpper,
      TotalAPITxnsLower: item.TotalAPITxnsLower,
      TotalPortalTxnsUpper: item.TotalPortalTxnsUpper,
      TotalPortalTxnsLower: item.TotalPortalTxnsLower,
      // Calculate projected growth from last actual period
      ProjectedGrowthTotal: this.calculateProjectedGrowth(item, 'TotalTxns'),
      ProjectedGrowthAPI: this.calculateProjectedGrowth(item, 'TotalAPITxns'),
      ProjectedGrowthPortal: this.calculateProjectedGrowth(item, 'TotalPortalTxns')
    }));
  }

  // Digital Usage History advanced controls
  selectedMonth1: string = '';
  selectedMonth2: string = '';
  showTrendlines: boolean = true;
  showProjections: boolean = true;
  projectionMonths: number = 6;
  showConfidenceInterval: boolean = true;

  // Advanced Statistical Forecasting controls
  showHoltWinters: boolean = false;

  // Tab index
  selectedTabIndex: number = 0;

  // GenAI Insights
  generatedInsights: string = '';
  isGeneratingInsights: boolean = false;

  // Statistical Analysis
  statisticalAnalysis: string = '';
  isGeneratingStatisticalAnalysis: boolean = false;
  // New properties for additional features
  availablePeriods: string[] = [];
  selectedPeriod: string = ''; // Keep for backward compatibility

  // Main period filtering
  mainSelectedPeriod: string = '';

  // Date range filtering properties
  dateRangeStart: string = '';
  dateRangeEnd: string = '';
  isDateRangeActive: boolean = false;

  // Filtered data properties
  filteredDigitalUsageData: any[] = [];
  filteredServiceUsageData: any[] = [];
  filteredRspAdoptionData: any[] = [];
  filteredRspUsageData: any[] = [];
  filteredRspPercentageData: any[] = [];

  // Raw data storage for insights (separate from getter)
  storedRawDigitalUsageData: any[] = [];

  // Service usage data and charts
  serviceUsageData: IDigitalServiceUsage[] = [];
  serviceUsageOptions: any = {};
  connectServicesOptions: any = {};
  assureServicesOptions: any = {};

  // RSP API adoption data and charts
  rspApiAdoptionData: IRSPAPIAdoption[] = [];
  rspApiAdoptionOptions: any = {};

  // RSP digital usage data and charts
  rspDigitalUsageData: IRSPDigitalUsage[] = [];
  rspDigitalUsageOptions: any = {};

  // RSP API percentage data and charts
  rspApiPercentageData: IRSPAPIPercentage[] = [];
  rspApiPercentageOptions: any = {};

  // Loading states
  isLoadingServiceUsage: boolean = false;
  isLoadingRspAdoption: boolean = false;
  isLoadingRspUsage: boolean = false;
  isLoadingRspPercentage: boolean = false;

  // Advanced analytics data
  advancedAnalytics: any = null;
  channelMixAnalysis: any = null;

  // Holt-Winters forecasting data
  holtWintersData: any[] = [];
  holtWintersMetadata: any = null;

  // Statistical validation data
  validationMetrics: any = null;
  modelMetadata: any = null;

  // Chart export properties
  isExporting: { [key: string]: boolean } = {};
  chartRefs: { [key: string]: any } = {};

  // Chart instances for export
  digitalUsageChart: any;
  serviceUsageChart: any;
  connectServicesChart: any;
  assureServicesChart: any;
  rspApiAdoptionChart: any;
  rspDigitalUsageChart: any;
  rspApiPercentageChart: any;

  // Document export properties
  isExportingDocument: boolean = false;
  exportProgress: number = 0;
  exportStatus: string = '';

  // Chart metadata for document export
  chartMetadata = [
    { type: 'digitalUsageHistory', title: 'Digital Usage History', description: 'Historical trends and projections' },
    { type: 'serviceUsage', title: 'Digital Usage By Service', description: 'Service breakdown analysis' },
    { type: 'connectServices', title: 'Connect Services API Usage', description: 'API usage percentage for Connect services' },
    { type: 'assureServices', title: 'Assure Services API Usage', description: 'API usage percentage for Assure services' },
    { type: 'rspApiAdoption', title: 'RSP API Adoption', description: 'API adoption metrics by RSP' },
    { type: 'rspDigitalUsage', title: 'Digital Usage By RSP', description: 'RSP digital usage breakdown' },
    { type: 'rspApiPercentage', title: 'RSP API Percentage', description: 'API percentage metrics by RSP' }
  ];

  constructor(
    private cioRspExecutiveService: CioRspExecutiveService,
    private snackBar: MatSnackBar,
    private templateService: PresentationTemplateService
  ) { }

  ngOnInit(): void {
    this.loadDigitalUsageData();
    this.loadAvailablePeriods();

    // Make test function available globally for debugging
    (window as any).testChartCapture = (chartType: string) => this.testChartCapture(chartType);
  }

  ngAfterViewInit(): void {
    // Initialize chart after view is initialized
  }

  // Load digital usage data
  loadDigitalUsageData(): void {
    // Pass date range parameters if active
    const startPeriod = this.isDateRangeActive ? this.dateRangeStart : undefined;
    const endPeriod = this.isDateRangeActive ? this.dateRangeEnd : undefined;

    this.cioRspExecutiveService.getDigitalUsageHistory(startPeriod, endPeriod).subscribe({
      next: (response) => {
        if (response && response.data) {
          // Convert string values to numbers for chart compatibility
          this.digitalUsageData = response.data.map((item: any) => ({
            ...item,
            TotalTxns: parseInt(item.TotalTxns) || 0,
            TotalAPITxns: parseInt(item.TotalAPITxns) || 0,
            TotalPortalTxns: parseInt(item.TotalPortalTxns) || 0
          }));

          console.log('Processed digital usage data:', this.digitalUsageData);

          // Store raw data for insights
          this.storedRawDigitalUsageData = [...this.digitalUsageData];

          // Initialize filtered data (starts with all data)
          this.filteredDigitalUsageData = [...this.digitalUsageData];

          // Set dynamic defaults for period comparison controls if available periods are loaded
          if (this.digitalUsageData.length > 0) {
            // If availablePeriods is already loaded, set dynamic defaults
            if (this.availablePeriods && this.availablePeriods.length > 0) {
              this.setDynamicPeriodDefaults();
            } else {
              // Fallback to data-based defaults if availablePeriods not yet loaded
              // Sort the actual data to get true chronological order
              const sortedData = [...this.digitalUsageData].sort((a, b) => {
                return this.comparePeriods(a.Period, b.Period);
              });

              const isInitialLoad = !this.selectedMonth1 && !this.selectedMonth2;
              if (isInitialLoad) {
                this.selectedMonth1 = sortedData[0].Period;
                this.selectedMonth2 = sortedData[sortedData.length - 1].Period;

                console.log('📊 Fallback period defaults set from data:', {
                  compareFrom: this.selectedMonth1,
                  compareTo: this.selectedMonth2,
                  totalRecords: sortedData.length
                });
              }
            }

            // Initialize date range filtering for other components (Key Insights, Data tab, etc.)
            this.updateDateRangeFilter();
            this.applyDateRangeFiltering();

            // Generate advanced analytics data (always available in Data tab)
            this.generateHoltWintersForecasts();
            this.generateStatisticalValidation();

            // Generate chart (filtering will be applied in prepareChartData based on selectedMonth1/2)
            this.generateChart();
          }
        }
      },
      error: (error) => {
        console.error('Error loading digital usage data:', error);
      }
    });
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating chart with data:', this.digitalUsageData);

    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      console.warn('No data available for chart');
      return;
    }

    // Prepare chart data with trendlines and projections
    const chartData = this.prepareChartData();

    // Create series based on configuration
    const series = this.createChartSeries(chartData);

    // Create enhanced chart configuration
    this.options = {
      title: {
        text: 'Digital Usage History - Trends & Projections',
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: chartData,
      series: series,
      legend: {
        position: 'bottom',
        spacing: 20
      },
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: {
            text: 'Period',
            fontSize: 12,
            fontWeight: 'bold'
          },
          label: {
            rotation: 45
          }
        },
        {
          type: 'number',
          position: 'left',
          title: {
            text: 'Number of Transactions',
            fontSize: 12,
            fontWeight: 'bold'
          },
          label: {
            formatter: (params: any) => this.formatNumber(params.value)
          }
        }
      ],
      tooltip: {
        enabled: true
      }
    };

    console.log('Enhanced chart options set:', this.options);
    console.log('Chart data with trendlines/projections:', chartData);
  }

  // Prepare chart data with trendlines and projections
  prepareChartData(): any[] {
    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      return [];
    }

    // Always start with all data, then apply period comparison filtering if both months are selected
    let dataToUse = [...this.digitalUsageData];

    // Apply period comparison filtering if both comparison months are selected
    if (this.selectedMonth1 && this.selectedMonth2) {
      const date1 = this.parseDate(this.selectedMonth1);
      const date2 = this.parseDate(this.selectedMonth2);
      const startDate = date1 <= date2 ? date1 : date2;
      const endDate = date1 <= date2 ? date2 : date1;

      dataToUse = this.digitalUsageData.filter(item => {
        const itemDate = this.parseDate(item.Period);
        return itemDate >= startDate && itemDate <= endDate;
      });

      console.log(`Filtering chart data from ${this.selectedMonth1} to ${this.selectedMonth2}:`, {
        originalCount: this.digitalUsageData.length,
        filteredCount: dataToUse.length,
        dateRange: `${startDate.toDateString()} to ${endDate.toDateString()}`,
        periods: dataToUse.map(item => item.Period).sort()
      });
    }

    // Sort data by period
    const sortedData = [...dataToUse].sort((a, b) => {
      return this.comparePeriods(a.Period, b.Period);
    });

    let filteredData = sortedData;

    let chartData = [...filteredData];

    // Add trendlines if enabled
    if (this.showTrendlines) {
      chartData = this.addTrendlineData(chartData);
    }

    // Add future projections if enabled
    if (this.showProjections && this.projectionMonths > 0) {
      chartData = this.addProjectionData(chartData);
    }

    // Note: Holt-Winters forecasts are handled as separate series, not added to main data

    return chartData;
  }

  // Create chart series based on configuration
  createChartSeries(chartData: any[]): any[] {
    const series: any[] = [];

    // Define colors matching the original
    const colors = {
      TotalTxns: '#0066cc',
      TotalAPITxns: '#ff0000',
      TotalPortalTxns: '#99ccff'
    };

    // Check if we have projection data
    const hasProjections = chartData.some(item => item.isProjection);

    // Main data series (actual data only)
    const actualData = chartData.filter(item => !item.isProjection);

    series.push({
      type: 'line',
      data: actualData,
      xKey: 'Period',
      yKey: 'TotalTxns',
      yName: 'Total Transactions',
      stroke: colors.TotalTxns,
      strokeWidth: 3,
      marker: {
        enabled: true,
        size: 6,
        fill: colors.TotalTxns
      }
    });

    series.push({
      type: 'line',
      data: actualData,
      xKey: 'Period',
      yKey: 'TotalAPITxns',
      yName: 'API Transactions',
      stroke: colors.TotalAPITxns,
      strokeWidth: 3,
      marker: {
        enabled: true,
        size: 6,
        fill: colors.TotalAPITxns
      }
    });

    series.push({
      type: 'line',
      data: actualData,
      xKey: 'Period',
      yKey: 'TotalPortalTxns',
      yName: 'Portal Transactions',
      stroke: colors.TotalPortalTxns,
      strokeWidth: 3,
      marker: {
        enabled: true,
        size: 6,
        fill: colors.TotalPortalTxns
      }
    });

    // Add trendline series if enabled
    if (this.showTrendlines) {
      series.push({
        type: 'line',
        data: actualData,
        xKey: 'Period',
        yKey: 'TotalTxnsTrend',
        yName: 'Total Trend',
        stroke: colors.TotalTxns,
        strokeWidth: 2,
        lineDash: [8, 4],
        lineDashOffset: 0,
        marker: {
          enabled: false
        }
      });

      series.push({
        type: 'line',
        data: actualData,
        xKey: 'Period',
        yKey: 'TotalAPITxnsTrend',
        yName: 'API Trend',
        stroke: colors.TotalAPITxns,
        strokeWidth: 2,
        lineDash: [8, 4],
        lineDashOffset: 0,
        marker: {
          enabled: false
        }
      });

      series.push({
        type: 'line',
        data: actualData,
        xKey: 'Period',
        yKey: 'TotalPortalTxnsTrend',
        yName: 'Portal Trend',
        stroke: colors.TotalPortalTxns,
        strokeWidth: 2,
        lineDash: [8, 4],
        lineDashOffset: 0,
        marker: {
          enabled: false
        }
      });
    }

    // Add projection series if enabled
    if (this.showProjections && hasProjections) {
      const projectionData = chartData.filter(item => item.isProjection);

      series.push({
        type: 'line',
        data: projectionData,
        xKey: 'Period',
        yKey: 'TotalTxns',
        yName: 'Total Projections',
        stroke: colors.TotalTxns,
        strokeWidth: 2,
        lineDash: [10, 5],
        strokeOpacity: 0.7,
        marker: {
          enabled: true,
          size: 4,
          fill: colors.TotalTxns,
          fillOpacity: 0.7
        }
      });

      series.push({
        type: 'line',
        data: projectionData,
        xKey: 'Period',
        yKey: 'TotalAPITxns',
        yName: 'API Projections',
        stroke: colors.TotalAPITxns,
        strokeWidth: 2,
        lineDash: [10, 5],
        strokeOpacity: 0.7,
        marker: {
          enabled: true,
          size: 4,
          fill: colors.TotalAPITxns,
          fillOpacity: 0.7
        }
      });

      series.push({
        type: 'line',
        data: projectionData,
        xKey: 'Period',
        yKey: 'TotalPortalTxns',
        yName: 'Portal Projections',
        stroke: colors.TotalPortalTxns,
        strokeWidth: 2,
        lineDash: [10, 5],
        strokeOpacity: 0.7,
        marker: {
          enabled: true,
          size: 4,
          fill: colors.TotalPortalTxns,
          fillOpacity: 0.7
        }
      });

      // Add confidence interval series if enabled
      if (this.showConfidenceInterval) {
        // Upper confidence bounds
        series.push({
          type: 'line',
          data: projectionData,
          xKey: 'Period',
          yKey: 'TotalTxnsUpper',
          yName: 'Total Upper CI',
          stroke: colors.TotalTxns,
          strokeWidth: 1,
          lineDash: [2, 2],
          strokeOpacity: 0.4,
          marker: {
            enabled: false
          }
        });

        // Lower confidence bounds
        series.push({
          type: 'line',
          data: projectionData,
          xKey: 'Period',
          yKey: 'TotalTxnsLower',
          yName: 'Total Lower CI',
          stroke: colors.TotalTxns,
          strokeWidth: 1,
          lineDash: [2, 2],
          strokeOpacity: 0.4,
          marker: {
            enabled: false
          }
        });
      }
    }

    // Add Holt-Winters forecast series if enabled
    if (this.showHoltWinters && this.holtWintersData.length > 0) {
      console.log('🔮 Creating Holt-Winters series with data:', this.holtWintersData);

      // Convert Holt-Winters data to chart format
      const holtWintersChartData = this.holtWintersData.map(item => ({
        Period: item.Period,
        HWTotalForecast: item.TotalForecast,
        HWAPIForecast: item.APIForecast,
        HWPortalForecast: item.PortalForecast,
        HWTotal80Lower: item.Total80Lower,
        HWTotal80Upper: item.Total80Upper,
        HWTotal95Lower: item.Total95Lower,
        HWTotal95Upper: item.Total95Upper
      }));
      // Main Holt-Winters forecast lines
      series.push({
        type: 'line',
        data: holtWintersChartData,
        xKey: 'Period',
        yKey: 'HWTotalForecast',
        yName: 'HW Total Forecast',
        stroke: colors.TotalTxns,
        strokeWidth: 3,
        lineDash: [12, 6],
        lineDashOffset: 0,
        marker: {
          enabled: true,
          size: 5,
          fill: colors.TotalTxns,
          shape: 'diamond'
        }
      });

      series.push({
        type: 'line',
        data: holtWintersChartData,
        xKey: 'Period',
        yKey: 'HWAPIForecast',
        yName: 'HW API Forecast',
        stroke: colors.TotalAPITxns,
        strokeWidth: 3,
        lineDash: [12, 6],
        lineDashOffset: 0,
        marker: {
          enabled: true,
          size: 5,
          fill: colors.TotalAPITxns,
          shape: 'diamond'
        }
      });

      series.push({
        type: 'line',
        data: holtWintersChartData,
        xKey: 'Period',
        yKey: 'HWPortalForecast',
        yName: 'HW Portal Forecast',
        stroke: colors.TotalPortalTxns,
        strokeWidth: 3,
        lineDash: [12, 6],
        lineDashOffset: 0,
        marker: {
          enabled: true,
          size: 5,
          fill: colors.TotalPortalTxns,
          shape: 'diamond'
        }
      });

      // 95% confidence bands for Holt-Winters
      series.push({
        type: 'line',
        data: holtWintersChartData,
        xKey: 'Period',
        yKey: 'HWTotal95Upper',
        yName: 'HW 95% Upper',
        stroke: colors.TotalTxns,
        strokeWidth: 1,
        lineDash: [3, 3],
        strokeOpacity: 0.3,
        marker: {
          enabled: false
        }
      });

      series.push({
        type: 'line',
        data: holtWintersChartData,
        xKey: 'Period',
        yKey: 'HWTotal95Lower',
        yName: 'HW 95% Lower',
        stroke: colors.TotalTxns,
        strokeWidth: 1,
        lineDash: [3, 3],
        strokeOpacity: 0.3,
        marker: {
          enabled: false
        }
      });
    }

    // Debug series configuration
    console.log('Generated chart series:', {
      totalSeries: series.length,
      trendlineCount: series.filter(s => s.yName?.includes('Trend')).length,
      holtWintersCount: series.filter(s => s.yName?.includes('HW')).length,
      trendlineConfig: series.filter(s => s.yName?.includes('Trend')).map(s => ({
        yName: s.yName,
        lineDash: s.lineDash,
        lineDashOffset: s.lineDashOffset,
        stroke: s.stroke,
        strokeWidth: s.strokeWidth
      }))
    });

    return series;
  }

  // Add trendline data to chart data
  addTrendlineData(data: any[]): any[] {
    if (data.length < 2) {
      return data;
    }

    // Calculate trendlines for each metric
    const trendData = this.calculateTrendlines(data);

    // Merge trendline data with original data
    return data.map((item, index) => ({
      ...item,
      TotalTxnsTrend: trendData.TotalTxns[index],
      TotalAPITxnsTrend: trendData.TotalAPITxns[index],
      TotalPortalTxnsTrend: trendData.TotalPortalTxns[index]
    }));
  }

  // Add projection data to chart data
  addProjectionData(data: any[]): any[] {
    if (data.length < 2 || this.projectionMonths <= 0) {
      return data;
    }

    // Calculate projections based on trend
    const projectionData = this.calculateProjections(data);

    // Combine original data with projections
    return [...data, ...projectionData];
  }

  // Note: Holt-Winters forecasts are now handled as separate series, not added to main data

  // Calculate future projections
  calculateProjections(data: any[]): any[] {
    const metrics = ['TotalTxns', 'TotalAPITxns', 'TotalPortalTxns'];
    const projections: any[] = [];

    // Get the last period to start projections from
    const lastPeriod = data[data.length - 1].Period;
    const lastDate = this.parseDate(lastPeriod);

    // Calculate trend slopes for each metric
    const trends: {[key: string]: number} = {};
    metrics.forEach(metric => {
      const values = data.map(d => d[metric]);
      const slope = this.calculateTrendSlope(values);
      trends[metric] = slope;
    });

    // Generate future periods
    for (let i = 1; i <= this.projectionMonths; i++) {
      const futureDate = new Date(lastDate);
      futureDate.setMonth(futureDate.getMonth() + i);
      const futurePeriod = this.formatPeriod(futureDate);

      // Calculate projected values
      const lastValues = data[data.length - 1];
      const projectedItem: any = {
        Period: futurePeriod,
        TotalTxns: Math.max(0, Math.round(lastValues['TotalTxns'] + (trends['TotalTxns'] * i))),
        TotalAPITxns: Math.max(0, Math.round(lastValues['TotalAPITxns'] + (trends['TotalAPITxns'] * i))),
        TotalPortalTxns: Math.max(0, Math.round(lastValues['TotalPortalTxns'] + (trends['TotalPortalTxns'] * i))),
        isProjection: true
      };

      // Add trend values if trendlines are enabled
      if (this.showTrendlines) {
        projectedItem.TotalTxnsTrend = projectedItem.TotalTxns;
        projectedItem.TotalAPITxnsTrend = projectedItem.TotalAPITxns;
        projectedItem.TotalPortalTxnsTrend = projectedItem.TotalPortalTxns;
      }

      // Add confidence intervals if enabled
      if (this.showConfidenceInterval) {
        const confidenceMargin = 0.15; // 15% confidence margin
        projectedItem.TotalTxnsUpper = Math.round(projectedItem.TotalTxns * (1 + confidenceMargin));
        projectedItem.TotalTxnsLower = Math.round(projectedItem.TotalTxns * (1 - confidenceMargin));
        projectedItem.TotalAPITxnsUpper = Math.round(projectedItem.TotalAPITxns * (1 + confidenceMargin));
        projectedItem.TotalAPITxnsLower = Math.round(projectedItem.TotalAPITxns * (1 - confidenceMargin));
        projectedItem.TotalPortalTxnsUpper = Math.round(projectedItem.TotalPortalTxns * (1 + confidenceMargin));
        projectedItem.TotalPortalTxnsLower = Math.round(projectedItem.TotalPortalTxns * (1 - confidenceMargin));
      }

      projections.push(projectedItem);
    }

    return projections;
  }

  // Calculate trend slope for projections
  calculateTrendSlope(values: number[]): number {
    const n = values.length;
    const x = Array.from({length: n}, (_, i) => i);

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  }

  // Parse period string to date
  parseDate(period: string): Date {
    const [month, year] = period.split('-');
    const monthMap: {[key: string]: number} = {
      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
    };
    return new Date(2000 + parseInt(year), monthMap[month]);
  }

  // Format date to period string
  formatPeriod(date: Date): string {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = (date.getFullYear() - 2000).toString().padStart(2, '0');
    return `${month}-${year}`;
  }

  // Calculate polynomial trendlines (degree 2)
  calculateTrendlines(data: any[]): any {
    const metrics = ['TotalTxns', 'TotalAPITxns', 'TotalPortalTxns'];
    const result: any = {};

    metrics.forEach(metric => {
      const values = data.map(d => d[metric]);
      const trendline = this.polynomialRegression(values, 2);
      result[metric] = trendline;
    });

    return result;
  }

  // Polynomial regression calculation
  polynomialRegression(values: number[], _degree: number): number[] {
    const n = values.length;
    const x = Array.from({length: n}, (_, i) => i);

    // For simplicity, using linear regression for now
    // In a full implementation, you'd use a proper polynomial regression library
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return x.map(xi => slope * xi + intercept);
  }

  // Compare periods for sorting
  comparePeriods(a: string, b: string): number {
    const parseDate = (period: string) => {
      const [month, year] = period.split('-');
      const monthMap: {[key: string]: number} = {
        'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6,
        'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12
      };
      return new Date(2000 + parseInt(year), monthMap[month] - 1);
    };

    return parseDate(a).getTime() - parseDate(b).getTime();
  }

  // Format numbers for display
  formatNumber(value: number): string {
    if (value >= 1000000) {
      return (value / 1000000).toFixed(1) + 'M';
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'K';
    }
    return value.toString();
  }

  // Calculate trend growth rate for a specific period
  calculateTrendGrowth(data: any[], period: string, metric: string): number {
    const currentIndex = data.findIndex(item => item.Period === period);
    if (currentIndex <= 0) return 0;

    const current = data[currentIndex][metric];
    const previous = data[currentIndex - 1][metric];

    if (!previous || previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  // Calculate projected growth from last actual period
  calculateProjectedGrowth(projectionItem: any, metric: string): number {
    const lastActualData = this.rawDigitalUsageData;
    if (lastActualData.length === 0) return 0;

    const lastActual = lastActualData[lastActualData.length - 1][metric];
    const projected = projectionItem[metric];

    if (!lastActual || lastActual === 0) return 0;
    return ((projected - lastActual) / lastActual) * 100;
  }

  // Handle chart option change
  onChartOptionChange(): void {
    console.log('🎯 Chart option change triggered:', {
      showHoltWinters: this.showHoltWinters,
      hasDigitalUsageData: !!this.digitalUsageData,
      dataLength: this.digitalUsageData?.length
    });

    // Update UI based on option changes
    if (!this.showProjections) {
      this.showConfidenceInterval = false;
    }

    // Generate advanced forecasts if enabled
    if (this.showHoltWinters) {
      console.log('🔮 Holt-Winters toggle enabled, generating forecasts...');
      this.generateHoltWintersForecasts();
      console.log('🔮 Holt-Winters data after generation:', this.holtWintersData);
    } else {
      console.log('🔮 Holt-Winters toggle disabled, clearing data...');
      this.holtWintersData = [];
      this.holtWintersMetadata = null;
    }

    // Always generate statistical validation (no longer controlled by checkbox)
    console.log('📊 Generating statistical validation...');
    this.generateStatisticalValidation();
    console.log('📊 Validation metrics after generation:', this.validationMetrics);

    // Regenerate the chart with new options
    this.generateChart();

    // Log the changes for debugging
    console.log('Chart options updated:', {
      showTrendlines: this.showTrendlines,
      showProjections: this.showProjections,
      projectionMonths: this.projectionMonths,
      showConfidenceInterval: this.showConfidenceInterval,
      showHoltWinters: this.showHoltWinters
    });

    // Debug trendlines specifically
    if (this.showTrendlines) {
      console.log('Trendlines enabled - checking data:', {
        hasData: !!this.digitalUsageData,
        dataLength: this.digitalUsageData?.length,
        sampleTrendData: this.digitalUsageData?.slice(0, 2)
      });
    }

    // Show user feedback for projection changes
    if (this.showProjections) {
      this.snackBar.open(
        `Chart updated with ${this.projectionMonths} month projections`,
        'Close',
        { duration: 2000 }
      );
    }
  }

  // Generate AI insights
  generateInsights(): void {
    this.isGeneratingInsights = true;
    this.generatedInsights = '';

    // Call the real AI insights service
    this.cioRspExecutiveService.generateAIInsights(this.digitalUsageData, null).subscribe({
      next: (response) => {
        if (response && response.data && response.data.insights) {
          this.generatedInsights = response.data.insights;
        } else {
          this.generatedInsights = 'No insights generated. Please try again.';
        }
        this.isGeneratingInsights = false;
      },
      error: (error) => {
        console.error('Error generating AI insights:', error);
        this.generatedInsights = 'Error generating insights. Please try again later.';
        this.isGeneratingInsights = false;
      }
    });
  }

  // Refresh insights
  refreshInsights(): void {
    this.generateInsights();
  }

  // Handle month selection change for Digital Usage History
  onMonthSelectionChange(): void {
    console.log('Month selection changed:', {
      selectedMonth1: this.selectedMonth1,
      selectedMonth2: this.selectedMonth2
    });

    // Validate that both months are selected for filtering
    if (!this.selectedMonth1 || !this.selectedMonth2) {
      console.warn('Both comparison months must be selected for filtering');
      // Still regenerate chart to show all data if one is missing
      this.generateChart();
      return;
    }

    // Update date range properties for other components
    this.updateDateRangeFilter();
    this.applyDateRangeFiltering();

    // Regenerate chart with new comparison months (filtering happens in prepareChartData)
    this.generateChart();

    // Regenerate other charts if period is selected
    if (this.mainSelectedPeriod) {
      this.generateServiceUsageCharts();
      this.generateRspApiAdoptionChart();
      this.generateRspDigitalUsageChart();
      this.generateRspApiPercentageChart();
    }

    // Show user feedback
    this.snackBar.open(
      `Chart updated to show data from ${this.selectedMonth1} to ${this.selectedMonth2}`,
      'Close',
      { duration: 3000 }
    );
  }

  // Update date range filter properties
  private updateDateRangeFilter(): void {
    if (this.selectedMonth1 && this.selectedMonth2) {
      const date1 = this.parseDate(this.selectedMonth1);
      const date2 = this.parseDate(this.selectedMonth2);

      if (date1 <= date2) {
        this.dateRangeStart = this.selectedMonth1;
        this.dateRangeEnd = this.selectedMonth2;
      } else {
        this.dateRangeStart = this.selectedMonth2;
        this.dateRangeEnd = this.selectedMonth1;
      }

      this.isDateRangeActive = true;
      console.log('Date range filter activated:', {
        start: this.dateRangeStart,
        end: this.dateRangeEnd
      });
    } else {
      this.dateRangeStart = '';
      this.dateRangeEnd = '';
      this.isDateRangeActive = false;
      console.log('Date range filter deactivated');
    }
  }

  // Apply date range filtering to all data sets
  private applyDateRangeFiltering(): void {
    if (this.isDateRangeActive) {
      const startDate = this.parseDate(this.dateRangeStart);
      const endDate = this.parseDate(this.dateRangeEnd);

      // Filter digital usage history data
      this.filteredDigitalUsageData = this.digitalUsageData.filter(item => {
        const itemDate = this.parseDate(item.Period);
        return itemDate >= startDate && itemDate <= endDate;
      });

      console.log('Applied date range filtering:', {
        originalCount: this.digitalUsageData.length,
        filteredCount: this.filteredDigitalUsageData.length,
        dateRange: `${startDate.toDateString()} to ${endDate.toDateString()}`
      });
    } else {
      // Use all data when no filter is active
      this.filteredDigitalUsageData = [...this.digitalUsageData];
    }
  }



  // Format insights for HTML display
  formatInsightsForDisplay(insights: string): string {
    if (!insights) return '';

    return insights
      // Convert markdown-style headers to HTML
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Convert bullet points to HTML list items
      .replace(/^• (.+)$/gm, '<li>$1</li>')
      // Convert line breaks to HTML breaks
      .replace(/\n/g, '<br>')
      // Wrap consecutive list items in ul tags
      .replace(/(<li>.*?<\/li>)(<br>)*(<li>.*?<\/li>)/g, '<ul>$1$3</ul>')
      // Clean up any remaining single list items
      .replace(/(<li>.*?<\/li>)/g, '<ul>$1</ul>')
      // Clean up multiple consecutive breaks
      .replace(/(<br>){2,}/g, '<br><br>');
  }

  // Create insight text based on data analysis
  createInsightText(): string {
    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      return 'No data available for analysis.';
    }

    const insights: string[] = [];
    const latestData = this.digitalUsageData[this.digitalUsageData.length - 1];
    const previousData = this.digitalUsageData[this.digitalUsageData.length - 2];

    if (previousData) {
      const totalGrowth = ((latestData.TotalTxns - previousData.TotalTxns) / previousData.TotalTxns) * 100;
      const apiGrowth = ((latestData.TotalAPITxns - previousData.TotalAPITxns) / previousData.TotalAPITxns) * 100;

      insights.push(`Total transaction volume ${totalGrowth > 0 ? 'increased' : 'decreased'} by ${Math.abs(totalGrowth).toFixed(1)}% from ${previousData.Period} to ${latestData.Period}.`);

      if (Math.abs(apiGrowth) > 5) {
        insights.push(`API transactions showed significant ${apiGrowth > 0 ? 'growth' : 'decline'} of ${Math.abs(apiGrowth).toFixed(1)}%.`);
      }

      const apiPercentage = (latestData.TotalAPITxns / latestData.TotalTxns) * 100;
      insights.push(`Current API adoption stands at ${apiPercentage.toFixed(1)}% of total transactions.`);

      if (apiPercentage > 80) {
        insights.push('Strong API adoption indicates successful digital transformation initiatives.');
      } else if (apiPercentage < 50) {
        insights.push('API adoption below 50% suggests opportunities for further digital channel migration.');
      }
    }

    return insights.join(' ');
  }

  // Load available periods for service analysis
  loadAvailablePeriods(): void {
    this.cioRspExecutiveService.getDigitalUsagePeriods().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.availablePeriods = response.data.map((item: any) => item.Period);

          // Set dynamic defaults for period comparison controls
          this.setDynamicPeriodDefaults();

          // Set default selected period to the latest for other charts
          if (this.availablePeriods.length > 0) {
            this.mainSelectedPeriod = this.availablePeriods[0];
            this.selectedPeriod = this.availablePeriods[0]; // Keep for backward compatibility
            this.loadServiceUsageData();
          }
        }
      },
      error: (error) => {
        console.error('Error loading available periods:', error);
      }
    });
  }

  // Set dynamic default values for period comparison controls
  setDynamicPeriodDefaults(): void {
    if (!this.availablePeriods || this.availablePeriods.length === 0) {
      console.warn('No available periods to set defaults');
      return;
    }

    // Sort periods chronologically to find true oldest and newest
    const sortedPeriods = [...this.availablePeriods].sort((a, b) => {
      return this.comparePeriods(a, b);
    });

    // Set defaults: oldest period as "Compare From", newest as "Compare To"
    const oldestPeriod = sortedPeriods[0];
    const newestPeriod = sortedPeriods[sortedPeriods.length - 1];

    // Only set defaults if they haven't been manually set by user
    const isInitialLoad = !this.selectedMonth1 && !this.selectedMonth2;

    if (isInitialLoad) {
      this.selectedMonth1 = oldestPeriod;
      this.selectedMonth2 = newestPeriod;

      console.log('🎯 Dynamic period defaults set:', {
        compareFrom: this.selectedMonth1,
        compareTo: this.selectedMonth2,
        totalPeriods: sortedPeriods.length,
        availableRange: `${oldestPeriod} to ${newestPeriod}`,
        allPeriods: sortedPeriods
      });

      // Update date range filtering for other components
      this.updateDateRangeFilter();
      this.applyDateRangeFiltering();

      // Trigger chart update with the new defaults
      if (this.digitalUsageData && this.digitalUsageData.length > 0) {
        this.generateChart();

        // Show user feedback about the automatic selection
        this.snackBar.open(
          `Automatically showing complete historical range: ${this.selectedMonth1} to ${this.selectedMonth2}`,
          'Close',
          { duration: 5000 }
        );
      }
    } else {
      console.log('🔄 Period defaults not set - user has already made selections:', {
        currentFrom: this.selectedMonth1,
        currentTo: this.selectedMonth2,
        availableRange: `${oldestPeriod} to ${newestPeriod}`
      });
    }
  }

  // Refresh period defaults when data changes (for database updates)
  refreshPeriodDefaults(forceUpdate: boolean = false): void {
    if (!this.availablePeriods || this.availablePeriods.length === 0) {
      console.warn('Cannot refresh period defaults - no available periods');
      return;
    }

    // Sort periods chronologically
    const sortedPeriods = [...this.availablePeriods].sort((a, b) => {
      return this.comparePeriods(a, b);
    });

    const oldestPeriod = sortedPeriods[0];
    const newestPeriod = sortedPeriods[sortedPeriods.length - 1];

    // Check if the available range has changed
    const currentOldest = this.selectedMonth1;
    const currentNewest = this.selectedMonth2;
    const rangeChanged = currentOldest !== oldestPeriod || currentNewest !== newestPeriod;

    if (forceUpdate || rangeChanged) {
      const previousRange = `${currentOldest} to ${currentNewest}`;

      this.selectedMonth1 = oldestPeriod;
      this.selectedMonth2 = newestPeriod;

      console.log('🔄 Period defaults refreshed due to data changes:', {
        previousRange: previousRange,
        newRange: `${this.selectedMonth1} to ${this.selectedMonth2}`,
        totalPeriods: sortedPeriods.length,
        rangeChanged: rangeChanged,
        forceUpdate: forceUpdate
      });

      // Update filtering and regenerate chart
      this.updateDateRangeFilter();
      this.applyDateRangeFiltering();

      if (this.digitalUsageData && this.digitalUsageData.length > 0) {
        this.generateChart();

        // Notify user of the automatic update
        if (rangeChanged) {
          this.snackBar.open(
            `Period range updated to show complete data: ${this.selectedMonth1} to ${this.selectedMonth2}`,
            'Close',
            { duration: 4000 }
          );
        }
      }
    }
  }

  // Load service usage data for selected period
  loadServiceUsageData(): void {
    const period = this.mainSelectedPeriod || this.selectedPeriod;
    if (!period) return;

    this.isLoadingServiceUsage = true;
    this.cioRspExecutiveService.getDigitalServiceUsage(period).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.serviceUsageData = response.data.map((item: any) => ({
            ...item,
            TotalAPITxns: parseInt(item.TotalAPITxns) || 0,
            TotalPortalTxns: parseInt(item.TotalPortalTxns) || 0,
            APIPercentage: parseFloat(item.APIPercentage) || 0
          }));
          this.generateServiceUsageCharts();
        }
        this.isLoadingServiceUsage = false;
      },
      error: (error) => {
        console.error('Error loading service usage data:', error);
        this.isLoadingServiceUsage = false;
      }
    });
  }

  // Load RSP API adoption data
  loadRspApiAdoptionData(): void {
    const period = this.mainSelectedPeriod || this.selectedPeriod;
    if (!period) return;

    this.isLoadingRspAdoption = true;
    this.cioRspExecutiveService.getRSPAPIAdoption(period).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.rspApiAdoptionData = response.data;
          this.generateRspApiAdoptionChart();
        }
        this.isLoadingRspAdoption = false;
      },
      error: (error) => {
        console.error('Error loading RSP API adoption data:', error);
        this.isLoadingRspAdoption = false;
      }
    });
  }

  // Load RSP digital usage data
  loadRspDigitalUsageData(): void {
    const period = this.mainSelectedPeriod || this.selectedPeriod;
    if (!period) return;

    this.isLoadingRspUsage = true;
    this.cioRspExecutiveService.getRSPDigitalUsage(period).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.rspDigitalUsageData = response.data;
          this.generateRspDigitalUsageChart();
        }
        this.isLoadingRspUsage = false;
      },
      error: (error) => {
        console.error('Error loading RSP digital usage data:', error);
        this.isLoadingRspUsage = false;
      }
    });
  }

  // Load RSP API percentage data
  loadRspApiPercentageData(): void {
    const period = this.mainSelectedPeriod || this.selectedPeriod;
    if (!period) return;

    this.isLoadingRspPercentage = true;
    this.cioRspExecutiveService.getRSPAPIPercentage(period).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.rspApiPercentageData = response.data;
          this.generateRspApiPercentageChart();
        }
        this.isLoadingRspPercentage = false;
      },
      error: (error) => {
        console.error('Error loading RSP API percentage data:', error);
        this.isLoadingRspPercentage = false;
      }
    });
  }

  // Handle main period selection change (controls all charts)
  onMainPeriodChange(): void {
    console.log(`🔄 Main period changed to: ${this.mainSelectedPeriod}`);

    // Update the individual selectedPeriod for backward compatibility
    this.selectedPeriod = this.mainSelectedPeriod;

    if (this.mainSelectedPeriod) {
      // Refresh all chart data with the new period
      this.loadServiceUsageData();
      this.loadRspApiAdoptionData();
      this.loadRspDigitalUsageData();
      this.loadRspApiPercentageData();

      // Also refresh the Digital Usage History chart
      this.loadDigitalUsageData();
    } else {
      // Load all data when "All Periods" is selected
      this.loadDigitalUsageData();
      this.loadServiceUsageData();
      this.loadRspApiAdoptionData();
      this.loadRspDigitalUsageData();
      this.loadRspApiPercentageData();
    }

    // Refresh period defaults to ensure they reflect any data changes
    setTimeout(() => {
      this.refreshPeriodDefaults();
    }, 1000); // Small delay to ensure data is loaded
  }

  // Handle period selection change (legacy method for backward compatibility)
  onPeriodSelectionChange(): void {
    if (this.selectedPeriod) {
      this.loadServiceUsageData();
      this.loadRspApiAdoptionData();
      this.loadRspDigitalUsageData();
      this.loadRspApiPercentageData();
    }
  }

  // Generate service usage charts
  generateServiceUsageCharts(): void {
    if (!this.serviceUsageData || this.serviceUsageData.length === 0) {
      return;
    }

    // Main service usage chart
    const chartData = this.serviceUsageData.map(item => ({
      ServiceName: item.ServiceName,
      TotalAPITxns: item.TotalAPITxns,
      TotalPortalTxns: item.TotalPortalTxns
    }));

    const displayPeriod = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    this.serviceUsageOptions = {
      title: {
        text: `Digital Usage By Service (${displayPeriod})`,
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: chartData,
      series: [
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions',
          fill: '#0066cc',
          stacked: true
        },
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions',
          fill: '#99ccff',
          stacked: true
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'Service Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: 'Transaction Count' },
          label: {
            formatter: (params: any) => this.formatNumber(params.value)
          }
        }
      ],
      legend: {
        position: 'bottom'
      }
    };

    // Connect services chart
    const connectData = this.serviceUsageData
      .filter(item => item.UsedForConnect === 1)
      .sort((a, b) => (b.APIPercentage || 0) - (a.APIPercentage || 0));

    this.connectServicesOptions = {
      title: {
        text: 'Key Connect Services (API%)',
        fontSize: 14,
        fontWeight: 'bold'
      },
      data: connectData,
      series: [
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'APIPercentage',
          yName: '% API Txns',
          fill: '#0066cc'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'Service Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: '% API Txns' },
          min: 0,
          max: 100
        }
      ]
    };

    // Assure services chart
    const assureData = this.serviceUsageData
      .filter(item => item.UsedForAssure === 1)
      .sort((a, b) => (b.APIPercentage || 0) - (a.APIPercentage || 0));

    this.assureServicesOptions = {
      title: {
        text: 'Key Assure Services (API%)',
        fontSize: 14,
        fontWeight: 'bold'
      },
      data: assureData,
      series: [
        {
          type: 'bar',
          xKey: 'ServiceName',
          yKey: 'APIPercentage',
          yName: '% API Txns',
          fill: '#0066cc'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'Service Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: '% API Txns' },
          min: 0,
          max: 100
        }
      ]
    };
  }

  // Generate RSP API adoption chart
  generateRspApiAdoptionChart(): void {
    if (!this.rspApiAdoptionData || this.rspApiAdoptionData.length === 0) {
      return;
    }

    // Transform data for grouped bar chart
    const chartData = this.rspApiAdoptionData.map(item => ({
      APIName: item.APIName,
      CertCount: item.CertCount,
      UtilCount: item.UtilCount
    }));

    const displayPeriod = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    this.rspApiAdoptionOptions = {
      title: {
        text: `RSP API Adoption & Utilisation (${displayPeriod})`,
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: chartData,
      series: [
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'CertCount',
          yName: 'Certified RSPs',
          fill: '#0066cc'
        },
        {
          type: 'bar',
          xKey: 'APIName',
          yKey: 'UtilCount',
          yName: 'Utilizing RSPs',
          fill: '#99ccff'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'API Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: 'RSP Count' }
        }
      ],
      legend: {
        position: 'bottom'
      }
    };
  }

  // Generate RSP digital usage chart
  generateRspDigitalUsageChart(): void {
    if (!this.rspDigitalUsageData || this.rspDigitalUsageData.length === 0) {
      return;
    }

    // Transform data for stacked bar chart
    const chartData = this.rspDigitalUsageData.map(item => ({
      RSPName: item.RSPName,
      TotalAPITxns: item.TotalAPITxns,
      TotalPortalTxns: item.TotalPortalTxns
    }));

    const displayPeriod = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    this.rspDigitalUsageOptions = {
      title: {
        text: `Digital Usage By RSP (${displayPeriod})`,
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: chartData,
      series: [
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'TotalAPITxns',
          yName: 'API Transactions',
          fill: '#0066cc',
          stacked: true
        },
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'TotalPortalTxns',
          yName: 'Portal Transactions',
          fill: '#99ccff',
          stacked: true
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'RSP Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: 'Transaction Count' },
          label: {
            formatter: (params: any) => this.formatNumber(params.value)
          }
        }
      ],
      legend: {
        position: 'bottom'
      }
    };
  }

  // Generate RSP API percentage chart
  generateRspApiPercentageChart(): void {
    if (!this.rspApiPercentageData || this.rspApiPercentageData.length === 0) {
      return;
    }

    const chartData = this.rspApiPercentageData.map(item => ({
      RSPName: item.RSPName,
      APIPercentage: item.APIPercentage
    }));

    const displayPeriod = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    this.rspApiPercentageOptions = {
      title: {
        text: `Digital Usage API% By RSP (${displayPeriod})`,
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: chartData,
      series: [
        {
          type: 'bar',
          xKey: 'RSPName',
          yKey: 'APIPercentage',
          yName: 'API Usage %',
          fill: '#0066cc'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'RSP Name' },
          label: { rotation: 45 }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: 'API Usage %' },
          min: 0,
          max: 100
        }
      ]
    };
  }



  // Get key observations based on data analysis
  getKeyObservations(): string[] {
    // Use filtered data if date range is active, otherwise use all data
    const dataToUse = this.isDateRangeActive ? this.filteredDigitalUsageData : this.digitalUsageData;

    if (!dataToUse || dataToUse.length < 2) {
      return ['Insufficient data for meaningful observations. At least 2 periods required.'];
    }

    const observations: string[] = [];
    const sortedData = [...dataToUse].sort((a, b) => this.comparePeriods(a.Period, b.Period));
    const latest = sortedData[sortedData.length - 1];
    const previous = sortedData[sortedData.length - 2];

    // Volume insights
    const metrics = [
      { key: 'TotalTxns', name: 'Total Transactions' },
      { key: 'TotalAPITxns', name: 'API Transactions' },
      { key: 'TotalPortalTxns', name: 'Portal Transactions' }
    ];

    metrics.forEach(metric => {
      const latestVal = (latest as any)[metric.key];
      const previousVal = (previous as any)[metric.key];

      if (previousVal > 0) {
        const growth = ((latestVal - previousVal) / previousVal) * 100;
        if (Math.abs(growth) > 5) {
          const direction = growth > 0 ? 'increased' : 'decreased';
          observations.push(`${metric.name} ${direction} by ${Math.abs(growth).toFixed(1)}% from ${previous.Period} to ${latest.Period} (${previousVal.toLocaleString()} → ${latestVal.toLocaleString()})`);
        }
      }
    });

    // Channel mix insights
    const totalLatest = latest.TotalTxns;
    const apiLatest = latest.TotalAPITxns;
    const totalPrevious = previous.TotalTxns;
    const apiPrevious = previous.TotalAPITxns;

    if (totalLatest > 0 && totalPrevious > 0) {
      const apiPctLatest = (apiLatest / totalLatest) * 100;
      const apiPctPrevious = (apiPrevious / totalPrevious) * 100;
      const mixShift = apiPctLatest - apiPctPrevious;

      if (Math.abs(mixShift) > 2) {
        const direction = mixShift > 0 ? 'towards API' : 'towards Portal';
        observations.push(`Channel mix shifted ${Math.abs(mixShift).toFixed(1)}% ${direction} from ${previous.Period} to ${latest.Period}`);
      }
    }

    return observations.length > 0 ? observations : ['No significant changes observed in the latest period.'];
  }

  // Get relative performance insights
  getRelativePerformanceInsights(): string[] {
    // Use filtered data if date range is active, otherwise use all data
    const dataToUse = this.isDateRangeActive ? this.filteredDigitalUsageData : this.digitalUsageData;

    if (!dataToUse || dataToUse.length < 3) {
      return ['Insufficient data for performance analysis. At least 3 periods required.'];
    }

    const insights: string[] = [];
    const sortedData = [...dataToUse].sort((a, b) => this.comparePeriods(a.Period, b.Period));

    // Calculate average growth rates over the entire period
    const firstPeriod = sortedData[0];
    const lastPeriod = sortedData[sortedData.length - 1];
    const periodsCount = sortedData.length - 1;

    if (periodsCount > 0) {
      const metrics = [
        { key: 'TotalTxns', name: 'Total Transactions' },
        { key: 'TotalAPITxns', name: 'API Transactions' },
        { key: 'TotalPortalTxns', name: 'Portal Transactions' }
      ];

      metrics.forEach(metric => {
        const firstVal = (firstPeriod as any)[metric.key];
        const lastVal = (lastPeriod as any)[metric.key];

        if (firstVal > 0) {
          const totalGrowth = ((lastVal - firstVal) / firstVal) * 100;
          const avgMonthlyGrowth = totalGrowth / periodsCount;

          if (Math.abs(avgMonthlyGrowth) > 1) {
            const trend = avgMonthlyGrowth > 0 ? 'growing' : 'declining';
            insights.push(`${metric.name} is ${trend} at an average rate of ${Math.abs(avgMonthlyGrowth).toFixed(1)}% per period`);
          }
        }
      });

      // Performance relative to overall trend
      const latest = sortedData[sortedData.length - 1];
      const previous = sortedData[sortedData.length - 2];

      const latestTotal = latest.TotalTxns;
      const previousTotal = previous.TotalTxns;
      const recentGrowth = previousTotal > 0 ? ((latestTotal - previousTotal) / previousTotal) * 100 : 0;

      const firstTotal = firstPeriod.TotalTxns;
      const lastTotal = lastPeriod.TotalTxns;
      const avgGrowth = firstTotal > 0 ? (((lastTotal - firstTotal) / firstTotal) * 100) / periodsCount : 0;

      if (Math.abs(recentGrowth - avgGrowth) > 2) {
        const performance = recentGrowth > avgGrowth ? 'above' : 'below';
        insights.push(`Recent growth (${recentGrowth.toFixed(1)}%) is ${performance} the historical average (${avgGrowth.toFixed(1)}%)`);
      }
    }

    return insights.length > 0 ? insights : ['Performance is consistent with historical trends.'];
  }

  // Get channel mix analysis data with enhanced trendline analysis
  getChannelMixAnalysis(): any[] {
    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      return [];
    }

    const sortedData = [...this.digitalUsageData].sort((a, b) => this.comparePeriods(a.Period, b.Period));

    // Calculate historical average API mix for baseline
    const totalApiTxns = sortedData.reduce((sum, item) => sum + item.TotalAPITxns, 0);
    const totalAllTxns = sortedData.reduce((sum, item) => sum + item.TotalTxns, 0);
    const historicalAPIMix = totalAllTxns > 0 ? (totalApiTxns / totalAllTxns) * 100 : 65;
    const historicalPortalMix = 100 - historicalAPIMix;

    // Calculate trendline for API mix using linear regression
    const apiMixTrendline = this.calculateLinearTrendline(
      sortedData.map((item, index) => ({
        x: index,
        y: item.TotalTxns > 0 ? (item.TotalAPITxns / item.TotalTxns) * 100 : 0
      }))
    );

    return sortedData.map((item, index) => {
      const totalTxns = item.TotalTxns;
      const apiTxns = item.TotalAPITxns;
      const portalTxns = item.TotalPortalTxns;

      const actualAPIPercent = totalTxns > 0 ? (apiTxns / totalTxns) * 100 : 0;
      const actualPortalPercent = totalTxns > 0 ? (portalTxns / totalTxns) * 100 : 0;

      // Calculate expected mix from trendline
      const expectedAPIMixFromTrend = apiMixTrendline.slope * index + apiMixTrendline.intercept;
      const expectedPortalMixFromTrend = 100 - expectedAPIMixFromTrend;

      // Calculate variances from both historical average and trendline
      const apiVarianceFromAvg = actualAPIPercent - historicalAPIMix;
      const portalVarianceFromAvg = actualPortalPercent - historicalPortalMix;
      const apiVarianceFromTrend = actualAPIPercent - expectedAPIMixFromTrend;
      const portalVarianceFromTrend = actualPortalPercent - expectedPortalMixFromTrend;

      return {
        Period: item.Period,
        ActualAPIPercent: actualAPIPercent,
        PredictedAPIPercent: historicalAPIMix,
        APIMixVariance: apiVarianceFromAvg,
        ActualPortalPercent: actualPortalPercent,
        PredictedPortalPercent: historicalPortalMix,
        PortalMixVariance: portalVarianceFromAvg,
        APIMixTrendVariance: apiVarianceFromTrend,
        PortalMixTrendVariance: portalVarianceFromTrend,
        ExpectedAPIMixTrend: expectedAPIMixFromTrend,
        ExpectedPortalMixTrend: expectedPortalMixFromTrend
      };
    });
  }

  // Calculate linear trendline using least squares method
  private calculateLinearTrendline(data: { x: number; y: number }[]): { slope: number; intercept: number } {
    if (data.length < 2) {
      return { slope: 0, intercept: 0 };
    }

    const n = data.length;
    const sumX = data.reduce((sum, point) => sum + point.x, 0);
    const sumY = data.reduce((sum, point) => sum + point.y, 0);
    const sumXY = data.reduce((sum, point) => sum + point.x * point.y, 0);
    const sumXX = data.reduce((sum, point) => sum + point.x * point.x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept };
  }

  // Get channel mix insights with enhanced trendline analysis
  getChannelMixInsights(): string[] {
    const mixAnalysis = this.getChannelMixAnalysis();
    if (mixAnalysis.length === 0) return [];

    const insights: string[] = [];

    // Calculate average variances from historical baseline
    const avgAPIVariance = mixAnalysis.reduce((sum, item) => sum + item.APIMixVariance, 0) / mixAnalysis.length;
    const avgPortalVariance = mixAnalysis.reduce((sum, item) => sum + item.PortalMixVariance, 0) / mixAnalysis.length;

    // Calculate average variances from trendline
    const avgAPITrendVariance = mixAnalysis.reduce((sum, item) => sum + (item.APIMixTrendVariance || 0), 0) / mixAnalysis.length;
    const avgPortalTrendVariance = mixAnalysis.reduce((sum, item) => sum + (item.PortalMixTrendVariance || 0), 0) / mixAnalysis.length;

    // Add insights about average channel mix variance from historical baseline
    if (Math.abs(avgAPIVariance) > 2) {
      insights.push(`API channel is averaging ${Math.abs(avgAPIVariance).toFixed(1)}% ${avgAPIVariance > 0 ? 'above' : 'below'} historical average`);
    }

    if (Math.abs(avgPortalVariance) > 2) {
      insights.push(`Portal channel is averaging ${Math.abs(avgPortalVariance).toFixed(1)}% ${avgPortalVariance > 0 ? 'above' : 'below'} historical average`);
    }

    // Add insights about trendline variance
    if (Math.abs(avgAPITrendVariance) > 2) {
      insights.push(`API channel is averaging ${Math.abs(avgAPITrendVariance).toFixed(1)}% ${avgAPITrendVariance > 0 ? 'above' : 'below'} the calculated trendline`);
    }

    if (Math.abs(avgPortalTrendVariance) > 2) {
      insights.push(`Portal channel is averaging ${Math.abs(avgPortalTrendVariance).toFixed(1)}% ${avgPortalTrendVariance > 0 ? 'above' : 'below'} the calculated trendline`);
    }

    // Add insights about specific periods with significant variance from baseline
    const significantVariances = mixAnalysis.filter(item =>
      Math.abs(item.APIMixVariance) > 5 || Math.abs(item.PortalMixVariance) > 5
    );

    significantVariances.forEach(item => {
      if (Math.abs(item.APIMixVariance) > 5) {
        insights.push(`In ${item.Period}, API mix was ${Math.abs(item.APIMixVariance).toFixed(1)}% ${item.APIMixVariance > 0 ? 'higher' : 'lower'} than historical average`);
      }
      if (Math.abs(item.PortalMixVariance) > 5) {
        insights.push(`In ${item.Period}, Portal mix was ${Math.abs(item.PortalMixVariance).toFixed(1)}% ${item.PortalMixVariance > 0 ? 'higher' : 'lower'} than historical average`);
      }
    });

    // Add insights about significant trendline deviations
    const significantTrendVariances = mixAnalysis.filter(item =>
      Math.abs(item.APIMixTrendVariance || 0) > 5 || Math.abs(item.PortalMixTrendVariance || 0) > 5
    );

    significantTrendVariances.forEach(item => {
      if (Math.abs(item.APIMixTrendVariance || 0) > 5) {
        insights.push(`In ${item.Period}, API mix deviated ${Math.abs(item.APIMixTrendVariance || 0).toFixed(1)}% from trendline prediction`);
      }
      if (Math.abs(item.PortalMixTrendVariance || 0) > 5) {
        insights.push(`In ${item.Period}, Portal mix deviated ${Math.abs(item.PortalMixTrendVariance || 0).toFixed(1)}% from trendline prediction`);
      }
    });

    if (insights.length === 0) {
      insights.push('Channel mix is closely following both historical trends and trendline predictions with no significant deviations.');
    }

    return insights;
  }

  // Get all insights summary
  getAllInsightsSummary(): string[] {
    const allInsights: string[] = [];

    // Combine all insights
    allInsights.push(...this.getKeyObservations());
    allInsights.push(...this.getRelativePerformanceInsights());
    allInsights.push(...this.getChannelMixInsights());
    allInsights.push(...this.getAdvancedAnalytics());

    return allInsights;
  }

  // Get advanced analytics (new method based on original implementation)
  getAdvancedAnalytics(): string[] {
    if (!this.digitalUsageData || this.digitalUsageData.length < 2) {
      return ['Insufficient data for advanced analytics. At least 2 periods required.'];
    }

    const insights: string[] = [];
    const sortedData = [...this.digitalUsageData].sort((a, b) => this.comparePeriods(a.Period, b.Period));

    // Get comparison months (use selected months if available, otherwise latest two)
    let month1Data, month2Data;
    if (this.selectedMonth1 && this.selectedMonth2) {
      month1Data = sortedData.find(item => item.Period === this.selectedMonth1);
      month2Data = sortedData.find(item => item.Period === this.selectedMonth2);
    }

    if (!month1Data || !month2Data) {
      month1Data = sortedData[sortedData.length - 2];
      month2Data = sortedData[sortedData.length - 1];
    }

    // Calculate compound monthly growth rate (CMGR)
    const date1 = this.parseDate(month1Data.Period);
    const date2 = this.parseDate(month2Data.Period);
    const monthsBetween = Math.abs((date2.getFullYear() - date1.getFullYear()) * 12 + date2.getMonth() - date1.getMonth());

    if (monthsBetween > 0) {
      const metrics = [
        { key: 'TotalTxns', name: 'Total Transactions' },
        { key: 'TotalAPITxns', name: 'API Transactions' },
        { key: 'TotalPortalTxns', name: 'Portal Transactions' }
      ];

      metrics.forEach(metric => {
        const val1 = (month1Data as any)[metric.key];
        const val2 = (month2Data as any)[metric.key];

        if (val1 > 0) {
          const cmgr = (Math.pow(val2 / val1, 1 / monthsBetween) - 1) * 100;
          if (Math.abs(cmgr) > 1) {
            insights.push(`${metric.name} CMGR: ${cmgr > 0 ? '+' : ''}${cmgr.toFixed(2)}% per month`);
          }
        }
      });
    }

    // Volatility analysis
    if (sortedData.length >= 3) {
      const totalTxnsValues = sortedData.map(item => item.TotalTxns);
      const mean = totalTxnsValues.reduce((sum, val) => sum + val, 0) / totalTxnsValues.length;
      const variance = totalTxnsValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / totalTxnsValues.length;
      const stdDev = Math.sqrt(variance);
      const coefficientOfVariation = (stdDev / mean) * 100;

      if (coefficientOfVariation > 20) {
        insights.push(`High volatility detected: ${coefficientOfVariation.toFixed(1)}% coefficient of variation in total transactions`);
      } else if (coefficientOfVariation < 5) {
        insights.push(`Low volatility: ${coefficientOfVariation.toFixed(1)}% coefficient of variation indicates stable transaction patterns`);
      }
    }

    return insights.length > 0 ? insights : ['No significant advanced analytics patterns detected.'];
  }

  // Get month comparison analysis (enhanced version)
  getMonthComparisonAnalysis(): any {
    // Use filtered data if date range is active, otherwise use all data
    const dataToUse = this.isDateRangeActive ? this.filteredDigitalUsageData : this.digitalUsageData;

    if (!dataToUse || dataToUse.length < 2) {
      return null;
    }

    const sortedData = [...dataToUse].sort((a, b) => this.comparePeriods(a.Period, b.Period));

    // Get comparison months
    let month1Data, month2Data;
    if (this.selectedMonth1 && this.selectedMonth2) {
      month1Data = sortedData.find(item => item.Period === this.selectedMonth1);
      month2Data = sortedData.find(item => item.Period === this.selectedMonth2);
    }

    if (!month1Data || !month2Data) {
      month1Data = sortedData[sortedData.length - 2];
      month2Data = sortedData[sortedData.length - 1];
    }

    const metrics = [
      { key: 'TotalTxns', name: 'Total Transactions' },
      { key: 'TotalAPITxns', name: 'API Transactions' },
      { key: 'TotalPortalTxns', name: 'Portal Transactions' }
    ];

    const comparisons = metrics.map(metric => {
      const val1 = (month1Data as any)[metric.key];
      const val2 = (month2Data as any)[metric.key];
      const diff = val2 - val1;
      const pctChange = val1 > 0 ? (diff / val1) * 100 : 0;

      return {
        metric: metric.name,
        month1Value: val1,
        month2Value: val2,
        difference: diff,
        percentageChange: pctChange
      };
    });

    // Channel mix analysis
    const totalLatest = month2Data.TotalTxns;
    const apiLatest = month2Data.TotalAPITxns;
    const totalPrevious = month1Data.TotalTxns;
    const apiPrevious = month1Data.TotalAPITxns;

    const apiPctLatest = totalLatest > 0 ? (apiLatest / totalLatest) * 100 : 0;
    const apiPctPrevious = totalPrevious > 0 ? (apiPrevious / totalPrevious) * 100 : 0;
    const channelMixShift = apiPctLatest - apiPctPrevious;

    return {
      month1: month1Data.Period,
      month2: month2Data.Period,
      comparisons,
      channelMixShift,
      apiPercentageLatest: apiPctLatest,
      apiPercentagePrevious: apiPctPrevious
    };
  }

  // Get growth rate color using RdYlGn color scheme (Red-Yellow-Green)
  getGrowthRateColor(value: number): string {
    // Normalize value to -1 to 1 range (assuming max growth rate of ±50%)
    const normalizedValue = Math.max(-1, Math.min(1, value / 50));

    // RdYlGn color scheme implementation
    if (normalizedValue <= -0.5) {
      // Deep red for very negative values
      return '#d73027';
    } else if (normalizedValue <= -0.25) {
      // Red for negative values
      return '#f46d43';
    } else if (normalizedValue <= -0.1) {
      // Light red for slightly negative values
      return '#fdae61';
    } else if (normalizedValue <= 0.1) {
      // Yellow for neutral values
      return '#fee08b';
    } else if (normalizedValue <= 0.25) {
      // Light green for slightly positive values
      return '#d9ef8b';
    } else if (normalizedValue <= 0.5) {
      // Green for positive values
      return '#a6d96a';
    } else {
      // Deep green for very positive values
      return '#66bd63';
    }
  }

  // Get variance color using RdYlGn color scheme with vmin=-10, vmax=10
  getVarianceColor(value: number): string {
    // Normalize value to -1 to 1 range (vmin=-10, vmax=10)
    const normalizedValue = Math.max(-1, Math.min(1, value / 10));

    // RdYlGn color scheme implementation for variance
    if (normalizedValue <= -0.8) {
      // Deep red for large negative variance
      return '#d73027';
    } else if (normalizedValue <= -0.6) {
      // Red for negative variance
      return '#f46d43';
    } else if (normalizedValue <= -0.4) {
      // Orange-red for moderate negative variance
      return '#fdae61';
    } else if (normalizedValue <= -0.2) {
      // Light orange for small negative variance
      return '#fee08b';
    } else if (normalizedValue <= 0.2) {
      // Yellow for neutral variance
      return '#ffffbf';
    } else if (normalizedValue <= 0.4) {
      // Light green for small positive variance
      return '#d9ef8b';
    } else if (normalizedValue <= 0.6) {
      // Green for moderate positive variance
      return '#a6d96a';
    } else if (normalizedValue <= 0.8) {
      // Darker green for positive variance
      return '#66bd63';
    } else {
      // Deep green for large positive variance
      return '#1a9641';
    }
  }

  // Get text color for variance cells to ensure readability against colored backgrounds
  getVarianceTextColor(value: number): string {
    // Normalize value to -1 to 1 range (vmin=-10, vmax=10)
    const normalizedValue = Math.max(-1, Math.min(1, value / 10));

    // For light backgrounds (yellow/light green/light orange), use dark text
    // For dark backgrounds (deep red/deep green), use white text
    if (normalizedValue <= -0.6 || normalizedValue >= 0.6) {
      // Deep red or deep green backgrounds - use white text
      return '#ffffff';
    } else {
      // Light/medium backgrounds - use dark text
      return '#333333';
    }
  }

  // Generate statistical analysis
  generateStatisticalAnalysis(): void {
    this.isGeneratingStatisticalAnalysis = true;
    this.statisticalAnalysis = '';

    // Simulate processing time
    setTimeout(() => {
      this.statisticalAnalysis = this.createStatisticalInsightText();
      this.isGeneratingStatisticalAnalysis = false;
    }, 1000);
  }

  // Refresh statistical analysis
  refreshStatisticalAnalysis(): void {
    this.generateStatisticalAnalysis();
  }

  // Create statistical insight text (original implementation)
  createStatisticalInsightText(): string {
    if (!this.digitalUsageData || this.digitalUsageData.length === 0) {
      return 'No data available for statistical analysis.';
    }

    const insights: string[] = [];

    // Sort data by period
    const sortedData = [...this.digitalUsageData].sort((a, b) => {
      return this.comparePeriods(a.Period, b.Period);
    });

    // Basic trend analysis
    if (sortedData.length >= 2) {
      const latest = sortedData[sortedData.length - 1];
      const previous = sortedData[sortedData.length - 2];

      insights.push(`📈 **Volume Trends (${previous.Period} to ${latest.Period})**`);

      const metrics = [
        { key: 'TotalTxns', name: 'Total transactions' },
        { key: 'TotalAPITxns', name: 'API transactions' },
        { key: 'TotalPortalTxns', name: 'Portal transactions' }
      ];

      metrics.forEach(metric => {
        const latestVal = (latest as any)[metric.key];
        const previousVal = (previous as any)[metric.key];

        if (previousVal > 0) {
          const growth = ((latestVal - previousVal) / previousVal) * 100;
          if (Math.abs(growth) > 5) {
            const direction = growth > 0 ? 'increased' : 'decreased';
            insights.push(`• ${metric.name} ${direction} by ${Math.abs(growth).toFixed(1)}% (${previousVal.toLocaleString()} → ${latestVal.toLocaleString()})`);
          }
        }
      });

      // Channel mix analysis
      const totalLatest = latest.TotalTxns;
      const apiLatest = latest.TotalAPITxns;

      if (totalLatest > 0) {
        const apiPct = (apiLatest / totalLatest) * 100;
        insights.push('\n🔄 **Channel Mix Analysis**');
        insights.push(`• Current API usage: ${apiPct.toFixed(1)}% of total transactions`);

        const totalPrevious = previous.TotalTxns;
        const apiPrevious = previous.TotalAPITxns;
        if (totalPrevious > 0) {
          const previousApiPct = (apiPrevious / totalPrevious) * 100;
          const mixShift = apiPct - previousApiPct;
          if (Math.abs(mixShift) > 2) {
            const direction = mixShift > 0 ? 'towards API' : 'towards Portal';
            insights.push(`• Channel mix shifted ${Math.abs(mixShift).toFixed(1)}% ${direction}`);
          }
        }
      }
    }

    // Add statistical recommendations
    insights.push('\n💡 **Statistical Recommendations**');
    insights.push('• Monitor transaction volume trends for capacity planning');
    insights.push('• Continue promoting API adoption for better efficiency');
    insights.push('• Analyze user behavior patterns for optimization opportunities');

    return insights.join('\n');
  }

  // Chart Export Functionality

  /**
   * Export chart to specified format
   * @param chartType - Type of chart to export (e.g., 'digitalUsageHistory', 'serviceUsage', etc.)
   * @param format - Export format ('png', 'jpeg', 'svg')
   */
  exportChart(chartType: string, format: 'png' | 'jpeg' | 'svg'): void {
    this.isExporting[chartType] = true;

    // Use setTimeout to ensure the loading state is visible
    setTimeout(() => {
      try {
        // Generate filename with timestamp
        const filename = this.generateExportFilename(chartType, format);

        // Find the currently visible chart element
        const chartElement = this.findVisibleChartElement(chartType);
        if (!chartElement) {
          throw new Error(`Chart element not found for type: ${chartType}`);
        }

        // Export using canvas
        this.fallbackCanvasExport(chartElement, format, filename);

        // Show success notification
        this.snackBar.open(`Chart exported as ${filename}`, 'Close', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });

      } catch (error) {
        console.error('Error exporting chart:', error);
        this.snackBar.open('Failed to export chart. Please try again.', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      } finally {
        this.isExporting[chartType] = false;
      }
    }, 100);
  }

  /**
   * Find chart element by chart type using direct DOM queries (no scrolling)
   */
  private findVisibleChartElement(chartType: string): HTMLElement | null {
    console.log(`🔍 Looking for chart type: ${chartType}`);

    // Get all ag-charts elements in the document
    const allCharts = document.querySelectorAll('ag-charts');
    console.log(`📊 Found ${allCharts.length} total ag-charts elements`);

    // Create a comprehensive list of all charts with their metadata
    const chartElements: Array<{
      element: HTMLElement,
      index: number,
      canvas: HTMLCanvasElement | null,
      hasContent: boolean,
      parentSection: string,
      isInActiveTab: boolean
    }> = [];

    for (let i = 0; i < allCharts.length; i++) {
      const element = allCharts[i] as HTMLElement;
      const canvas = element.querySelector('canvas');
      const hasContent = !!(canvas && canvas.width > 0 && canvas.height > 0);

      // Determine parent section
      let parentSection = 'unknown';
      let currentElement = element.parentElement;
      while (currentElement) {
        const cardTitle = currentElement.querySelector('mat-card-title');
        if (cardTitle) {
          parentSection = cardTitle.textContent?.toLowerCase() || 'unknown';
          break;
        }
        currentElement = currentElement.parentElement;
      }

      // Check if chart is in an active tab (not hidden)
      const isInActiveTab = this.isElementInActiveTab(element);

      chartElements.push({
        element,
        index: i,
        canvas,
        hasContent,
        parentSection,
        isInActiveTab
      });

      console.log(`📊 Chart ${i}: hasContent=${hasContent}, section="${parentSection}", activeTab=${isInActiveTab}`);
    }

    // Define chart type to section mapping
    const chartSectionMapping: { [key: string]: string[] } = {
      'digitalUsageHistory': ['digital usage history'],
      'serviceUsage': ['digital usage by service'],
      'connectServices': ['digital usage by service'],
      'assureServices': ['digital usage by service'],
      'rspApiAdoption': ['rsp api adoption'],
      'rspDigitalUsage': ['digital usage by rsp'],
      'rspApiPercentage': ['digital usage api%', 'rsp api percentage']
    };

    const expectedSections = chartSectionMapping[chartType] || [];

    // Find charts in the expected section
    const sectionCharts = chartElements.filter(chart =>
      expectedSections.some(section => chart.parentSection.includes(section)) &&
      chart.hasContent
    );

    if (sectionCharts.length > 0) {
      // For service usage section, we need to distinguish between different charts
      if (chartType === 'connectServices') {
        // First pie chart in service section
        const pieCharts = sectionCharts.filter(chart =>
          chart.canvas && chart.canvas.width < 600 // Pie charts are typically smaller
        );
        if (pieCharts.length > 0) {
          console.log(`✅ Found Connect Services chart (pie chart 1)`);
          return pieCharts[0].element;
        }
      } else if (chartType === 'assureServices') {
        // Second pie chart in service section
        const pieCharts = sectionCharts.filter(chart =>
          chart.canvas && chart.canvas.width < 600 // Pie charts are typically smaller
        );
        if (pieCharts.length > 1) {
          console.log(`✅ Found Assure Services chart (pie chart 2)`);
          return pieCharts[1].element;
        }
      } else if (chartType === 'serviceUsage') {
        // Main bar chart in service section (typically larger)
        const barCharts = sectionCharts.filter(chart =>
          chart.canvas && chart.canvas.width >= 600 // Bar charts are typically larger
        );
        if (barCharts.length > 0) {
          console.log(`✅ Found Service Usage chart (bar chart)`);
          return barCharts[0].element;
        }
      }

      // Default: return first chart in section
      console.log(`✅ Found chart for ${chartType} in expected section`);
      return sectionCharts[0].element;
    }

    // Fallback: find any chart with content
    const chartsWithContent = chartElements.filter(chart => chart.hasContent);
    if (chartsWithContent.length > 0) {
      console.log(`🔄 Fallback: using first available chart for ${chartType}`);
      return chartsWithContent[0].element;
    }

    console.warn(`❌ No suitable chart element found for type: ${chartType}`);
    return null;
  }

  /**
   * Check if element is in an active tab (not hidden)
   */
  private isElementInActiveTab(element: HTMLElement): boolean {
    // Check if element is inside a mat-tab-body that's hidden
    let currentElement = element.parentElement;
    while (currentElement) {
      if (currentElement.classList.contains('mat-tab-body')) {
        // Check if this tab body is active (not hidden)
        return !currentElement.hasAttribute('aria-hidden') ||
               currentElement.getAttribute('aria-hidden') === 'false';
      }
      currentElement = currentElement.parentElement;
    }

    // If not in a tab, consider it active
    return true;
  }

  /**
   * Find chart by index (alternative detection method)
   */
  private findChartByIndex(chartType: string): HTMLElement | null {
    const allCharts = document.querySelectorAll('ag-charts');

    // Simple index-based mapping
    const chartIndexMapping: { [key: string]: number } = {
      'digitalUsageHistory': 0,
      'serviceUsage': 1,
      'connectServices': 2,
      'assureServices': 3,
      'rspApiAdoption': 4,
      'rspDigitalUsage': 5,
      'rspApiPercentage': 6
    };

    const index = chartIndexMapping[chartType];
    if (index !== undefined && index < allCharts.length) {
      const element = allCharts[index] as HTMLElement;
      const canvas = element.querySelector('canvas');
      if (canvas && canvas.width > 0 && canvas.height > 0) {
        console.log(`✅ Found chart by index for ${chartType} at position ${index}`);
        return element;
      }
    }

    return null;
  }

  /**
   * Find any available chart (last resort)
   */
  private findAnyAvailableChart(): HTMLElement | null {
    const allCharts = document.querySelectorAll('ag-charts');

    for (let i = 0; i < allCharts.length; i++) {
      const element = allCharts[i] as HTMLElement;
      const canvas = element.querySelector('canvas');
      if (canvas && canvas.width > 0 && canvas.height > 0) {
        console.log(`✅ Found available chart at index ${i}`);
        return element;
      }
    }

    return null;
  }

  /**
   * Generate export filename with timestamp
   */
  private generateExportFilename(chartType: string, format: string): string {
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const period = this.mainSelectedPeriod || this.selectedPeriod || 'All';

    const chartNames: { [key: string]: string } = {
      'digitalUsageHistory': 'Digital_Usage_History',
      'serviceUsage': 'Service_Usage',
      'connectServices': 'Connect_Services_API_Usage',
      'assureServices': 'Assure_Services_API_Usage',
      'rspApiAdoption': 'RSP_API_Adoption',
      'rspDigitalUsage': 'RSP_Digital_Usage',
      'rspApiPercentage': 'RSP_API_Percentage'
    };

    const chartName = chartNames[chartType] || chartType;
    return `${chartName}_${period}_${timestamp}.${format}`;
  }



  /**
   * Fallback export method using canvas
   */
  private fallbackCanvasExport(chartElement: HTMLElement, format: string, filename: string): void {
    try {
      const canvas = chartElement.querySelector('canvas');
      if (!canvas) {
        throw new Error('Canvas element not found in chart');
      }

      // Ensure canvas has content
      if (canvas.width === 0 || canvas.height === 0) {
        throw new Error('Canvas has no content to export');
      }

      // Create download link
      const link = document.createElement('a');
      link.download = filename;

      // Convert canvas to data URL
      let dataURL: string;
      if (format === 'svg') {
        // For SVG, we'll export as PNG since canvas doesn't natively support SVG export
        dataURL = canvas.toDataURL('image/png', 1.0);
      } else {
        const mimeType = format === 'jpeg' ? 'image/jpeg' : 'image/png';
        const quality = format === 'jpeg' ? 0.9 : 1.0;
        dataURL = canvas.toDataURL(mimeType, quality);
      }

      link.href = dataURL;

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log(`Chart exported successfully as ${filename}`);

    } catch (error) {
      console.error('Error in fallback canvas export:', error);
      throw error;
    }
  }

  /**
   * Check if chart is currently being exported
   */
  isChartExporting(chartType: string): boolean {
    return this.isExporting[chartType] || false;
  }

  /**
   * Export all charts in a specific section
   */
  exportAllCharts(section: string, format: 'png' | 'jpeg' | 'svg'): void {
    const sectionCharts: { [key: string]: string[] } = {
      'digitalUsageHistory': ['digitalUsageHistory'],
      'serviceUsage': ['serviceUsage', 'connectServices', 'assureServices'],
      'rspUsage': ['rspApiAdoption', 'rspDigitalUsage', 'rspApiPercentage']
    };

    const charts = sectionCharts[section];
    if (!charts) {
      this.snackBar.open('Invalid section for export', 'Close', { duration: 3000 });
      return;
    }

    // Export each chart with a small delay to prevent overwhelming the browser
    charts.forEach((chartType, index) => {
      setTimeout(() => {
        this.exportChart(chartType, format);
      }, index * 500); // 500ms delay between exports
    });

    this.snackBar.open(`Exporting ${charts.length} charts...`, 'Close', {
      duration: 3000,
      panelClass: ['info-snackbar']
    });
  }

  // Document Export Functionality

  /**
   * Export all charts to PowerPoint presentation
   */
  async exportToPowerPoint(): Promise<void> {
    this.isExportingDocument = true;
    this.exportProgress = 0;
    this.exportStatus = 'Initializing PowerPoint export...';

    try {
      // Wait for all charts to be loaded
      await this.waitForChartsToLoad();

      // Create new presentation
      const pptx = new pptxgen();

      // Configure presentation properties
      pptx.author = 'CIO RSP Executive Dashboard';
      pptx.company = 'NBN Co';
      pptx.subject = 'RSP Executive Monthly Statistics';
      pptx.title = 'Exec Monthly Stats';

      // Add title slide
      await this.addTitleSlide(pptx);
      this.updateProgress(10, 'Added title slide...');

      // Add chart slides
      const totalCharts = this.chartMetadata.length;
      for (let i = 0; i < totalCharts; i++) {
        const chart = this.chartMetadata[i];

        // Ensure chart is loaded before capture (no scrolling)
        await this.ensureChartVisible(chart.type);

        await this.addChartSlide(pptx, chart);
        const progress = 10 + ((i + 1) / totalCharts) * 80;
        this.updateProgress(progress, `Added ${chart.title} slide...`);

        // Small delay between slides to prevent overwhelming the browser
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Add ending slide
      await this.addEndingSlide(pptx);
      this.updateProgress(95, 'Added ending slide...');

      // Generate and download file
      const filename = this.generateDocumentFilename('pptx');
      this.updateProgress(100, 'Generating PowerPoint file...');

      await pptx.writeFile({ fileName: filename });

      this.snackBar.open(`PowerPoint exported as ${filename}`, 'Close', {
        duration: 5000,
        panelClass: ['success-snackbar']
      });

    } catch (error) {
      console.error('Error exporting to PowerPoint:', error);
      this.snackBar.open('Failed to export PowerPoint. Please try again.', 'Close', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    } finally {
      this.isExportingDocument = false;
      this.exportProgress = 0;
      this.exportStatus = '';
    }
  }

  /**
   * Export all charts to PDF document
   */
  async exportToPDF(): Promise<void> {
    this.isExportingDocument = true;
    this.exportProgress = 0;
    this.exportStatus = 'Initializing PDF export...';

    try {
      // Wait for all charts to be loaded
      await this.waitForChartsToLoad();

      // Create new PDF document
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      // Add title page
      await this.addTitlePage(pdf);
      this.updateProgress(10, 'Added title page...');

      // Add chart pages
      const totalCharts = this.chartMetadata.length;
      for (let i = 0; i < totalCharts; i++) {
        const chart = this.chartMetadata[i];
        pdf.addPage();
        await this.addChartPage(pdf, chart);
        const progress = 10 + ((i + 1) / totalCharts) * 80;
        this.updateProgress(progress, `Added ${chart.title} page...`);
      }

      // Add ending page
      pdf.addPage();
      await this.addEndingPage(pdf);
      this.updateProgress(95, 'Added ending page...');

      // Generate and download file
      const filename = this.generateDocumentFilename('pdf');
      this.updateProgress(100, 'Generating PDF file...');

      pdf.save(filename);

      this.snackBar.open(`PDF exported as ${filename}`, 'Close', {
        duration: 5000,
        panelClass: ['success-snackbar']
      });

    } catch (error) {
      console.error('Error exporting to PDF:', error);
      this.snackBar.open('Failed to export PDF. Please try again.', 'Close', {
        duration: 5000,
        panelClass: ['error-snackbar']
      });
    } finally {
      this.isExportingDocument = false;
      this.exportProgress = 0;
      this.exportStatus = '';
    }
  }

  // Helper Methods for Document Export

  /**
   * Update export progress
   */
  private updateProgress(progress: number, status: string): void {
    this.exportProgress = progress;
    this.exportStatus = status;
  }

  /**
   * Generate document filename with timestamp
   */
  private generateDocumentFilename(format: string): string {
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const period = this.mainSelectedPeriod || this.selectedPeriod || 'All';
    return `CIO_RSP_Executive_Report_${period}_${timestamp}.${format}`;
  }

  /**
   * Add title slide to PowerPoint
   */
  private async addTitleSlide(pptx: any): Promise<void> {
    // Try to load intro template image first
    const introTemplate = await this.templateService.loadTemplateImage('intro');

    if (introTemplate) {
      console.log('📄 Using intro template image for title slide');

      // Add hybrid template slide (background image + dynamic text overlay)
      await this.addHybridTemplateSlide(pptx, introTemplate, 'intro');
    } else {
      console.log('📄 No intro template image found, using default title slide');
      // Fallback to programmatically generated slide
      await this.addDefaultTitleSlide(pptx);
    }
  }

  /**
   * Add hybrid template slide (background image + dynamic text overlay)
   */
  private async addHybridTemplateSlide(pptx: any, templateImage: any, slideType: 'intro' | 'end'): Promise<void> {
    try {
      console.log(`📄 Adding ${slideType} hybrid template slide (image + text overlay)`);

      const slide = pptx.addSlide();

      // Step 1: Add background image (full slide coverage)
      slide.addImage({
        data: templateImage.dataURL,
        x: 0,
        y: 0,
        w: '100%',
        h: '100%'
      });

      // Step 2: Get text overlay configuration for this template type
      const textConfig = this.templateService.getTextOverlayConfig(slideType);

      // Step 3: Create template context with dynamic data
      const period = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
      const context = this.templateService.createTemplateContext(
        period,
        'Exec Monthly Stats',
        {
          company: 'NBN Co',
          subject: 'RSP Executive Monthly Statistics'
        }
      );

      // Step 4: Add dynamic text overlays
      if (textConfig.title) {
        // Use different title text based on slide type
        const titleText = slideType === 'intro' ? context.title : 'Thank You';

        // Configure font family for "Thank You" text
        const titleOptions: any = {
          x: textConfig.title.x,
          y: textConfig.title.y,
          w: textConfig.title.w,
          h: textConfig.title.h,
          fontSize: textConfig.title.fontSize,
          color: textConfig.title.color,
          align: textConfig.title.align,
          bold: true
        };

        // Add Aptos (Headings) font for both intro and end slide titles
        titleOptions.fontFace = 'Aptos';

        slide.addText(titleText, titleOptions);
        console.log(`📄 Added title overlay: "${titleText}" ${slideType === 'end' ? '(Aptos 70pt)' : ''}`);
      }

      if (textConfig.period && context.period) {
        // Use "Report Period:" format for both intro and end slides
        const periodText = `Report Period: ${context.period}`;

        const periodOptions: any = {
          x: textConfig.period.x,
          y: textConfig.period.y,
          w: textConfig.period.w,
          h: textConfig.period.h,
          fontSize: textConfig.period.fontSize,
          color: textConfig.period.color,
          align: textConfig.period.align
        };

        // Add Aptos font for period text on both intro and end slides
        periodOptions.fontFace = 'Aptos';

        slide.addText(periodText, periodOptions);
        console.log(`📄 Added period overlay: "${periodText}" (Aptos font)`);
      }

      if (textConfig.date && context.date) {
        const dateText = `Generated: ${context.date}`;

        const dateOptions: any = {
          x: textConfig.date.x,
          y: textConfig.date.y,
          w: textConfig.date.w,
          h: textConfig.date.h,
          fontSize: textConfig.date.fontSize,
          color: textConfig.date.color,
          align: textConfig.date.align
        };

        // Add Aptos font for date text on both intro and end slides
        dateOptions.fontFace = 'Aptos';

        slide.addText(dateText, dateOptions);
        console.log(`📄 Added date overlay: "${dateText}" at center position (${textConfig.date.x}, ${textConfig.date.y}) in Aptos font`);
      }

      if (textConfig.company && context.company) {
        const companyText = slideType === 'intro'
          ? `${context.company}`
          : `© ${context.company}`;

        slide.addText(companyText, {
          x: textConfig.company.x,
          y: textConfig.company.y,
          w: textConfig.company.w,
          h: textConfig.company.h,
          fontSize: textConfig.company.fontSize,
          color: textConfig.company.color,
          align: textConfig.company.align,
          bold: slideType === 'intro'
        });
        console.log(`📄 Added company overlay: "${companyText}"`);
      }





      console.log(`📄 Successfully created ${slideType} hybrid template slide with ${Object.keys(textConfig).length} text overlays`);

    } catch (error) {
      console.warn(`Failed to add ${slideType} hybrid template slide:`, error);
      // Fallback to default slide
      if (slideType === 'intro') {
        await this.addDefaultTitleSlide(pptx);
      } else {
        await this.addDefaultEndingSlide(pptx);
      }
    }
  }

  /**
   * Add default title slide (fallback when no template is available)
   */
  private async addDefaultTitleSlide(pptx: any): Promise<void> {
    const slide = pptx.addSlide();

    // Add title
    slide.addText('Exec Monthly Stats', {
      x: 1,
      y: 2,
      w: 8,
      h: 1.5,
      fontSize: 44,
      bold: true,
      align: 'center',
      color: '1976d2'
    });

    // Add subtitle
    const period = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    const date = new Date().toLocaleDateString();
    slide.addText(`Period: ${period} | Generated: ${date}`, {
      x: 1,
      y: 4,
      w: 8,
      h: 0.8,
      fontSize: 18,
      align: 'center',
      color: '666666'
    });

    // Add slide number
    slide.addText('1', {
      x: 9.5,
      y: 6.8,
      w: 0.5,
      h: 0.3,
      fontSize: 12,
      align: 'center',
      color: '999999'
    });
  }

  /**
   * Add ending slide to PowerPoint
   */
  private async addEndingSlide(pptx: any): Promise<void> {
    // Try to load end template image first
    const endTemplate = await this.templateService.loadTemplateImage('end');

    if (endTemplate) {
      console.log('📄 Using end template image for ending slide');

      // Add hybrid template slide (background image + dynamic text overlay)
      await this.addHybridTemplateSlide(pptx, endTemplate, 'end');
    } else {
      console.log('📄 No end template image found, using default ending slide');
      // Fallback to programmatically generated slide
      await this.addDefaultEndingSlide(pptx);
    }
  }

  /**
   * Add default ending slide (fallback when no template is available)
   */
  private async addDefaultEndingSlide(pptx: any): Promise<void> {
    const slide = pptx.addSlide();

    // Add ending text
    slide.addText('End', {
      x: 1,
      y: 3,
      w: 8,
      h: 1.5,
      fontSize: 44,
      bold: true,
      align: 'center',
      color: '1976d2'
    });

    // Add slide number
    const slideNumber = this.chartMetadata.length + 2; // +2 for title and ending slides
    slide.addText(slideNumber.toString(), {
      x: 9.5,
      y: 6.8,
      w: 0.5,
      h: 0.3,
      fontSize: 12,
      align: 'center',
      color: '999999'
    });
  }

  /**
   * Add chart slide to PowerPoint
   */
  private async addChartSlide(pptx: any, chartMeta: any): Promise<void> {
    console.log(`📊 Creating slide for chart: ${chartMeta.title}`);
    const slide = pptx.addSlide();

    // Add slide title
    slide.addText(chartMeta.title, {
      x: 0.5,
      y: 0.3,
      w: 9,
      h: 0.8,
      fontSize: 24,
      bold: true,
      align: 'center',
      color: '1976d2'
    });

    console.log(`📸 Capturing image for chart: ${chartMeta.type}`);

    // Get chart image with dimensions
    const chartResult = await this.captureChartImage(chartMeta.type);
    if (chartResult && chartResult.dataURL && chartResult.dataURL.length > 1000) { // Validate image data
      console.log(`✅ Chart image captured successfully for ${chartMeta.type}, size: ${chartResult.dataURL.length} chars`);
      console.log(`📐 Chart dimensions: ${chartResult.dimensions.width}x${chartResult.dimensions.height}, aspect ratio: ${chartResult.dimensions.aspectRatio.toFixed(2)}`);

      try {
        // Validate data URL format
        if (!chartResult.dataURL.startsWith('data:image/')) {
          throw new Error('Invalid data URL format');
        }

        // Calculate optimal slide dimensions based on chart type and aspect ratio
        const slideLayout = this.calculateOptimalSlideLayout(chartMeta.type, chartResult.dimensions);

        // Add chart image to slide with dynamic sizing
        slide.addImage({
          data: chartResult.dataURL,
          x: slideLayout.x,
          y: slideLayout.y,
          w: slideLayout.width,
          h: slideLayout.height,
          sizing: {
            type: 'contain'
          }
        });
        console.log(`✅ Chart image added to slide for ${chartMeta.type} with layout:`, slideLayout);
      } catch (error) {
        console.error(`❌ Error adding image to slide for ${chartMeta.type}:`, error);

        // Add placeholder text if image fails
        slide.addText('Chart image could not be loaded', {
          x: 1,
          y: 3.5,
          w: 8,
          h: 1,
          fontSize: 16,
          align: 'center',
          color: 'ff0000'
        });
      }
    } else {
      console.warn(`❌ No valid chart image captured for ${chartMeta.type}`);

      // Add placeholder text if no image
      slide.addText('Chart not available', {
        x: 1,
        y: 3.5,
        w: 8,
        h: 1,
        fontSize: 16,
        align: 'center',
        color: 'ff0000'
      });
    }

    // Add description
    slide.addText(chartMeta.description, {
      x: 0.5,
      y: 6.2,
      w: 9,
      h: 0.5,
      fontSize: 14,
      align: 'center',
      color: '666666'
    });

    // Add slide number
    const slideNumber = this.chartMetadata.findIndex(c => c.type === chartMeta.type) + 2; // +1 for title slide, +1 for 1-based indexing
    slide.addText(slideNumber.toString(), {
      x: 9.5,
      y: 6.8,
      w: 0.5,
      h: 0.3,
      fontSize: 12,
      align: 'center',
      color: '999999'
    });

    console.log(`✅ Slide completed for ${chartMeta.title}`);
  }

  /**
   * Calculate optimal slide layout based on chart type and dimensions
   */
  private calculateOptimalSlideLayout(chartType: string, dimensions: {width: number, height: number, aspectRatio: number}): {x: number, y: number, width: number, height: number} {
    // PowerPoint slide dimensions (standard 16:9 aspect ratio)
    const slideWidth = 10; // inches

    // Available area for chart (leaving space for title and margins)
    const availableWidth = 9; // inches
    const availableHeight = 5.5; // inches

    // Chart type specific optimizations
    const chartTypeSettings = {
      'digitalUsageHistory': {
        preferredAspectRatio: 2.5, // Wide for time series
        minWidth: 8,
        maxHeight: 4.5
      },
      'serviceUsage': {
        preferredAspectRatio: 1.8, // Moderate width for bar charts
        minWidth: 7,
        maxHeight: 5
      },
      'connectServices': {
        preferredAspectRatio: 1.0, // Square for pie charts
        minWidth: 4,
        maxHeight: 4
      },
      'assureServices': {
        preferredAspectRatio: 1.0, // Square for pie charts
        minWidth: 4,
        maxHeight: 4
      },
      'rspApiAdoption': {
        preferredAspectRatio: 1.6, // Standard for bar charts
        minWidth: 7,
        maxHeight: 5
      },
      'rspDigitalUsage': {
        preferredAspectRatio: 1.8, // Wide for stacked bars
        minWidth: 7.5,
        maxHeight: 4.5
      },
      'rspApiPercentage': {
        preferredAspectRatio: 1.6, // Standard for bar charts
        minWidth: 7,
        maxHeight: 5
      }
    };

    const settings = (chartTypeSettings as any)[chartType] || {
      preferredAspectRatio: 1.6,
      minWidth: 7,
      maxHeight: 5
    };

    let finalWidth: number;
    let finalHeight: number;

    // Calculate dimensions based on chart's natural aspect ratio and type preferences
    if (dimensions.aspectRatio > settings.preferredAspectRatio) {
      // Chart is wider than preferred - constrain by width
      finalWidth = Math.min(availableWidth, settings.minWidth);
      finalHeight = finalWidth / dimensions.aspectRatio;

      // Ensure height doesn't exceed limits
      if (finalHeight > settings.maxHeight) {
        finalHeight = settings.maxHeight;
        finalWidth = finalHeight * dimensions.aspectRatio;
      }
    } else {
      // Chart is taller than preferred - constrain by height
      finalHeight = Math.min(availableHeight, settings.maxHeight);
      finalWidth = finalHeight * dimensions.aspectRatio;

      // Ensure width doesn't exceed limits
      if (finalWidth > availableWidth) {
        finalWidth = availableWidth;
        finalHeight = finalWidth / dimensions.aspectRatio;
      }
    }

    // Center the chart on the slide
    const x = (slideWidth - finalWidth) / 2;
    const y = 1.5 + (availableHeight - finalHeight) / 2; // 1.5 inches from top for title

    console.log(`📐 Layout for ${chartType}: ${finalWidth.toFixed(2)}x${finalHeight.toFixed(2)} at (${x.toFixed(2)}, ${y.toFixed(2)})`);

    return {
      x: x,
      y: y,
      width: finalWidth,
      height: finalHeight
    };
  }

  /**
   * Calculate optimal PDF layout based on chart type and dimensions
   */
  private calculateOptimalPDFLayout(chartType: string, dimensions: {width: number, height: number, aspectRatio: number}, pageWidth: number, pageHeight: number): {x: number, y: number, width: number, height: number} {
    // Available area for chart (leaving space for title and margins)
    const availableWidth = pageWidth - 40; // 20mm margins on each side
    const availableHeight = pageHeight - 80; // 40mm for title, 40mm for bottom margin

    // Chart type specific optimizations for PDF (in mm)
    const chartTypeSettings = {
      'digitalUsageHistory': {
        preferredAspectRatio: 2.5, // Wide for time series
        minWidth: 200,
        maxHeight: 120
      },
      'serviceUsage': {
        preferredAspectRatio: 1.8, // Moderate width for bar charts
        minWidth: 180,
        maxHeight: 140
      },
      'connectServices': {
        preferredAspectRatio: 1.0, // Square for pie charts
        minWidth: 120,
        maxHeight: 120
      },
      'assureServices': {
        preferredAspectRatio: 1.0, // Square for pie charts
        minWidth: 120,
        maxHeight: 120
      },
      'rspApiAdoption': {
        preferredAspectRatio: 1.6, // Standard for bar charts
        minWidth: 180,
        maxHeight: 140
      },
      'rspDigitalUsage': {
        preferredAspectRatio: 1.8, // Wide for stacked bars
        minWidth: 200,
        maxHeight: 120
      },
      'rspApiPercentage': {
        preferredAspectRatio: 1.6, // Standard for bar charts
        minWidth: 180,
        maxHeight: 140
      }
    };

    const settings = (chartTypeSettings as any)[chartType] || {
      preferredAspectRatio: 1.6,
      minWidth: 180,
      maxHeight: 140
    };

    let finalWidth: number;
    let finalHeight: number;

    // Calculate dimensions based on chart's natural aspect ratio and type preferences
    if (dimensions.aspectRatio > settings.preferredAspectRatio) {
      // Chart is wider than preferred - constrain by width
      finalWidth = Math.min(availableWidth, settings.minWidth);
      finalHeight = finalWidth / dimensions.aspectRatio;

      // Ensure height doesn't exceed limits
      if (finalHeight > settings.maxHeight) {
        finalHeight = settings.maxHeight;
        finalWidth = finalHeight * dimensions.aspectRatio;
      }
    } else {
      // Chart is taller than preferred - constrain by height
      finalHeight = Math.min(availableHeight, settings.maxHeight);
      finalWidth = finalHeight * dimensions.aspectRatio;

      // Ensure width doesn't exceed limits
      if (finalWidth > availableWidth) {
        finalWidth = availableWidth;
        finalHeight = finalWidth / dimensions.aspectRatio;
      }
    }

    // Center the chart on the page
    const x = (pageWidth - finalWidth) / 2;
    const y = 40 + (availableHeight - finalHeight) / 2; // 40mm from top for title

    console.log(`📐 PDF Layout for ${chartType}: ${finalWidth.toFixed(2)}x${finalHeight.toFixed(2)} at (${x.toFixed(2)}, ${y.toFixed(2)})`);

    return {
      x: x,
      y: y,
      width: finalWidth,
      height: finalHeight
    };
  }

  /**
   * Add title page to PDF
   */
  private async addTitlePage(pdf: any): Promise<void> {
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add title
    pdf.setFontSize(36);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(25, 118, 210); // Blue color
    pdf.text('Exec Monthly Stats', pageWidth / 2, pageHeight / 2 - 20, { align: 'center' });

    // Add subtitle
    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(102, 102, 102); // Gray color
    const period = this.mainSelectedPeriod || this.selectedPeriod || 'All Periods';
    const date = new Date().toLocaleDateString();
    pdf.text(`Period: ${period} | Generated: ${date}`, pageWidth / 2, pageHeight / 2 + 10, { align: 'center' });

    // Add page number
    pdf.setFontSize(10);
    pdf.setTextColor(153, 153, 153);
    pdf.text('1', pageWidth - 20, pageHeight - 10);
  }

  /**
   * Add ending page to PDF
   */
  private async addEndingPage(pdf: any): Promise<void> {
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add ending text
    pdf.setFontSize(36);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(25, 118, 210); // Blue color
    pdf.text('End', pageWidth / 2, pageHeight / 2, { align: 'center' });

    // Add page number
    pdf.setFontSize(10);
    pdf.setTextColor(153, 153, 153);
    const pageNumber = this.chartMetadata.length + 2; // +2 for title and ending pages
    pdf.text(pageNumber.toString(), pageWidth - 20, pageHeight - 10);
  }

  /**
   * Add chart page to PDF
   */
  private async addChartPage(pdf: any, chartMeta: any): Promise<void> {
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Add page title
    pdf.setFontSize(20);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(25, 118, 210); // Blue color
    pdf.text(chartMeta.title, pageWidth / 2, 20, { align: 'center' });

    // Get chart image with dimensions
    const chartResult = await this.captureChartImage(chartMeta.type);
    if (chartResult && chartResult.dataURL) {
      console.log(`📐 PDF Chart dimensions: ${chartResult.dimensions.width}x${chartResult.dimensions.height}, aspect ratio: ${chartResult.dimensions.aspectRatio.toFixed(2)}`);

      // Calculate optimal PDF dimensions based on chart aspect ratio
      const pdfLayout = this.calculateOptimalPDFLayout(chartMeta.type, chartResult.dimensions, pageWidth, pageHeight);

      pdf.addImage(chartResult.dataURL, 'PNG', pdfLayout.x, pdfLayout.y, pdfLayout.width, pdfLayout.height);
      console.log(`✅ Chart image added to PDF for ${chartMeta.type} with layout:`, pdfLayout);
    } else {
      // Add placeholder text if no image
      pdf.setFontSize(16);
      pdf.setTextColor(255, 0, 0); // Red color
      pdf.text('Chart not available', pageWidth / 2, pageHeight / 2, { align: 'center' });
    }

    // Add description
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(102, 102, 102); // Gray color
    pdf.text(chartMeta.description, pageWidth / 2, pageHeight - 30, { align: 'center' });

    // Add page number
    pdf.setFontSize(10);
    pdf.setTextColor(153, 153, 153);
    const pageNumber = this.chartMetadata.findIndex(c => c.type === chartMeta.type) + 2; // +1 for title page, +1 for 1-based indexing
    pdf.text(pageNumber.toString(), pageWidth - 20, pageHeight - 10);
  }

  /**
   * Capture chart image with dimension analysis for optimal PowerPoint sizing
   */
  private async captureChartImage(chartType: string): Promise<{dataURL: string, dimensions: {width: number, height: number, aspectRatio: number}} | null> {
    try {
      console.log(`🔍 Starting chart capture for type: ${chartType}`);

      // Strategy 1: Find chart element using improved detection
      let chartElement = this.findVisibleChartElement(chartType);

      // Strategy 2: If not found, try alternative detection
      if (!chartElement) {
        chartElement = this.findChartByIndex(chartType);
      }

      // Strategy 3: If still not found, try finding any available chart
      if (!chartElement) {
        chartElement = this.findAnyAvailableChart();
      }

      if (!chartElement) {
        console.warn(`❌ Chart element not found for type: ${chartType}`);
        return null;
      }

      console.log(`✅ Found chart element for ${chartType}:`, chartElement);

      // Try to get canvas directly from AG Charts
      const canvas = chartElement.querySelector('canvas');
      if (!canvas) {
        console.warn(`❌ No canvas found in chart element for ${chartType}`);
        return null;
      }

      // Analyze chart dimensions
      const originalWidth = canvas.width;
      const originalHeight = canvas.height;
      const aspectRatio = originalWidth / originalHeight;

      console.log(`📏 Original canvas dimensions: ${originalWidth}x${originalHeight}`);
      console.log(`📐 Aspect ratio: ${aspectRatio.toFixed(2)}`);

      if (originalWidth === 0 || originalHeight === 0) {
        console.warn(`❌ Canvas has zero dimensions for ${chartType}`);
        return null;
      }

      // Method 1: Try to get data URL directly from canvas with high quality
      try {
        const directDataURL = canvas.toDataURL('image/png', 1.0);
        if (directDataURL && directDataURL.length > 1000) { // Basic validation
          console.log(`✅ Direct canvas capture successful for ${chartType}, length: ${directDataURL.length}`);
          return {
            dataURL: directDataURL,
            dimensions: {
              width: originalWidth,
              height: originalHeight,
              aspectRatio: aspectRatio
            }
          };
        }
      } catch (directError) {
        console.warn(`⚠️ Direct canvas capture failed for ${chartType}:`, directError);
      }

      // Method 2: Use html2canvas as fallback with high quality settings
      console.log(`🔄 Trying html2canvas fallback for ${chartType}...`);

      const html2canvasResult = await html2canvas(chartElement, {
        backgroundColor: '#ffffff',
        scale: 2, // Higher scale for better quality
        useCORS: true,
        allowTaint: true,
        logging: false,
        width: chartElement.offsetWidth,
        height: chartElement.offsetHeight,
        foreignObjectRendering: false,
        removeContainer: true
      });

      if (html2canvasResult && html2canvasResult.width > 0 && html2canvasResult.height > 0) {
        const fallbackDataURL = html2canvasResult.toDataURL('image/png', 1.0);
        console.log(`✅ html2canvas capture successful for ${chartType}, length: ${fallbackDataURL.length}`);
        return {
          dataURL: fallbackDataURL,
          dimensions: {
            width: html2canvasResult.width,
            height: html2canvasResult.height,
            aspectRatio: html2canvasResult.width / html2canvasResult.height
          }
        };
      }

      console.error(`❌ Both capture methods failed for ${chartType}`);
      return null;

    } catch (error) {
      console.error(`❌ Error capturing chart image for ${chartType}:`, error);
      return null;
    }
  }

  /**
   * Check if document export is in progress
   */
  isDocumentExporting(): boolean {
    return this.isExportingDocument;
  }

  /**
   * Get current export progress percentage
   */
  getExportProgress(): number {
    return this.exportProgress;
  }

  /**
   * Get current export status message
   */
  getExportStatus(): string {
    return this.exportStatus;
  }

  /**
   * Ensure chart is loaded before capture (NO SCROLLING)
   */
  private async ensureChartVisible(chartType: string): Promise<void> {
    console.log(`🔍 Ensuring chart is loaded: ${chartType}`);

    // Wait for any ongoing animations or rendering
    await new Promise(resolve => setTimeout(resolve, 200));

    // Verify chart exists without scrolling
    const chartElement = this.findVisibleChartElement(chartType);
    if (chartElement) {
      console.log(`✅ Chart element found for ${chartType}`);
    } else {
      console.warn(`⚠️ Could not find chart element for ${chartType}`);
    }

    // Additional wait for chart rendering
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  /**
   * Wait for all charts to be loaded before export
   */
  private async waitForChartsToLoad(): Promise<void> {
    console.log(`⏳ Waiting for all charts to load...`);

    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      const allCharts = document.querySelectorAll('ag-charts');
      let loadedCharts = 0;

      for (let i = 0; i < allCharts.length; i++) {
        const element = allCharts[i] as HTMLElement;
        const canvas = element.querySelector('canvas');

        if (canvas && canvas.width > 0 && canvas.height > 0) {
          loadedCharts++;
        }
      }

      console.log(`📊 Charts loaded: ${loadedCharts}/${allCharts.length}`);

      if (loadedCharts >= Math.min(7, allCharts.length)) { // We expect at least 7 charts
        console.log(`✅ All expected charts are loaded`);
        break;
      }

      attempts++;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (attempts >= maxAttempts) {
      console.warn(`⚠️ Timeout waiting for charts to load, proceeding anyway`);
    }

    // Final wait for any remaining rendering
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Test method for debugging chart capture (can be called from browser console)
   */
  async testChartCapture(chartType: string = 'digitalUsageHistory'): Promise<void> {
    console.log(`🧪 Testing chart capture for: ${chartType}`);

    const chartResult = await this.captureChartImage(chartType);
    if (chartResult) {
      console.log(`✅ Chart capture successful! Image length: ${chartResult.dataURL.length}`);
      console.log(`📐 Dimensions: ${chartResult.dimensions.width}x${chartResult.dimensions.height}, aspect ratio: ${chartResult.dimensions.aspectRatio.toFixed(2)}`);

      // Create a temporary image element to verify the capture
      const img = document.createElement('img');
      img.src = chartResult.dataURL;
      img.style.maxWidth = '300px';
      img.style.border = '2px solid green';
      img.title = `Captured ${chartType}`;

      // Add to page temporarily for visual verification
      document.body.appendChild(img);

      setTimeout(() => {
        document.body.removeChild(img);
      }, 5000);

      console.log(`✅ Test image added to page for 5 seconds`);
    } else {
      console.error(`❌ Chart capture failed for ${chartType}`);
    }
  }

  // Generate Holt-Winters exponential smoothing forecasts
  generateHoltWintersForecasts(): void {
    console.log('Generating Holt-Winters forecasts...', {
      hasData: !!this.digitalUsageData,
      dataLength: this.digitalUsageData?.length,
      projectionMonths: this.projectionMonths
    });

    if (!this.digitalUsageData || this.digitalUsageData.length < 12) {
      console.warn('Insufficient data for Holt-Winters forecasting (minimum 12 data points required)');
      this.holtWintersData = [];
      this.holtWintersMetadata = null;
      return;
    }

    try {
      // Sort data by period
      const sortedData = [...this.digitalUsageData].sort((a, b) => {
        return this.comparePeriods(a.Period, b.Period);
      });

      // Extract time series data
      const totalTxns = sortedData.map(item => item.TotalTxns);
      const apiTxns = sortedData.map(item => item.TotalAPITxns);
      const portalTxns = sortedData.map(item => item.TotalPortalTxns);

      // Generate forecasts for each series
      const totalForecast = this.holtWintersSmoothing(totalTxns, 12, this.projectionMonths);
      const apiForecast = this.holtWintersSmoothing(apiTxns, 12, this.projectionMonths);
      const portalForecast = this.holtWintersSmoothing(portalTxns, 12, this.projectionMonths);

      // Generate forecast periods
      const lastDate = this.parseDate(sortedData[sortedData.length - 1].Period);
      this.holtWintersData = [];

      for (let i = 1; i <= this.projectionMonths; i++) {
        const futureDate = new Date(lastDate);
        futureDate.setMonth(futureDate.getMonth() + i);
        const futurePeriod = this.formatPeriod(futureDate);

        const totalValue = totalForecast.forecast[i - 1];
        const apiValue = apiForecast.forecast[i - 1];
        const portalValue = portalForecast.forecast[i - 1];

        // Calculate confidence intervals (80% and 95%)
        const totalStdError = totalForecast.standardError;
        const apiStdError = apiForecast.standardError;
        const portalStdError = portalForecast.standardError;

        this.holtWintersData.push({
          Period: futurePeriod,
          TotalForecast: Math.max(0, Math.round(totalValue)),
          APIForecast: Math.max(0, Math.round(apiValue)),
          PortalForecast: Math.max(0, Math.round(portalValue)),
          // 80% confidence intervals (1.28 * std error)
          Total80Lower: Math.max(0, Math.round(totalValue - 1.28 * totalStdError)),
          Total80Upper: Math.round(totalValue + 1.28 * totalStdError),
          API80Lower: Math.max(0, Math.round(apiValue - 1.28 * apiStdError)),
          API80Upper: Math.round(apiValue + 1.28 * apiStdError),
          Portal80Lower: Math.max(0, Math.round(portalValue - 1.28 * portalStdError)),
          Portal80Upper: Math.round(portalValue + 1.28 * portalStdError),
          // 95% confidence intervals (1.96 * std error)
          Total95Lower: Math.max(0, Math.round(totalValue - 1.96 * totalStdError)),
          Total95Upper: Math.round(totalValue + 1.96 * totalStdError),
          API95Lower: Math.max(0, Math.round(apiValue - 1.96 * apiStdError)),
          API95Upper: Math.round(apiValue + 1.96 * apiStdError),
          Portal95Lower: Math.max(0, Math.round(portalValue - 1.96 * portalStdError)),
          Portal95Upper: Math.round(portalValue + 1.96 * portalStdError)
        });
      }

      // Set metadata
      this.holtWintersMetadata = {
        seasonality: Math.min(12, Math.floor(sortedData.length / 2)),
        dataPoints: sortedData.length,
        timestamp: new Date(),
        forecastHorizon: this.projectionMonths
      };

      console.log('Holt-Winters forecasts generated:', this.holtWintersData);
    } catch (error) {
      console.error('Error generating Holt-Winters forecasts:', error);
      this.holtWintersData = [];
      this.holtWintersMetadata = null;
    }
  }

  // Holt-Winters exponential smoothing implementation
  private holtWintersSmoothing(data: number[], seasonLength: number, forecastPeriods: number): any {
    const n = data.length;

    // Adjust season length for smaller datasets
    const adjustedSeasonLength = Math.min(seasonLength, Math.floor(n / 2));

    if (n < 4 || adjustedSeasonLength < 2) {
      throw new Error('Insufficient data for seasonal decomposition');
    }

    // Initialize parameters
    const alpha = 0.3; // Level smoothing parameter
    const beta = 0.1;  // Trend smoothing parameter
    const gamma = 0.1; // Seasonal smoothing parameter

    // Initialize level, trend, and seasonal components
    let level = ss.mean(data.slice(0, adjustedSeasonLength));
    let trend = 0;

    // Calculate trend only if we have enough data
    if (n >= 2 * adjustedSeasonLength) {
      trend = (ss.mean(data.slice(adjustedSeasonLength, 2 * adjustedSeasonLength)) - level) / adjustedSeasonLength;
    } else {
      // Simple linear trend for smaller datasets
      const x = Array.from({length: n}, (_, i) => i);
      const regression = ss.linearRegression(x.map((xi, i) => [xi, data[i]]));
      trend = regression.m;
    }

    const seasonal: number[] = [];
    for (let i = 0; i < adjustedSeasonLength; i++) {
      const seasonalValues = [];
      for (let j = i; j < n; j += adjustedSeasonLength) {
        seasonalValues.push(data[j]);
      }
      if (seasonalValues.length > 0) {
        seasonal[i] = ss.mean(seasonalValues) - level;
      } else {
        seasonal[i] = 0;
      }
    }

    // Apply Holt-Winters smoothing
    const smoothed: number[] = [];
    const levels: number[] = [level];
    const trends: number[] = [trend];
    const seasonals: number[] = [...seasonal];

    for (let i = 0; i < n; i++) {
      const seasonalIndex = i % adjustedSeasonLength;

      // Update level
      const newLevel = alpha * (data[i] - seasonals[seasonalIndex]) + (1 - alpha) * (level + trend);

      // Update trend
      const newTrend = beta * (newLevel - level) + (1 - beta) * trend;

      // Update seasonal
      seasonals[seasonalIndex] = gamma * (data[i] - newLevel) + (1 - gamma) * seasonals[seasonalIndex];

      smoothed.push(newLevel + newTrend + seasonals[seasonalIndex]);

      level = newLevel;
      trend = newTrend;
      levels.push(level);
      trends.push(trend);
    }

    // Generate forecasts
    const forecast: number[] = [];
    for (let i = 1; i <= forecastPeriods; i++) {
      const seasonalIndex = (n + i - 1) % adjustedSeasonLength;
      const forecastValue = level + i * trend + seasonals[seasonalIndex];
      forecast.push(forecastValue);
    }

    // Calculate standard error for confidence intervals
    const residuals = data.map((value, index) => value - smoothed[index]);
    const standardError = ss.standardDeviation(residuals);

    return {
      forecast,
      standardError,
      level,
      trend,
      seasonal: seasonals
    };
  }

  // Generate statistical validation metrics
  generateStatisticalValidation(): void {
    console.log('Generating statistical validation...', {
      hasData: !!this.digitalUsageData,
      dataLength: this.digitalUsageData?.length
    });

    if (!this.digitalUsageData || this.digitalUsageData.length < 3) {
      console.warn('Insufficient data for statistical validation (minimum 3 data points required)');
      this.validationMetrics = null;
      this.modelMetadata = null;
      return;
    }

    try {
      // Sort data by period
      const sortedData = [...this.digitalUsageData].sort((a, b) => {
        return this.comparePeriods(a.Period, b.Period);
      });
      console.log('📊 Sorted data for validation:', sortedData.length, 'items');

      // Use appropriate portion for back-testing based on data size
      const backtestMonths = Math.min(6, Math.floor(sortedData.length / 3));
      const trainingData = sortedData.slice(0, sortedData.length - backtestMonths);
      const testData = sortedData.slice(sortedData.length - backtestMonths);
      console.log('📊 Data split:', {
        total: sortedData.length,
        training: trainingData.length,
        test: testData.length,
        backtestMonths
      });

      // Generate forecasts for test period using training data
      const totalTxns = trainingData.map(item => item.TotalTxns);
      const actualValues = testData.map(item => item.TotalTxns);
      console.log('📊 Extracted values:', { totalTxns: totalTxns.slice(0, 3), actualValues });

      // Simple trend-based forecast for validation
      const trendForecast = this.generateTrendForecast(totalTxns, backtestMonths);
      console.log('📊 Generated trend forecast:', trendForecast);

      // Calculate accuracy metrics
      const mae = this.calculateMAE(actualValues, trendForecast);
      const mape = this.calculateMAPE(actualValues, trendForecast);
      const rmse = this.calculateRMSE(actualValues, trendForecast);
      console.log('📊 Calculated metrics:', { mae, mape, rmse });

      // Ljung-Box test for residual autocorrelation
      const residuals = actualValues.map((actual, index) => actual - trendForecast[index]);
      const ljungBoxP = this.ljungBoxTest(residuals);
      console.log('📊 Ljung-Box test result:', ljungBoxP);

      this.validationMetrics = {
        mae,
        mape,
        rmse,
        ljungBoxP,
        backtestMonths
      };

      this.modelMetadata = {
        version: '1.0.0',
        lastUpdated: new Date(),
        trainingSize: trainingData.length,
        validationMethod: 'Time Series Cross-Validation'
      };

      console.log('📊 Statistical validation completed successfully:', this.validationMetrics);
    } catch (error) {
      console.error('Error generating statistical validation:', error);
      this.validationMetrics = null;
      this.modelMetadata = null;
    }
  }

  // Calculate Mean Absolute Error
  private calculateMAE(actual: number[], forecast: number[]): number {
    const errors = actual.map((a, i) => Math.abs(a - forecast[i]));
    return ss.mean(errors);
  }

  // Calculate Mean Absolute Percentage Error
  private calculateMAPE(actual: number[], forecast: number[]): number {
    const percentageErrors = actual.map((a, i) => {
      if (a === 0) return 0;
      return Math.abs((a - forecast[i]) / a) * 100;
    });
    return ss.mean(percentageErrors);
  }

  // Calculate Root Mean Square Error
  private calculateRMSE(actual: number[], forecast: number[]): number {
    const squaredErrors = actual.map((a, i) => Math.pow(a - forecast[i], 2));
    return Math.sqrt(ss.mean(squaredErrors));
  }

  // Simple trend-based forecast for validation
  private generateTrendForecast(data: number[], periods: number): number[] {
    const n = data.length;
    const x = Array.from({length: n}, (_, i) => i);
    const regression = ss.linearRegression(x.map((xi, i) => [xi, data[i]]));

    const forecast: number[] = [];
    for (let i = 0; i < periods; i++) {
      const forecastValue = regression.m * (n + i) + regression.b;
      forecast.push(Math.max(0, forecastValue));
    }

    return forecast;
  }

  // Ljung-Box test for residual autocorrelation (simplified implementation)
  private ljungBoxTest(residuals: number[]): number {
    const n = residuals.length;
    const lags = Math.min(10, Math.floor(n / 4));

    let statistic = 0;
    for (let lag = 1; lag <= lags; lag++) {
      const autocorr = this.calculateAutocorrelation(residuals, lag);
      statistic += (autocorr * autocorr) / (n - lag);
    }

    statistic *= n * (n + 2);

    // Convert to approximate p-value (simplified)
    // In practice, this would use chi-square distribution
    const pValue = Math.exp(-statistic / (2 * lags));
    return Math.min(1, Math.max(0, pValue));
  }

  // Calculate autocorrelation at specific lag
  private calculateAutocorrelation(data: number[], lag: number): number {
    const n = data.length;
    const mean = ss.mean(data);

    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < n - lag; i++) {
      numerator += (data[i] - mean) * (data[i + lag] - mean);
    }

    for (let i = 0; i < n; i++) {
      denominator += Math.pow(data[i] - mean, 2);
    }

    return denominator === 0 ? 0 : numerator / denominator;
  }
}
