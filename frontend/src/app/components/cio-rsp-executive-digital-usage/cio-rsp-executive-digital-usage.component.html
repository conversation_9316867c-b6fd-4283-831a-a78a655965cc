<!-- Professional Page Header with Period Selector and Document Export -->
<header class="page-header">
  <div class="header-left">
    <h1>CIO RSP Executive Digital Usage</h1>
    <p class="header-subtitle">Comprehensive analytics and insights dashboard</p>
  </div>

  <div class="header-right">
    <!-- Controls Container -->
    <div class="header-controls">
      <!-- Main Period Selector -->
      <div class="main-period-selector">
        <mat-form-field appearance="fill" class="period-selector-field">
          <mat-label>Select Period</mat-label>
          <mat-select [(value)]="mainSelectedPeriod" (selectionChange)="onMainPeriodChange()">
            <mat-option value="">All Periods</mat-option>
            @for (period of availablePeriods; track period) {
              <mat-option [value]="period">{{ period }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
      </div>

      <!-- Document Export Buttons -->
      <div class="document-export-buttons">
        <button mat-raised-button
                color="primary"
                [disabled]="isDocumentExporting()"
                (click)="exportToPowerPoint()"
                class="header-export-button"
                [attr.aria-label]="isDocumentExporting() ? 'Exporting to PowerPoint...' : 'Export to PowerPoint'">
          <mat-icon>slideshow</mat-icon>
          @if (isDocumentExporting()) {
            Exporting...
          } @else {
            PowerPoint
          }
        </button>

        <button mat-raised-button
                color="accent"
                [disabled]="isDocumentExporting()"
                (click)="exportToPDF()"
                class="header-export-button"
                [attr.aria-label]="isDocumentExporting() ? 'Exporting to PDF...' : 'Export to PDF'">
          <mat-icon>picture_as_pdf</mat-icon>
          @if (isDocumentExporting()) {
            Exporting...
          } @else {
            PDF
          }
        </button>
      </div>
    </div>

    <!-- Export Progress -->
    @if (isDocumentExporting()) {
      <div class="export-progress-container">
        <mat-progress-bar
          mode="determinate"
          [value]="getExportProgress()"
          class="export-progress-bar"
          [attr.aria-label]="'Export progress: ' + getExportProgress() + '%'">
        </mat-progress-bar>
        <div class="export-status" role="status" aria-live="polite">{{ getExportStatus() }}</div>
      </div>
    }
  </div>
</header>

<mat-divider></mat-divider>
<!-- Digital Usage History Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Digital Usage History</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <!-- Period Comparison Controls (Persistent across all tabs) -->
    <div class="period-comparison-section">
      <h4>
        <mat-icon>date_range</mat-icon>
        Period Comparison & Data Filtering
      </h4>
      <div class="comparison-selectors">
        <mat-form-field appearance="outline" class="period-selector">
          <mat-label>Compare From</mat-label>
          <mat-select [(value)]="selectedMonth1" (selectionChange)="onMonthSelectionChange()">
            @for (period of availablePeriods; track period) {
              <mat-option [value]="period">{{ period }}</mat-option>
            }
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="period-selector">
          <mat-label>Compare To</mat-label>
          <mat-select [(value)]="selectedMonth2" (selectionChange)="onMonthSelectionChange()">
            @for (period of availablePeriods; track period) {
              <mat-option [value]="period">{{ period }}</mat-option>
            }
          </mat-select>
        </mat-form-field>

        @if (selectedMonth1 && selectedMonth2) {
          <div class="filter-info">
            <mat-icon>filter_alt</mat-icon>
            Showing complete historical range: {{ selectedMonth1 }} to {{ selectedMonth2 }}
            @if (availablePeriods && availablePeriods.length > 0) {
              <span class="period-count">({{ availablePeriods.length }} periods)</span>
            }
          </div>
        } @else {
          <div class="filter-info">
            <mat-icon>info</mat-icon>
            Loading period data - will auto-select complete historical range
          </div>
        }
      </div>
    </div>

    <mat-tab-group>
      <!-- Chart tab -->
      <mat-tab label="Chart">
        <!-- Compact Chart Controls -->
        <div class="chart-controls-container">
          <div class="controls-row">
            <!-- Chart Options Controls -->
            <div class="chart-options-controls">
              <h4>Chart Options</h4>
              <div class="options-row">
                <div class="options-checkboxes">
                  <mat-checkbox [(ngModel)]="showTrendlines" (change)="onChartOptionChange()">
                    Trendlines
                  </mat-checkbox>
                  <mat-checkbox [(ngModel)]="showProjections" (change)="onChartOptionChange()">
                    Projections
                  </mat-checkbox>
                  <mat-checkbox [(ngModel)]="showConfidenceInterval" (change)="onChartOptionChange()">
                    Confidence Intervals
                  </mat-checkbox>
                  <mat-checkbox [(ngModel)]="showHoltWinters" (change)="onChartOptionChange()">
                    Holt-Winters Forecast
                  </mat-checkbox>

                  <!-- Projection Period Selector - Right next to Holt-Winters -->
                  <div class="projection-controls" [style.opacity]="showProjections ? '1' : '0.5'">
                    <mat-form-field appearance="outline" class="projection-selector">
                      <mat-label>Projection Months</mat-label>
                      <mat-select [(value)]="projectionMonths" (selectionChange)="onChartOptionChange()" [disabled]="!showProjections">
                        <mat-option [value]="3">3 months</mat-option>
                        <mat-option [value]="6">6 months</mat-option>
                        <mat-option [value]="9">9 months</mat-option>
                        <mat-option [value]="12">12 months</mat-option>
                        <mat-option [value]="18">18 months</mat-option>
                        <mat-option [value]="24">24 months</mat-option>
                      </mat-select>
                    </mat-form-field>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Export buttons for Digital Usage History -->
        <div class="export-buttons-container">
          <button mat-raised-button [matMenuTriggerFor]="digitalUsageExportMenu"
                  [disabled]="isChartExporting('digitalUsageHistory')"
                  class="export-button"
                  [attr.aria-label]="isChartExporting('digitalUsageHistory') ? 'Exporting chart...' : 'Export Digital Usage History chart'">
            <mat-icon>download</mat-icon>
            @if (isChartExporting('digitalUsageHistory')) {
              Exporting...
            } @else {
              Export Chart
            }
          </button>
          <mat-menu #digitalUsageExportMenu="matMenu">
            <button mat-menu-item (click)="exportChart('digitalUsageHistory', 'png')">
              <mat-icon>image</mat-icon>
              <span>Export as PNG</span>
            </button>
            <button mat-menu-item (click)="exportChart('digitalUsageHistory', 'jpeg')">
              <mat-icon>image</mat-icon>
              <span>Export as JPEG</span>
            </button>
            <button mat-menu-item (click)="exportChart('digitalUsageHistory', 'svg')">
              <mat-icon>vector_image</mat-icon>
              <span>Export as SVG</span>
            </button>
          </mat-menu>
        </div>

        <div class="chart-container">
          <ag-charts [options]="options"></ag-charts>
        </div>
      </mat-tab>

      <!-- Key Insights tab -->
      <mat-tab label="Key Insights">
        <div class="key-insights-container">

          <!-- Month Comparison Analysis Section -->
          @if (selectedMonth1 && selectedMonth2) {
            <div class="insights-section">
              <h3>📊 Month Comparison Analysis</h3>
              <p class="section-description">Detailed comparison between {{ selectedMonth1 }} and {{ selectedMonth2 }}</p>

              @if (getMonthComparisonAnalysis(); as comparison) {
                <div class="comparison-grid">
                  <!-- Volume Comparisons -->
                  <div class="comparison-card">
                    <h4>Volume Comparisons</h4>
                    @for (comp of comparison.comparisons; track comp.metric) {
                      <div class="metric-comparison">
                        <strong>{{ comp.metric }}</strong>
                        <div class="comparison-values">
                          <span>{{ comparison.month1 }}: {{ comp.month1Value | number }}</span>
                          <span>{{ comparison.month2 }}: {{ comp.month2Value | number }}</span>
                        </div>
                        <div class="comparison-change"
                             [class]="comp.percentageChange >= 0 ? 'positive' : 'negative'">
                          Change: {{ comp.difference | number }} ({{ comp.percentageChange | number:'1.1-1' }}%)
                        </div>
                      </div>
                    }
                  </div>

                  <!-- Channel Mix Analysis -->
                  <div class="comparison-card">
                    <h4>Channel Mix Analysis</h4>
                    <div class="channel-mix">
                      <div class="mix-comparison">
                        <span>{{ comparison.month1 }} API %: {{ comparison.apiPercentagePrevious | number:'1.1-1' }}%</span>
                        <span>{{ comparison.month2 }} API %: {{ comparison.apiPercentageLatest | number:'1.1-1' }}%</span>
                      </div>
                      <div class="channel-shift"
                           [class]="comparison.channelMixShift >= 0 ? 'positive' : 'negative'">
                        Channel Mix Shift: {{ comparison.channelMixShift | number:'1.1-1' }}%
                        {{ comparison.channelMixShift >= 0 ? 'towards API' : 'towards Portal' }}
                      </div>
                    </div>
                  </div>
                </div>
              }
            </div>
          }

          <!-- Key Observations Section -->
          <div class="insights-section">
            <h3>🔍 Key Observations</h3>
            <p class="section-description">Significant changes and patterns identified in the data</p>
            <div class="observations-list">
              @for (observation of getKeyObservations(); track observation) {
                <div class="observation-item">
                  <mat-icon>trending_up</mat-icon>
                  <span>{{ observation }}</span>
                </div>
              }
            </div>
          </div>

          <!-- Relative Performance Analysis Section -->
          <div class="insights-section">
            <h3>📈 Relative Performance Analysis</h3>
            <p class="section-description">Performance trends and growth rate analysis</p>
            <div class="performance-list">
              @for (insight of getRelativePerformanceInsights(); track insight) {
                <div class="performance-item">
                  <mat-icon>analytics</mat-icon>
                  <span>{{ insight }}</span>
                </div>
              }
            </div>
          </div>

          <!-- Advanced Analytics Section -->
          <div class="insights-section">
            <h3>🧮 Advanced Analytics</h3>
            <p class="section-description">Compound growth rates, volatility analysis, and statistical metrics</p>
            <div class="analytics-list">
              @for (analytic of getAdvancedAnalytics(); track analytic) {
                <div class="analytics-item">
                  <mat-icon>calculate</mat-icon>
                  <span>{{ analytic }}</span>
                </div>
              }
            </div>
          </div>

          <!-- Channel Mix Trendline & Variances Section -->
          <div class="insights-section">
            <h3>🔄 Channel Mix Trendline & Variances</h3>
            <p class="section-description">Channel mix analysis with trendline predictions and variance calculations</p>

            <!-- Channel Mix Insights -->
            <div class="channel-mix-insights">
              @for (insight of getChannelMixInsights(); track insight) {
                <div class="channel-insight-item">
                  <mat-icon>swap_horiz</mat-icon>
                  <span>{{ insight }}</span>
                </div>
              }
            </div>

            <!-- Channel Mix Data Table -->
            @if (getChannelMixAnalysis().length > 0) {
              <div class="channel-mix-table-container">
                <h4>Channel Mix Variance Data</h4>
                <table class="channel-mix-table">
                  <thead>
                    <tr>
                      <th>Period</th>
                      <th>Actual API %</th>
                      <th>Expected API %</th>
                      <th>Variance from Avg</th>
                      <th>Variance from Trend</th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (item of getChannelMixAnalysis(); track item.Period) {
                      <tr>
                        <td>{{ item.Period }}</td>
                        <td>{{ item.ActualAPIPercent | number:'1.1-1' }}%</td>
                        <td>{{ item.PredictedAPIPercent | number:'1.1-1' }}%</td>
                        <td [style.background-color]="getVarianceColor(item.APIMixVariance)"
                            [style.color]="getVarianceTextColor(item.APIMixVariance)"
                            class="variance-cell">
                          {{ item.APIMixVariance | number:'1.1-1' }}%
                        </td>
                        <td [style.background-color]="getVarianceColor(item.APIMixTrendVariance || 0)"
                            [style.color]="getVarianceTextColor(item.APIMixTrendVariance || 0)"
                            class="variance-cell">
                          {{ (item.APIMixTrendVariance || 0) | number:'1.1-1' }}%
                        </td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>
            }
          </div>

          <!-- Key Insights Summary Section -->
          <div class="insights-section">
            <h3>📋 Key Insights Summary</h3>
            <p class="section-description">Comprehensive summary of all analytical insights</p>
            <div class="summary-insights">
              @for (insight of getAllInsightsSummary(); track insight) {
                <div class="summary-insight-item">
                  <mat-icon>lightbulb</mat-icon>
                  <span>{{ insight }}</span>
                </div>
              }
            </div>
          </div>

        </div>
      </mat-tab>

      <!-- Enhanced Data tab -->
      <mat-tab label="Data">
        <div class="data-table-container">

          <!-- Raw Historical Data Section -->
          <div class="data-section">
            <h3>📊 Historical Data</h3>
            <p class="section-description">Raw transaction data for the selected time period</p>
            <table class="data-table">
              <thead>
                <tr>
                  <th>Period</th>
                  <th>Total Transactions</th>
                  <th>API Transactions</th>
                  <th>Portal Transactions</th>
                  <th>API %</th>
                </tr>
              </thead>
              <tbody>
                @for (item of (isDateRangeActive ? filteredDigitalUsageData : storedRawDigitalUsageData); track item.Period) {
                  <tr>
                    <td>{{ item.Period }}</td>
                    <td>{{ item.TotalTxns | number }}</td>
                    <td>{{ item.TotalAPITxns | number }}</td>
                    <td>{{ item.TotalPortalTxns | number }}</td>
                    <td>{{ (item.TotalAPITxns / item.TotalTxns) * 100 | number:'1.1-1' }}%</td>
                  </tr>
                }
              </tbody>
            </table>
          </div>

          <!-- Trend Analysis Section -->
          @if (showTrendlines && trendData.length > 0) {
            <div class="data-section">
              <h3>📈 Trend Analysis</h3>
              <p class="section-description">Calculated trendlines and growth rates based on polynomial regression</p>
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Period</th>
                    <th>Total Trend</th>
                    <th>API Trend</th>
                    <th>Portal Trend</th>
                    <th>Total Growth %</th>
                    <th>API Growth %</th>
                    <th>Portal Growth %</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of trendData; track item.Period) {
                    <tr>
                      <td>{{ item.Period }}</td>
                      <td>{{ item.TotalTxnsTrend | number:'1.0-0' }}</td>
                      <td>{{ item.TotalAPITxnsTrend | number:'1.0-0' }}</td>
                      <td>{{ item.TotalPortalTxnsTrend | number:'1.0-0' }}</td>
                      <td [class]="item.TotalTxnsTrendGrowth >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.TotalTxnsTrendGrowth | number:'1.1-1' }}%
                      </td>
                      <td [class]="item.TotalAPITxnsTrendGrowth >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.TotalAPITxnsTrendGrowth | number:'1.1-1' }}%
                      </td>
                      <td [class]="item.TotalPortalTxnsTrendGrowth >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.TotalPortalTxnsTrendGrowth | number:'1.1-1' }}%
                      </td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          }

          <!-- Projection Data Section -->
          @if (showProjections && projectionData.length > 0) {
            <div class="data-section">
              <h3>🔮 Future Projections</h3>
              <p class="section-description">Projected values for the next {{ projectionMonths }} months based on trend analysis</p>
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Period</th>
                    <th>Total Projected</th>
                    <th>API Projected</th>
                    <th>Portal Projected</th>
                    @if (showConfidenceInterval) {
                      <th>Total Range</th>
                      <th>API Range</th>
                      <th>Portal Range</th>
                    }
                    <th>Total Growth %</th>
                    <th>API Growth %</th>
                    <th>Portal Growth %</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of projectionData; track item.Period) {
                    <tr class="projection-row">
                      <td><strong>{{ item.Period }}</strong></td>
                      <td>{{ item.TotalTxns | number }}</td>
                      <td>{{ item.TotalAPITxns | number }}</td>
                      <td>{{ item.TotalPortalTxns | number }}</td>
                      @if (showConfidenceInterval) {
                        <td class="confidence-range">
                          {{ item.TotalTxnsLower | number }} - {{ item.TotalTxnsUpper | number }}
                        </td>
                        <td class="confidence-range">
                          {{ item.TotalAPITxnsLower | number }} - {{ item.TotalAPITxnsUpper | number }}
                        </td>
                        <td class="confidence-range">
                          {{ item.TotalPortalTxnsLower | number }} - {{ item.TotalPortalTxnsUpper | number }}
                        </td>
                      }
                      <td [class]="item.ProjectedGrowthTotal >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.ProjectedGrowthTotal | number:'1.1-1' }}%
                      </td>
                      <td [class]="item.ProjectedGrowthAPI >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.ProjectedGrowthAPI | number:'1.1-1' }}%
                      </td>
                      <td [class]="item.ProjectedGrowthPortal >= 0 ? 'positive-growth' : 'negative-growth'">
                        {{ item.ProjectedGrowthPortal | number:'1.1-1' }}%
                      </td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          }

          <!-- Holt-Winters Forecast Section -->
          @if (showHoltWinters && holtWintersData.length > 0) {
            <div class="data-section">
              <h3>🔮 Holt-Winters Exponential Smoothing Forecast</h3>
              <p class="section-description">Advanced seasonal forecasting with 80% and 95% confidence intervals</p>
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Period</th>
                    <th>Total Forecast</th>
                    <th>API Forecast</th>
                    <th>Portal Forecast</th>
                    <th>80% CI Range</th>
                    <th>95% CI Range</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of holtWintersData; track item.Period) {
                    <tr class="forecast-row">
                      <td><strong>{{ item.Period }}</strong></td>
                      <td>{{ item.TotalForecast | number }}</td>
                      <td>{{ item.APIForecast | number }}</td>
                      <td>{{ item.PortalForecast | number }}</td>
                      <td class="confidence-range">
                        {{ item.Total80Lower | number }} - {{ item.Total80Upper | number }}
                      </td>
                      <td class="confidence-range">
                        {{ item.Total95Lower | number }} - {{ item.Total95Upper | number }}
                      </td>
                    </tr>
                  }
                </tbody>
              </table>

              @if (holtWintersMetadata) {
                <div class="forecast-metadata">
                  <h4>Forecast Model Information</h4>
                  <div class="metadata-grid">
                    <div class="metadata-item">
                      <strong>Model Type:</strong> Holt-Winters Exponential Smoothing
                    </div>
                    <div class="metadata-item">
                      <strong>Seasonality:</strong> {{ holtWintersMetadata.seasonality }}
                    </div>
                    <div class="metadata-item">
                      <strong>Data Points Used:</strong> {{ holtWintersMetadata.dataPoints }}
                    </div>
                    <div class="metadata-item">
                      <strong>Generated:</strong> {{ holtWintersMetadata.timestamp | date:'medium' }}
                    </div>
                  </div>
                </div>
              }
            </div>
          }

          <!-- Statistical Validation Section -->
          @if (validationMetrics) {
            <div class="data-section">
              <h3>📊 Statistical Validation & Accuracy Metrics</h3>
              <p class="section-description">Back-testing validation and forecast accuracy assessment</p>

              <div class="validation-grid">
                <div class="validation-card">
                  <h4>Forecast Accuracy</h4>
                  <div class="metric-item">
                    <strong>MAE (Mean Absolute Error):</strong> {{ validationMetrics.mae | number:'1.0-0' }}
                  </div>
                  <div class="metric-item">
                    <strong>MAPE (Mean Absolute Percentage Error):</strong> {{ validationMetrics.mape | number:'1.1-1' }}%
                  </div>
                  <div class="metric-item">
                    <strong>RMSE (Root Mean Square Error):</strong> {{ validationMetrics.rmse | number:'1.0-0' }}
                  </div>
                </div>

                <div class="validation-card">
                  <h4>Model Diagnostics</h4>
                  <div class="metric-item">
                    <strong>Ljung-Box Test p-value:</strong> {{ validationMetrics.ljungBoxP | number:'1.3-3' }}
                  </div>
                  <div class="metric-item">
                    <strong>Residual Autocorrelation:</strong>
                    <span [class]="validationMetrics.ljungBoxP > 0.05 ? 'validation-pass' : 'validation-fail'">
                      {{ validationMetrics.ljungBoxP > 0.05 ? 'PASS' : 'FAIL' }}
                    </span>
                  </div>
                  <div class="metric-item">
                    <strong>Back-testing Period:</strong> {{ validationMetrics.backtestMonths }} months
                  </div>
                </div>
              </div>

              @if (modelMetadata) {
                <div class="model-metadata">
                  <h4>Model Metadata</h4>
                  <div class="metadata-grid">
                    <div class="metadata-item">
                      <strong>Model Version:</strong> {{ modelMetadata.version }}
                    </div>
                    <div class="metadata-item">
                      <strong>Last Updated:</strong> {{ modelMetadata.lastUpdated | date:'medium' }}
                    </div>
                    <div class="metadata-item">
                      <strong>Training Data Size:</strong> {{ modelMetadata.trainingSize }} periods
                    </div>
                    <div class="metadata-item">
                      <strong>Validation Method:</strong> {{ modelMetadata.validationMethod }}
                    </div>
                  </div>
                </div>
              }
            </div>
          }

          <!-- Holt-Winters Forecast Data Section -->
          @if (holtWintersData.length > 0) {
            <div class="data-section">
              <h3>🔮 Holt-Winters Forecast Data</h3>
              <p class="section-description">Advanced exponential smoothing forecasts with confidence intervals</p>

              <div class="data-table-container">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th>Period</th>
                      <th>Total Forecast</th>
                      <th>API Forecast</th>
                      <th>Portal Forecast</th>
                      <th>95% Lower</th>
                      <th>95% Upper</th>
                    </tr>
                  </thead>
                  <tbody>
                    @for (item of holtWintersData; track item.Period) {
                      <tr>
                        <td>{{ item.Period }}</td>
                        <td>{{ item.TotalForecast | number }}</td>
                        <td>{{ item.APIForecast | number }}</td>
                        <td>{{ item.PortalForecast | number }}</td>
                        <td>{{ item.Total95Lower | number }}</td>
                        <td>{{ item.Total95Upper | number }}</td>
                      </tr>
                    }
                  </tbody>
                </table>
              </div>

              @if (holtWintersMetadata) {
                <div class="forecast-metadata">
                  <h4>Forecast Metadata</h4>
                  <div class="metadata-grid">
                    <div class="metadata-item">
                      <strong>Seasonality Detected:</strong> {{ holtWintersMetadata.seasonality }}
                    </div>
                    <div class="metadata-item">
                      <strong>Training Data Points:</strong> {{ holtWintersMetadata.dataPoints }}
                    </div>
                    <div class="metadata-item">
                      <strong>Forecast Horizon:</strong> {{ holtWintersMetadata.forecastHorizon }} periods
                    </div>
                    <div class="metadata-item">
                      <strong>Generated:</strong> {{ holtWintersMetadata.timestamp | date:'medium' }}
                    </div>
                  </div>
                </div>
              }
            </div>
          }

          <!-- Data Summary Section -->
          <div class="data-section">
            <h3>📋 Data Summary</h3>
            <div class="summary-grid">
              <div class="summary-card">
                <h4>Historical Data</h4>
                <p><strong>{{ rawDigitalUsageData.length }}</strong> periods</p>
                <p>From {{ rawDigitalUsageData[0]?.Period }} to {{ rawDigitalUsageData[rawDigitalUsageData.length - 1]?.Period }}</p>
              </div>
              @if (showTrendlines) {
                <div class="summary-card">
                  <h4>Trend Analysis</h4>
                  <p><strong>Polynomial regression</strong> (degree 2)</p>
                  <p>{{ trendData.length }} trend calculations</p>
                </div>
              }
              @if (showProjections) {
                <div class="summary-card">
                  <h4>Projections</h4>
                  <p><strong>{{ projectionMonths }}</strong> months ahead</p>
                  @if (showConfidenceInterval) {
                    <p>With confidence intervals</p>
                  }
                </div>
              }
              @if (holtWintersData.length > 0) {
                <div class="summary-card">
                  <h4>Holt-Winters Forecast</h4>
                  <p><strong>Exponential smoothing</strong> with seasonality</p>
                  <p>{{ holtWintersData.length }} forecast periods</p>
                  @if (showHoltWinters) {
                    <p><em>Currently visible on chart</em></p>
                  } @else {
                    <p><em>Available in data (toggle chart to view)</em></p>
                  }
                </div>
              }
              @if (validationMetrics) {
                <div class="summary-card">
                  <h4>Statistical Validation</h4>
                  <p><strong>Back-testing</strong> validation</p>
                  <p>MAPE: {{ validationMetrics.mape | number:'1.1-1' }}%</p>
                </div>
              }
            </div>
          </div>

        </div>
      </mat-tab>

      <!-- GenAI Insights tab -->
      <mat-tab label="GenAI Insights">
        <div class="insights-container">
          <h3>AI-Generated Insights</h3>
          <div class="insights-actions">
            <button mat-raised-button
                    color="primary"
                    (click)="generateInsights()"
                    [disabled]="isGeneratingInsights"
                    class="professional-action-button"
                    [attr.aria-label]="isGeneratingInsights ? 'Generating AI insights...' : 'Generate AI insights'">
              <mat-icon>psychology</mat-icon>
              @if (isGeneratingInsights) {
                Generating...
              } @else {
                Generate Insights
              }
            </button>
            <button mat-raised-button
                    color="accent"
                    (click)="refreshInsights()"
                    [disabled]="isGeneratingInsights || !generatedInsights"
                    class="professional-action-button secondary-button"
                    [attr.aria-label]="'Refresh AI insights'">
              <mat-icon>refresh</mat-icon>
              Refresh
            </button>
          </div>

          <div class="insights-content">
            @if (isGeneratingInsights) {
              <div class="loading-insights">
                <mat-spinner diameter="30"></mat-spinner>
                <p>⏳ Generating AI insights...</p>
              </div>
            } @else if (generatedInsights) {
              <div class="ai-insights">
                <div class="insights-text" [innerHTML]="formatInsightsForDisplay(generatedInsights)"></div>
              </div>
            } @else {
              <div class="insights-placeholder">
                <p>Click 'Generate Insights' to analyze the data with AI.</p>
                <p class="insights-description">
                  AI will analyze trends, patterns, and provide actionable recommendations based on your digital usage data.
                </p>
              </div>
            }
          </div>
        </div>
      </mat-tab>

      <!-- Statistical Analysis tab -->
      <mat-tab label="Statistical Analysis">
        <div class="insights-container">
          <h3>Statistical Analysis</h3>
          <div class="insights-actions">
            <button mat-raised-button
                    color="primary"
                    (click)="generateStatisticalAnalysis()"
                    [disabled]="isGeneratingStatisticalAnalysis"
                    class="professional-action-button"
                    [attr.aria-label]="isGeneratingStatisticalAnalysis ? 'Generating statistical analysis...' : 'Generate statistical analysis'">
              <mat-icon>analytics</mat-icon>
              @if (isGeneratingStatisticalAnalysis) {
                Generating...
              } @else {
                Generate Analysis
              }
            </button>
            <button mat-raised-button
                    color="accent"
                    (click)="refreshStatisticalAnalysis()"
                    [disabled]="isGeneratingStatisticalAnalysis || !statisticalAnalysis"
                    class="professional-action-button secondary-button"
                    [attr.aria-label]="'Refresh statistical analysis'">
              <mat-icon>refresh</mat-icon>
              Refresh
            </button>
          </div>

          <div class="insights-content">
            @if (isGeneratingStatisticalAnalysis) {
              <div class="loading-insights">
                <mat-spinner diameter="30"></mat-spinner>
                <p>⏳ Generating statistical analysis...</p>
              </div>
            } @else if (statisticalAnalysis) {
              <div class="ai-insights">
                <div class="insights-text" [innerHTML]="formatInsightsForDisplay(statisticalAnalysis)"></div>
              </div>
            } @else {
              <div class="insights-placeholder">
                <p>Click 'Generate Analysis' to analyze the data with statistical methods.</p>
                <p class="insights-description">
                  Statistical analysis provides mathematical insights based on growth rates, trends, and variance calculations.
                </p>
              </div>
            }
          </div>
        </div>
      </mat-tab>
    </mat-tab-group>
  </mat-card-content>
</mat-card>

<mat-divider></mat-divider>






<!-- Digital Usage By Service Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Digital Usage By Service</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    @if (mainSelectedPeriod) {
      <mat-tab-group>
        <!-- Service Usage Chart Tab -->
        <mat-tab label="Graph">
          @if (isLoadingServiceUsage) {
            <div class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading service usage data...</p>
            </div>
          } @else {
            <!-- Export buttons for Service Usage -->
            <div class="export-buttons-container">
              <button mat-raised-button [matMenuTriggerFor]="serviceUsageExportMenu"
                      [disabled]="isChartExporting('serviceUsage')"
                      class="export-button"
                      [attr.aria-label]="isChartExporting('serviceUsage') ? 'Exporting chart...' : 'Export Service Usage chart'">
                <mat-icon>download</mat-icon>
                @if (isChartExporting('serviceUsage')) {
                  Exporting...
                } @else {
                  Export Chart
                }
              </button>
              <mat-menu #serviceUsageExportMenu="matMenu">
                <button mat-menu-item (click)="exportChart('serviceUsage', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export as PNG</span>
                </button>
                <button mat-menu-item (click)="exportChart('serviceUsage', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportChart('serviceUsage', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export as SVG</span>
                </button>
              </mat-menu>

              <!-- Export all service charts button -->
              <button mat-raised-button [matMenuTriggerFor]="allServiceExportMenu"
                      class="export-button export-all-button"
                      [attr.aria-label]="'Export all service charts'">
                <mat-icon>download_for_offline</mat-icon>
                Export All Service Charts
              </button>
              <mat-menu #allServiceExportMenu="matMenu">
                <button mat-menu-item (click)="exportAllCharts('serviceUsage', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export All as PNG</span>
                </button>
                <button mat-menu-item (click)="exportAllCharts('serviceUsage', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export All as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportAllCharts('serviceUsage', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export All as SVG</span>
                </button>
              </mat-menu>
            </div>

            <div class="chart-container">
              <ag-charts [options]="serviceUsageOptions"></ag-charts>
            </div>

            <!-- API Usage Percentage by Service Type -->
            <div class="service-type-charts">
              <div class="service-type-header">
                <h3>API Usage Percentage by Service Type</h3>
                <!-- Export buttons for service type charts -->
                <div class="export-buttons-container">
                  <button mat-raised-button [matMenuTriggerFor]="connectServicesExportMenu"
                          [disabled]="isChartExporting('connectServices')"
                          class="export-button small-export-button"
                          [attr.aria-label]="isChartExporting('connectServices') ? 'Exporting Connect chart...' : 'Export Connect Services chart'">
                    <mat-icon>download</mat-icon>
                    Export Connect
                  </button>
                  <mat-menu #connectServicesExportMenu="matMenu">
                    <button mat-menu-item (click)="exportChart('connectServices', 'png')">
                      <mat-icon>image</mat-icon>
                      <span>PNG</span>
                    </button>
                    <button mat-menu-item (click)="exportChart('connectServices', 'jpeg')">
                      <mat-icon>image</mat-icon>
                      <span>JPEG</span>
                    </button>
                    <button mat-menu-item (click)="exportChart('connectServices', 'svg')">
                      <mat-icon>vector_image</mat-icon>
                      <span>SVG</span>
                    </button>
                  </mat-menu>

                  <button mat-raised-button [matMenuTriggerFor]="assureServicesExportMenu"
                          [disabled]="isChartExporting('assureServices')"
                          class="export-button small-export-button"
                          [attr.aria-label]="isChartExporting('assureServices') ? 'Exporting Assure chart...' : 'Export Assure Services chart'">
                    <mat-icon>download</mat-icon>
                    Export Assure
                  </button>
                  <mat-menu #assureServicesExportMenu="matMenu">
                    <button mat-menu-item (click)="exportChart('assureServices', 'png')">
                      <mat-icon>image</mat-icon>
                      <span>PNG</span>
                    </button>
                    <button mat-menu-item (click)="exportChart('assureServices', 'jpeg')">
                      <mat-icon>image</mat-icon>
                      <span>JPEG</span>
                    </button>
                    <button mat-menu-item (click)="exportChart('assureServices', 'svg')">
                      <mat-icon>vector_image</mat-icon>
                      <span>SVG</span>
                    </button>
                  </mat-menu>
                </div>
              </div>
              <div class="charts-row">
                <div class="chart-half">
                  <ag-charts [options]="connectServicesOptions"></ag-charts>
                </div>
                <div class="chart-half">
                  <ag-charts [options]="assureServicesOptions"></ag-charts>
                </div>
              </div>
            </div>
          }
        </mat-tab>

        <!-- Service Usage Data Tab -->
        <mat-tab label="Data">
          @if (serviceUsageData.length > 0) {
            <div class="data-table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Service Name</th>
                    <th>Used For Connect</th>
                    <th>Used For Assure</th>
                    <th>Latest Version</th>
                    <th>API Cert Count</th>
                    <th>Total API Txns</th>
                    <th>Total Portal Txns</th>
                    <th>API %</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of serviceUsageData; track item.ServiceName) {
                    <tr>
                      <td>{{ item.ServiceName }}</td>
                      <td>{{ item.UsedForConnect ? 'Yes' : 'No' }}</td>
                      <td>{{ item.UsedForAssure ? 'Yes' : 'No' }}</td>
                      <td>{{ item.LatestVersion }}</td>
                      <td>{{ item.APICertCount | number }}</td>
                      <td>{{ item.TotalAPITxns | number }}</td>
                      <td>{{ item.TotalPortalTxns | number }}</td>
                      <td>{{ item.APIPercentage | number:'1.1-1' }}%</td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          } @else {
            <p>No service usage data available for the selected period.</p>
          }
        </mat-tab>
      </mat-tab-group>
    }
  </mat-card-content>
</mat-card>

<!-- RSP API Adoption & Utilisation Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>RSP API Adoption & Utilisation</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    @if (mainSelectedPeriod) {
      <mat-tab-group>
        <!-- RSP API Adoption Chart Tab -->
        <mat-tab label="Graph">
          @if (isLoadingRspAdoption) {
            <div class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading RSP API adoption data...</p>
            </div>
          } @else {
            <!-- Export buttons for RSP API Adoption -->
            <div class="export-buttons-container">
              <button mat-raised-button [matMenuTriggerFor]="rspApiAdoptionExportMenu"
                      [disabled]="isChartExporting('rspApiAdoption')"
                      class="export-button"
                      [attr.aria-label]="isChartExporting('rspApiAdoption') ? 'Exporting chart...' : 'Export RSP API Adoption chart'">
                <mat-icon>download</mat-icon>
                @if (isChartExporting('rspApiAdoption')) {
                  Exporting...
                } @else {
                  Export Chart
                }
              </button>
              <mat-menu #rspApiAdoptionExportMenu="matMenu">
                <button mat-menu-item (click)="exportChart('rspApiAdoption', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export as PNG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspApiAdoption', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspApiAdoption', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export as SVG</span>
                </button>
              </mat-menu>
            </div>

            <div class="chart-container">
              <ag-charts [options]="rspApiAdoptionOptions"></ag-charts>
            </div>
          }
        </mat-tab>

        <!-- RSP API Adoption Data Tab -->
        <mat-tab label="Data">
          @if (rspApiAdoptionData.length > 0) {
            <div class="data-table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>API Name</th>
                    <th>Certified RSPs</th>
                    <th>Utilizing RSPs</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of rspApiAdoptionData; track item.APIName) {
                    <tr>
                      <td>{{ item.APIName }}</td>
                      <td>{{ item.CertCount | number }}</td>
                      <td>{{ item.UtilCount | number }}</td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          } @else {
            <p>No RSP API adoption data available for the selected period.</p>
          }
        </mat-tab>
      </mat-tab-group>
    } @else {
      <p>Please select a period from the Digital Usage By Service section above.</p>
    }
  </mat-card-content>
</mat-card>

<mat-divider></mat-divider>

<!-- Digital Usage By RSP Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Digital Usage By RSP</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    @if (mainSelectedPeriod) {
      <mat-tab-group>
        <!-- RSP Usage Chart Tab -->
        <mat-tab label="Graph">
          @if (isLoadingRspUsage) {
            <div class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading RSP digital usage data...</p>
            </div>
          } @else {
            <!-- Export buttons for RSP Digital Usage -->
            <div class="export-buttons-container">
              <button mat-raised-button [matMenuTriggerFor]="rspDigitalUsageExportMenu"
                      [disabled]="isChartExporting('rspDigitalUsage')"
                      class="export-button"
                      [attr.aria-label]="isChartExporting('rspDigitalUsage') ? 'Exporting chart...' : 'Export RSP Digital Usage chart'">
                <mat-icon>download</mat-icon>
                @if (isChartExporting('rspDigitalUsage')) {
                  Exporting...
                } @else {
                  Export Chart
                }
              </button>
              <mat-menu #rspDigitalUsageExportMenu="matMenu">
                <button mat-menu-item (click)="exportChart('rspDigitalUsage', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export as PNG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspDigitalUsage', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspDigitalUsage', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export as SVG</span>
                </button>
              </mat-menu>
            </div>

            <div class="chart-container">
              <ag-charts [options]="rspDigitalUsageOptions"></ag-charts>
            </div>
          }
        </mat-tab>

        <!-- RSP Usage Data Tab -->
        <mat-tab label="Data">
          @if (rspDigitalUsageData.length > 0) {
            <div class="data-table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Digital Vol Rank</th>
                    <th>RSP Name</th>
                    <th>% Txns via APIs</th>
                    <th>Total Services</th>
                    <th>Ratio of Digital Txn to Active Services</th>
                    <th>Total API Txns</th>
                    <th>Total Portal Txns</th>
                    <th>Total Txns</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of rspDigitalUsageData; track item.RSPName) {
                    <tr>
                      <td>{{ item.DigitalVolRank }}</td>
                      <td>{{ item.RSPName }}</td>
                      <td>{{ item.APIPercentage | number:'1.1-1' }}%</td>
                      <td>{{ item.TotalServices | number }}</td>
                      <td>{{ item.TxnPerService | number:'1.1-1' }}</td>
                      <td>{{ item.TotalAPITxns | number }}</td>
                      <td>{{ item.TotalPortalTxns | number }}</td>
                      <td>{{ item.TotalTxns | number }}</td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          } @else {
            <p>No RSP digital usage data available for the selected period.</p>
          }
        </mat-tab>
      </mat-tab-group>
    } @else {
      <p>Please select a period from the Digital Usage By Service section above.</p>
    }
  </mat-card-content>
</mat-card>

<mat-divider></mat-divider>

<!-- Digital Usage API% By RSP Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Digital Usage API% By RSP</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    @if (mainSelectedPeriod) {
      <mat-tab-group>
        <!-- RSP API Percentage Chart Tab -->
        <mat-tab label="Graph">
          @if (isLoadingRspPercentage) {
            <div class="loading-container">
              <mat-spinner></mat-spinner>
              <p>Loading RSP API percentage data...</p>
            </div>
          } @else {
            <!-- Export buttons for RSP API Percentage -->
            <div class="export-buttons-container">
              <button mat-raised-button [matMenuTriggerFor]="rspApiPercentageExportMenu"
                      [disabled]="isChartExporting('rspApiPercentage')"
                      class="export-button"
                      [attr.aria-label]="isChartExporting('rspApiPercentage') ? 'Exporting chart...' : 'Export RSP API Percentage chart'">
                <mat-icon>download</mat-icon>
                @if (isChartExporting('rspApiPercentage')) {
                  Exporting...
                } @else {
                  Export Chart
                }
              </button>
              <mat-menu #rspApiPercentageExportMenu="matMenu">
                <button mat-menu-item (click)="exportChart('rspApiPercentage', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export as PNG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspApiPercentage', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportChart('rspApiPercentage', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export as SVG</span>
                </button>
              </mat-menu>

              <!-- Export all RSP charts button -->
              <button mat-raised-button [matMenuTriggerFor]="allRspExportMenu"
                      class="export-button export-all-button"
                      [attr.aria-label]="'Export all RSP charts'">
                <mat-icon>download_for_offline</mat-icon>
                Export All RSP Charts
              </button>
              <mat-menu #allRspExportMenu="matMenu">
                <button mat-menu-item (click)="exportAllCharts('rspUsage', 'png')">
                  <mat-icon>image</mat-icon>
                  <span>Export All as PNG</span>
                </button>
                <button mat-menu-item (click)="exportAllCharts('rspUsage', 'jpeg')">
                  <mat-icon>image</mat-icon>
                  <span>Export All as JPEG</span>
                </button>
                <button mat-menu-item (click)="exportAllCharts('rspUsage', 'svg')">
                  <mat-icon>vector_image</mat-icon>
                  <span>Export All as SVG</span>
                </button>
              </mat-menu>
            </div>

            <div class="chart-container">
              <ag-charts [options]="rspApiPercentageOptions"></ag-charts>
            </div>
          }
        </mat-tab>

        <!-- RSP API Percentage Data Tab -->
        <mat-tab label="Data">
          @if (rspApiPercentageData.length > 0) {
            <div class="data-table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>Digital Vol Rank</th>
                    <th>RSP</th>
                    <th>API%</th>
                    <th>~Svc Count Rank</th>
                  </tr>
                </thead>
                <tbody>
                  @for (item of rspApiPercentageData; track item.RSPName) {
                    <tr>
                      <td>{{ item.DigitalVolRank }}</td>
                      <td>{{ item.RSPName }}</td>
                      <td>{{ item.APIPercentage | number:'1.1-1' }}%</td>
                      <td>{{ item.ServiceCountRank }}</td>
                    </tr>
                  }
                </tbody>
              </table>
            </div>
          } @else {
            <p>No RSP API percentage data available for the selected period.</p>
          }
        </mat-tab>
      </mat-tab-group>
    } @else {
      <p>Please select a period from the Digital Usage By Service section above.</p>
    }
  </mat-card-content>
</mat-card>

<mat-divider></mat-divider>

<!-- Service Portal Users Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Service Portal Users</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="placeholder-section">
      <p class="placeholder-text">Data Coming Soon</p>
      <p class="placeholder-description">Service Portal user analytics will be available in a future release.</p>
    </div>
  </mat-card-content>
</mat-card>

<mat-divider></mat-divider>

<!-- Public Website Users Section -->
<mat-card class="main-section">
  <mat-card-header>
    <mat-card-title>Public Website Users</mat-card-title>
  </mat-card-header>
  <mat-card-content>
    <div class="placeholder-section">
      <p class="placeholder-text">Data Coming Soon</p>
      <p class="placeholder-description">Public website user analytics will be available in a future release.</p>
    </div>
  </mat-card-content>
</mat-card>
