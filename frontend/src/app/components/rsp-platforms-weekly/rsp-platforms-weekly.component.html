<mat-card class="example-card">
  <mat-card-title>
    <mat-icon color="primary">analytics</mat-icon>
    RSP Platforms Weekly Report
  </mat-card-title>

  <!-- Optimized Period Comparison Controls -->
  <mat-card class="period-comparison-card-compact">
    <mat-card-content class="period-controls-compact">
      <div class="period-controls-header">
        <div class="period-title">
          <mat-icon>date_range</mat-icon>
          <span>Period Comparison</span>
        </div>
        <div class="period-actions-compact">
          <button mat-raised-button color="primary" (click)="updateDateRangeFilter()" 
                  [disabled]="!dateRangeStart || !dateRangeEnd" class="compact-button">
            <mat-icon>filter_alt</mat-icon>
            Apply Filter
          </button>
          <button mat-stroked-button (click)="clearDateRangeFilter()" 
                  [disabled]="!isDateRangeActive" class="compact-button">
            <mat-icon>clear</mat-icon>
            Clear Filter
          </button>
        </div>
      </div>
      
      <div class="period-fields-row">
        <mat-form-field appearance="outline" class="period-field-compact">
          <mat-label>Compare From (Week)</mat-label>
          <mat-select [(value)]="dateRangeStart" (selectionChange)="updateDateRangeFilter()">
            @for (period of availablePeriods; track period.WeekEnding) {
              <mat-option [value]="period.WeekEnding">
                {{ period.WeekLabel }} ({{ rspPlatformsWeeklyService.formatWeekEnding(period.WeekEnding) }})
              </mat-option>
            }
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline" class="period-field-compact">
          <mat-label>Compare To (Week)</mat-label>
          <mat-select [(value)]="dateRangeEnd" (selectionChange)="updateDateRangeFilter()">
            @for (period of availablePeriods; track period.WeekEnding) {
              <mat-option [value]="period.WeekEnding">
                {{ period.WeekLabel }} ({{ rspPlatformsWeeklyService.formatWeekEnding(period.WeekEnding) }})
              </mat-option>
            }
          </mat-select>
        </mat-form-field>

        @if (isDateRangeActive) {
          <div class="filter-status-compact">
            <mat-icon color="primary">info</mat-icon>
            <span>{{ rspPlatformsWeeklyService.formatWeekEnding(dateRangeStart) }} 
                  to {{ rspPlatformsWeeklyService.formatWeekEnding(dateRangeEnd) }}</span>
          </div>
        }
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Export Controls -->
  <div class="export-controls">
    <button mat-raised-button color="accent" (click)="exportToPDF()">
      <mat-icon>picture_as_pdf</mat-icon>
      Export PDF
    </button>
    <button mat-raised-button color="accent" (click)="exportToPowerPoint()">
      <mat-icon>slideshow</mat-icon>
      Export PowerPoint
    </button>
  </div>

  <!-- Weekly Report View Section (Standalone) -->
  <mat-card class="weekly-report-standalone">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>description</mat-icon>
        RSP Platforms Weekly Report
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      @if (isLoadingWeeklyData || isLoadingHistory) {
        <div class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading weekly report...</p>
        </div>
      } @else {
        <div class="weekly-report-container">
          <!-- Report Header -->
          <div class="report-header">
            <h2>RSP Platforms Weekly Report</h2>
            <p><strong>Week Ending:</strong> {{ selectedWeek ? rspPlatformsWeeklyService.formatWeekEnding(selectedWeek) : 'Select a week' }}</p>
          </div>

          <!-- Platform Performance Table -->
          <div class="platform-performance-table">
            <h3>Platform Performance Summary</h3>
            <table class="performance-table">
              <thead>
                <tr>
                  <th rowspan="2">Platform/Service Type</th>
                  <th colspan="3">Current Week</th>
                  <th colspan="3">Last Week</th>
                  <th colspan="3">Last Month</th>
                  <th rowspan="2">Change</th>
                </tr>
                <tr>
                  <th>Target</th>
                  <th>Actual</th>
                  <th>RAG</th>
                  <th>Target</th>
                  <th>Actual</th>
                  <th>RAG</th>
                  <th>Target</th>
                  <th>Actual</th>
                  <th>RAG</th>
                </tr>
              </thead>
              <tbody>
                @for (platform of getWeeklyReportData(); track platform.PlatformServiceType) {
                  <tr>
                    <td class="platform-name">{{ platform.PlatformServiceType }}</td>
                    <td class="target-cell">{{ formatPercentage(platform.CurrentWeekTarget) }}</td>
                    <td class="actual-cell">{{ formatPercentage(platform.CurrentWeekActual) }}</td>
                    <td class="rag-cell">
                      <span [class]="getRAGClass(platform.CurrentWeekActual, platform.CurrentWeekTarget)">
                        {{ getRAGStatus(platform.CurrentWeekActual, platform.CurrentWeekTarget) }}
                      </span>
                    </td>
                    <td class="target-cell">{{ formatPercentage(platform.LastWeekTarget) }}</td>
                    <td class="actual-cell">{{ formatPercentage(platform.LastWeekActual) }}</td>
                    <td class="rag-cell">
                      <span [class]="getRAGClass(platform.LastWeekActual, platform.LastWeekTarget)">
                        {{ getRAGStatus(platform.LastWeekActual, platform.LastWeekTarget) }}
                      </span>
                    </td>
                    <td class="target-cell">{{ formatPercentage(platform.LastMonthTarget) }}</td>
                    <td class="actual-cell">{{ formatPercentage(platform.LastMonthActual) }}</td>
                    <td class="rag-cell">
                      <span [class]="getRAGClass(platform.LastMonthActual, platform.LastMonthTarget)">
                        {{ getRAGStatus(platform.LastMonthActual, platform.LastMonthTarget) }}
                      </span>
                    </td>
                    <td class="change-cell" [class]="getChangeClass(platform.ChangeFromLastWeek)">
                      {{ formatChange(platform.ChangeFromLastWeek) }}
                    </td>
                  </tr>
                }
              </tbody>
            </table>
          </div>

          <!-- Incidents Section -->
          <div class="incidents-section">
            <h3>New Incidents</h3>
            <table class="incidents-table">
              <thead>
                <tr>
                  <th>Incident Number</th>
                  <th>Priority</th>
                  <th>Impact Start Date</th>
                  <th>Duration</th>
                  <th>Affected Service</th>
                  <th>Customer Impact</th>
                </tr>
              </thead>
              <tbody>
                @for (incident of getIncidentData(); track incident.id) {
                  <tr>
                    <td>{{ incident.number }}</td>
                    <td class="priority-cell" [class]="'priority-' + incident.priority.toLowerCase()">
                      {{ incident.priority }}
                    </td>
                    <td>{{ incident.startDate }}</td>
                    <td>{{ incident.duration }}</td>
                    <td>{{ incident.affectedService }}</td>
                    <td>{{ incident.customerImpact }}</td>
                  </tr>
                } @empty {
                  <tr>
                    <td colspan="6" class="no-incidents">No incidents with open problem tickets and/or no preventative measure in place</td>
                  </tr>
                }
              </tbody>
            </table>
          </div>

          <!-- Previous Incidents Section -->
          <div class="previous-incidents-section">
            <h3>Previous Incidents</h3>
            <table class="previous-incidents-table">
              <thead>
                <tr>
                  <th>Incident Number</th>
                  <th>Priority</th>
                  <th>Impact Start Date</th>
                  <th>RSP Impact Duration</th>
                  <th>Affected Service</th>
                </tr>
              </thead>
              <tbody>
                @for (incident of getPreviousIncidentData(); track incident.id) {
                  <tr>
                    <td>{{ incident.number }}</td>
                    <td class="priority-cell" [class]="'priority-' + incident.priority.toLowerCase()">
                      {{ incident.priority }}
                    </td>
                    <td>{{ incident.startDate }}</td>
                    <td>{{ incident.duration }}</td>
                    <td>{{ incident.affectedService }}</td>
                  </tr>
                } @empty {
                  <tr>
                    <td colspan="5" class="no-incidents">Historical incidents with open problem tickets and/or no preventative measure in place</td>
                  </tr>
                }
              </tbody>
            </table>
          </div>
        </div>
      }
    </mat-card-content>
  </mat-card>

  <!-- Main Content Tabs (Updated Structure) -->
  <mat-tab-group [(selectedIndex)]="selectedTabIndex" class="main-tabs">
    
    <!-- Chart Tab -->
    <mat-tab label="Chart">
      <div class="tab-content">
        
        <!-- Chart Controls -->
        <mat-card class="chart-controls-card">
          <mat-card-content>
            <div class="chart-controls-container">
              <div class="chart-controls-row">
                <!-- Week Selection -->
                <mat-form-field appearance="outline" class="control-field">
                  <mat-label>Select Week</mat-label>
                  <mat-select [(value)]="selectedWeek" (selectionChange)="onWeekSelectionChange()">
                    @for (week of availableWeeks; track week) {
                      <mat-option [value]="week">
                        {{ rspPlatformsWeeklyService.formatWeekEnding(week) }}
                      </mat-option>
                    }
                  </mat-select>
                </mat-form-field>

                <!-- Chart Options -->
                <mat-slide-toggle [(ngModel)]="showTrendlines" class="chart-toggle">
                  Show Trendlines
                </mat-slide-toggle>

                <mat-slide-toggle [(ngModel)]="showProjections" class="chart-toggle">
                  Show Projections
                </mat-slide-toggle>

                @if (showProjections) {
                  <mat-form-field appearance="outline" class="projection-field">
                    <mat-label>Projection Weeks</mat-label>
                    <mat-select [(value)]="projectionWeeks">
                      <mat-option [value]="2">2 Weeks</mat-option>
                      <mat-option [value]="4">4 Weeks</mat-option>
                      <mat-option [value]="6">6 Weeks</mat-option>
                      <mat-option [value]="8">8 Weeks</mat-option>
                    </mat-select>
                  </mat-form-field>
                }

                <mat-slide-toggle [(ngModel)]="showHoltWinters" class="chart-toggle">
                  Holt-Winters Forecast
                </mat-slide-toggle>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <!-- Charts -->
        <div class="charts-container">
          <!-- Platforms History Trend Chart -->
          <mat-card class="chart-card">
            <mat-card-header>
              <mat-card-title>Platform Performance Trends</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              @if (isLoadingHistory) {
                <div class="loading-container">
                  <mat-spinner diameter="50"></mat-spinner>
                  <p>Loading platform trends...</p>
                </div>
              } @else {
                <div class="chart-container">
                  <ag-charts [options]="platformsHistoryOptions"></ag-charts>
                </div>
              }
            </mat-card-content>
          </mat-card>

          <!-- Weekly Platform Availability Chart -->
          <mat-card class="chart-card">
            <mat-card-header>
              <mat-card-title>Weekly Platform Availability</mat-card-title>
            </mat-card-header>
            <mat-card-content>
              @if (isLoadingWeeklyData) {
                <div class="loading-container">
                  <mat-spinner diameter="50"></mat-spinner>
                  <p>Loading weekly data...</p>
                </div>
              } @else {
                <div class="chart-container">
                  <ag-charts [options]="platformsAvailabilityOptions"></ag-charts>
                </div>
              }
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Key Insights Tab -->
    <mat-tab label="Key Insights">
      <div class="tab-content">
        <mat-card class="insights-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>insights</mat-icon>
              Platform Performance Summary
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            @if (isLoadingSummary) {
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Loading summary...</p>
              </div>
            } @else if (platformsSummary) {
              <div class="summary-grid">
                <div class="summary-item">
                  <mat-icon color="primary">dashboard</mat-icon>
                  <div class="summary-content">
                    <h3>{{ platformsSummary.totalPlatforms }}</h3>
                    <p>Total Platforms</p>
                  </div>
                </div>

                <div class="summary-item">
                  <mat-icon [style.color]="getStatusColor(platformsSummary.averageAvailability, 99)">
                    {{ getStatusIcon(platformsSummary.averageAvailability, 99) }}
                  </mat-icon>
                  <div class="summary-content">
                    <h3>{{ formatPercentage(platformsSummary.averageAvailability) }}</h3>
                    <p>Average Availability</p>
                  </div>
                </div>

                <div class="summary-item">
                  <mat-icon color="warn">warning</mat-icon>
                  <div class="summary-content">
                    <h3>{{ platformsSummary.totalIncidents }}</h3>
                    <p>Total Incidents</p>
                  </div>
                </div>

                <div class="summary-item">
                  <mat-icon color="primary">check_circle</mat-icon>
                  <div class="summary-content">
                    <h3>{{ platformsSummary.platformsAboveTarget }}</h3>
                    <p>Above Target</p>
                  </div>
                </div>

                <div class="summary-item">
                  <mat-icon color="warn">error</mat-icon>
                  <div class="summary-content">
                    <h3>{{ platformsSummary.platformsBelowTarget }}</h3>
                    <p>Below Target</p>
                  </div>
                </div>
              </div>
            } @else {
              <p>No summary data available.</p>
            }
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Data Tab -->
    <mat-tab label="Data">
      <div class="tab-content">
        <mat-card class="data-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>table_view</mat-icon>
              Platform Performance Data
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            @if (isLoadingHistory) {
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Loading data...</p>
              </div>
            } @else {
              <div class="grid-container">
                <ag-grid-angular
                  class="ag-theme-material"
                  [gridOptions]="gridOptions"
                  [rowData]="isDateRangeActive ? filteredPlatformsHistoryData : platformsHistoryData"
                  style="height: 600px; width: 100%;">
                </ag-grid-angular>
              </div>
            }
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- GenAI Insights Tab -->
    <mat-tab label="GenAI Insights">
      <div class="tab-content">
        <mat-card class="insights-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>psychology</mat-icon>
              AI-Generated Insights
            </mat-card-title>
            <div class="card-actions">
              <button mat-raised-button color="primary" (click)="generateAIInsights()"
                      [disabled]="isGeneratingInsights">
                @if (isGeneratingInsights) {
                  <mat-spinner diameter="20"></mat-spinner>
                } @else {
                  <mat-icon>auto_awesome</mat-icon>
                }
                Generate Insights
              </button>
            </div>
          </mat-card-header>
          <mat-card-content>
            @if (isGeneratingInsights) {
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Generating AI insights...</p>
              </div>
            } @else if (generatedInsights) {
              <div class="insights-content" [innerHTML]="generatedInsights"></div>
            } @else {
              <p>Click "Generate Insights" to analyze platform performance data using AI.</p>
            }
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

    <!-- Statistical Insights Tab -->
    <mat-tab label="Statistical Insights">
      <div class="tab-content">
        <mat-card class="insights-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>analytics</mat-icon>
              Statistical Analysis
            </mat-card-title>
            <div class="card-actions">
              <button mat-raised-button color="primary" (click)="generateStatisticalAnalysis()"
                      [disabled]="isGeneratingStatisticalAnalysis">
                @if (isGeneratingStatisticalAnalysis) {
                  <mat-spinner diameter="20"></mat-spinner>
                } @else {
                  <mat-icon>calculate</mat-icon>
                }
                Generate Analysis
              </button>
            </div>
          </mat-card-header>
          <mat-card-content>
            @if (isGeneratingStatisticalAnalysis) {
              <div class="loading-container">
                <mat-spinner diameter="50"></mat-spinner>
                <p>Generating statistical analysis...</p>
              </div>
            } @else if (statisticalAnalysis) {
              <div class="insights-content" [innerHTML]="statisticalAnalysis"></div>
            } @else {
              <p>Click "Generate Analysis" to perform statistical analysis on platform performance data.</p>
            }
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>

  </mat-tab-group>
</mat-card>
