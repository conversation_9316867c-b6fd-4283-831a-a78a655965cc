import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { AgChartsModule } from 'ag-charts-angular';
import { AgGridAngular } from 'ag-grid-angular';

import { RSPPlatformsWeeklyService } from '../../services/rsp-platforms-weekly.service';
import {
  IRSPPlatformsWeekly,
  IRSPPlatformsWeeklyHistory,
  IRSPPlatformsWeeklyPeriod,
  IRSPPlatformsWeeklySummary
} from '../../models/models';

@Component({
  selector: 'app-rsp-platforms-weekly',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatTabsModule,
    MatIconModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    AgChartsModule,
    AgGridAngular
  ],
  templateUrl: './rsp-platforms-weekly.component.html',
  styleUrls: ['./rsp-platforms-weekly.component.css']
})
export class RSPPlatformsWeeklyComponent implements OnInit {

  constructor(public rspPlatformsWeeklyService: RSPPlatformsWeeklyService) {}

  // Data properties
  platformsHistoryData: any[] = [];
  platformsWeeklyData: any[] = [];
  availablePeriods: IRSPPlatformsWeeklyPeriod[] = [];
  platformsSummary: IRSPPlatformsWeeklySummary | null = null;

  // Loading states
  isLoadingHistory: boolean = false;
  isLoadingWeeklyData: boolean = false;
  isLoadingSummary: boolean = false;

  // Period comparison controls
  dateRangeStart: string = '';
  dateRangeEnd: string = '';
  isDateRangeActive: boolean = false;

  // Filtered data (when date range is active)
  filteredPlatformsHistoryData: any[] = [];
  filteredPlatformsWeeklyData: any[] = [];

  // Chart options
  platformsHistoryOptions: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  platformsAvailabilityOptions: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  // Chart controls
  selectedWeek1: string = '';
  selectedWeek2: string = '';
  showTrendlines: boolean = true;
  showProjections: boolean = true;
  projectionWeeks: number = 4;
  showConfidenceInterval: boolean = true;
  showHoltWinters: boolean = false;

  // Tab index
  selectedTabIndex: number = 0;

  // GenAI Insights
  generatedInsights: string = '';
  isGeneratingInsights: boolean = false;

  // Statistical Analysis
  statisticalAnalysis: string = '';
  isGeneratingStatisticalAnalysis: boolean = false;

  // Period filtering
  availableWeeks: string[] = [];
  selectedWeek: string = '';
  mainSelectedWeek: string = '';

  // AG Grid configuration
  gridOptions: any = {
    columnDefs: [
      { field: 'WeekEnding', headerName: 'Week Ending', sortable: true, filter: true, width: 120 },
      { field: 'PlatformServiceType', headerName: 'Platform/Service Type', sortable: true, filter: true, width: 200 },
      { 
        field: 'CurrentWeekTarget', 
        headerName: 'Target %', 
        sortable: true, 
        filter: true, 
        width: 100,
        valueFormatter: (params: any) => {
          const value = parseFloat(params.value);
          return !isNaN(value) ? `${value.toFixed(2)}%` : '0%';
        }
      },
      { 
        field: 'CurrentWeekActual', 
        headerName: 'Actual %', 
        sortable: true, 
        filter: true, 
        width: 100,
        valueFormatter: (params: any) => {
          const value = parseFloat(params.value);
          return !isNaN(value) ? `${value.toFixed(2)}%` : '0%';
        }
      },
      { 
        field: 'ChangeFromLastWeek', 
        headerName: 'Change', 
        sortable: true, 
        filter: true, 
        width: 100,
        valueFormatter: (params: any) => {
          const value = parseFloat(params.value);
          return !isNaN(value) ? `${value >= 0 ? '+' : ''}${value.toFixed(2)}%` : '0%';
        }
      },
      { field: 'IncidentCount', headerName: 'Incidents', sortable: true, filter: true, width: 100 },
      { 
        field: 'ServiceAvailability', 
        headerName: 'Service Availability %', 
        sortable: true, 
        filter: true, 
        width: 150,
        valueFormatter: (params: any) => {
          const value = parseFloat(params.value);
          return !isNaN(value) ? `${value.toFixed(2)}%` : '0%';
        }
      },
      { field: 'Notes', headerName: 'Notes', sortable: true, filter: true, width: 300 }
    ],
    defaultColDef: {
      resizable: true,
      sortable: true,
      filter: true
    },
    pagination: true,
    paginationPageSize: 25,
    paginationPageSizeSelector: [10, 25, 50],
    rowSelection: 'single'
  };

  ngOnInit(): void {
    this.loadInitialData();
  }

  // Load initial data
  loadInitialData(): void {
    this.loadAvailablePeriods();
    this.loadPlatformsHistoryData();
    this.loadPlatformsSummary();
  }

  // Load available periods
  loadAvailablePeriods(): void {
    this.rspPlatformsWeeklyService.getRSPPlatformsWeeklyPeriods().subscribe({
      next: (response) => {
        if (response && response.data) {
          this.availablePeriods = response.data;
          this.availableWeeks = this.availablePeriods.map(p => p.WeekEnding);
          
          // Set dynamic period defaults (oldest to newest)
          this.setDynamicPeriodDefaults();
        }
      },
      error: (error) => {
        console.error('Error loading available periods:', error);
      }
    });
  }

  // Set dynamic period defaults to full available range
  setDynamicPeriodDefaults(): void {
    if (this.availablePeriods && this.availablePeriods.length > 0) {
      const weekRange = this.rspPlatformsWeeklyService.getWeekRange(this.availablePeriods);
      this.dateRangeStart = weekRange.oldest;
      this.dateRangeEnd = weekRange.newest;
      
      // Set main selected week to the most recent
      this.mainSelectedWeek = weekRange.newest;
      this.selectedWeek = weekRange.newest;
      
      // Generate the weekly chart for the selected week
      this.generatePlatformsAvailabilityChart();
      
      console.log(`Set dynamic defaults: ${this.dateRangeStart} to ${this.dateRangeEnd}`);
    }
  }

  // Load platforms history data
  loadPlatformsHistoryData(): void {
    // Pass date range parameters if active
    const startWeek = this.isDateRangeActive ? this.dateRangeStart : undefined;
    const endWeek = this.isDateRangeActive ? this.dateRangeEnd : undefined;

    this.isLoadingHistory = true;
    this.rspPlatformsWeeklyService.getRSPPlatformsWeeklyHistory(startWeek, endWeek).subscribe({
      next: (response) => {
        if (response && response.data) {
          // Convert string values to numbers for chart compatibility
          this.platformsHistoryData = response.data.map((item: any) => ({
            ...item,
            CurrentWeekTarget: parseFloat(item.CurrentWeekTarget) || 0,
            CurrentWeekActual: parseFloat(item.CurrentWeekActual) || 0,
            LastWeekTarget: parseFloat(item.LastWeekTarget) || 0,
            LastWeekActual: parseFloat(item.LastWeekActual) || 0,
            ChangeFromLastWeek: parseFloat(item.ChangeFromLastWeek) || 0,
            IncidentCount: parseInt(item.IncidentCount) || 0,
            ServiceAvailability: parseFloat(item.ServiceAvailability) || 0
          }));

          // Apply date range filtering if active
          this.applyDateRangeFiltering();
          this.generatePlatformsHistoryChart();
        }
        this.isLoadingHistory = false;
      },
      error: (error) => {
        console.error('Error loading platforms history data:', error);
        this.isLoadingHistory = false;
      }
    });
  }

  // Load platforms summary
  loadPlatformsSummary(): void {
    const weekEnding = this.mainSelectedWeek || this.selectedWeek;

    this.isLoadingSummary = true;
    this.rspPlatformsWeeklyService.getRSPPlatformsWeeklySummary(weekEnding).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.platformsSummary = response.data;
        }
        this.isLoadingSummary = false;
      },
      error: (error) => {
        console.error('Error loading platforms summary:', error);
        this.isLoadingSummary = false;
      }
    });
  }

  // Apply date range filtering
  applyDateRangeFiltering(): void {
    if (this.isDateRangeActive && this.dateRangeStart && this.dateRangeEnd) {
      const startDate = new Date(this.dateRangeStart);
      const endDate = new Date(this.dateRangeEnd);

      this.filteredPlatformsHistoryData = this.platformsHistoryData.filter(item => {
        const itemDate = new Date(item.WeekEnding);
        return itemDate >= startDate && itemDate <= endDate;
      });

      this.filteredPlatformsWeeklyData = this.platformsWeeklyData.filter(item => {
        const itemDate = new Date(item.WeekEnding);
        return itemDate >= startDate && itemDate <= endDate;
      });
    } else {
      this.filteredPlatformsHistoryData = [...this.platformsHistoryData];
      this.filteredPlatformsWeeklyData = [...this.platformsWeeklyData];
    }
  }

  // Update date range filter
  updateDateRangeFilter(): void {
    this.isDateRangeActive = !!(this.dateRangeStart && this.dateRangeEnd);
    this.applyDateRangeFiltering();
    this.generatePlatformsHistoryChart();
    this.generatePlatformsAvailabilityChart();

    // Reload data with new date range
    this.loadPlatformsHistoryData();
  }

  // Clear date range filter
  clearDateRangeFilter(): void {
    this.dateRangeStart = '';
    this.dateRangeEnd = '';
    this.isDateRangeActive = false;
    this.applyDateRangeFiltering();
    this.generatePlatformsHistoryChart();
    this.generatePlatformsAvailabilityChart();

    // Reload data without date range
    this.loadPlatformsHistoryData();
  }

  // Generate platforms history chart
  generatePlatformsHistoryChart(): void {
    const dataToUse = this.isDateRangeActive ? this.filteredPlatformsHistoryData : this.platformsHistoryData;

    if (!dataToUse || dataToUse.length === 0) {
      return;
    }

    // Group data by week for trending
    const weeklyData = this.groupDataByWeek(dataToUse);

    // Sort by date to ensure proper chronological order
    weeklyData.sort((a, b) => new Date(a.WeekEnding).getTime() - new Date(b.WeekEnding).getTime());

    // Format dates for better display
    const formattedData = weeklyData.map(item => ({
      ...item,
      WeekEndingFormatted: this.rspPlatformsWeeklyService.formatWeekEnding(item.WeekEnding)
    }));

    this.platformsHistoryOptions = {
      title: {
        text: 'RSP Platforms Weekly Performance Trends',
        fontSize: 16,
        fontWeight: 'bold'
      },
      data: formattedData,
      series: [
        {
          type: 'line',
          xKey: 'WeekEndingFormatted',
          yKey: 'AverageAvailability',
          yName: 'Average Availability %',
          stroke: '#0066cc',
          strokeWidth: 3,
          marker: {
            enabled: true,
            size: 8,
            fill: '#0066cc'
          }
        },
        {
          type: 'line',
          xKey: 'WeekEndingFormatted',
          yKey: 'AverageTarget',
          yName: 'Average Target %',
          stroke: '#ff6600',
          strokeWidth: 2,
          lineDash: [5, 5],
          marker: {
            enabled: true,
            size: 6,
            fill: '#ff6600'
          }
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: { text: 'Week Ending' },
          label: {
            rotation: 45,
            fontSize: 11
          }
        },
        {
          type: 'number',
          position: 'left',
          title: { text: 'Availability %' },
          min: Math.max(95, Math.min(...formattedData.map(d => d.AverageAvailability)) - 1),
          max: Math.min(100, Math.max(...formattedData.map(d => d.AverageAvailability)) + 0.5),
          label: {
            formatter: (params: any) => `${params.value.toFixed(1)}%`
          }
        }
      ],
      legend: {
        position: 'bottom'
      },
      tooltip: {
        enabled: true
      }
    };
  }

  // Generate platforms availability chart
  generatePlatformsAvailabilityChart(): void {
    const weekEnding = this.mainSelectedWeek || this.selectedWeek;
    if (!weekEnding) return;

    this.isLoadingWeeklyData = true;
    this.rspPlatformsWeeklyService.getRSPPlatformsWeeklyByWeek(weekEnding).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.platformsWeeklyData = response.data.map((item: any) => ({
            ...item,
            CurrentWeekTarget: parseFloat(item.CurrentWeekTarget) || 0,
            CurrentWeekActual: parseFloat(item.CurrentWeekActual) || 0,
            ServiceAvailability: parseFloat(item.ServiceAvailability) || 0
          }));

          const chartData = this.platformsWeeklyData.map(item => ({
            PlatformServiceType: item.PlatformServiceType,
            CurrentWeekActual: item.CurrentWeekActual,
            CurrentWeekTarget: item.CurrentWeekTarget,
            ServiceAvailability: item.ServiceAvailability
          }));

          // Calculate dynamic Y-axis range
          const allValues = chartData.flatMap(item => [item.CurrentWeekActual, item.CurrentWeekTarget]);
          const minValue = Math.min(...allValues);
          const maxValue = Math.max(...allValues);

          this.platformsAvailabilityOptions = {
            title: {
              text: `Platform Availability - Week Ending ${this.rspPlatformsWeeklyService.formatWeekEnding(weekEnding)}`,
              fontSize: 16,
              fontWeight: 'bold'
            },
            data: chartData,
            series: [
              {
                type: 'column',
                xKey: 'PlatformServiceType',
                yKey: 'CurrentWeekActual',
                yName: 'Actual %',
                fill: '#0066cc',
                strokeWidth: 1,
                stroke: '#004499'
              },
              {
                type: 'column',
                xKey: 'PlatformServiceType',
                yKey: 'CurrentWeekTarget',
                yName: 'Target %',
                fill: '#ff6600',
                strokeWidth: 1,
                stroke: '#cc5200'
              }
            ],
            axes: [
              {
                type: 'category',
                position: 'bottom',
                title: { text: 'Platform/Service Type' },
                label: {
                  rotation: 45,
                  fontSize: 10
                }
              },
              {
                type: 'number',
                position: 'left',
                title: { text: 'Availability %' },
                min: Math.max(95, minValue - 1),
                max: Math.min(100, maxValue + 0.5),
                label: {
                  formatter: (params: any) => `${params.value.toFixed(1)}%`
                }
              }
            ],
            legend: {
              position: 'bottom'
            },
            tooltip: {
              enabled: true
            }
          };
        }
        this.isLoadingWeeklyData = false;
      },
      error: (error) => {
        console.error('Error loading weekly platforms data:', error);
        this.isLoadingWeeklyData = false;
      }
    });
  }

  // Group data by week for trending
  groupDataByWeek(data: any[]): any[] {
    const weeklyGroups: { [key: string]: any[] } = {};

    data.forEach(item => {
      const week = item.WeekEnding;
      if (!weeklyGroups[week]) {
        weeklyGroups[week] = [];
      }
      weeklyGroups[week].push(item);
    });

    return Object.keys(weeklyGroups).map(week => {
      const weekData = weeklyGroups[week];
      const avgAvailability = weekData.reduce((sum, item) => sum + item.CurrentWeekActual, 0) / weekData.length;
      const avgTarget = weekData.reduce((sum, item) => sum + item.CurrentWeekTarget, 0) / weekData.length;

      return {
        WeekEnding: week,
        AverageAvailability: avgAvailability,
        AverageTarget: avgTarget,
        TotalIncidents: weekData.reduce((sum, item) => sum + item.IncidentCount, 0)
      };
    }).sort((a, b) => new Date(a.WeekEnding).getTime() - new Date(b.WeekEnding).getTime());
  }

  // Handle week selection change
  onWeekSelectionChange(): void {
    if (this.selectedWeek) {
      this.mainSelectedWeek = this.selectedWeek;
      this.generatePlatformsAvailabilityChart();
      this.loadPlatformsSummary();
    }
  }

  // Get weekly report data for the detailed table view
  getWeeklyReportData(): any[] {
    if (!this.platformsWeeklyData || this.platformsWeeklyData.length === 0) {
      return [];
    }
    return this.platformsWeeklyData;
  }

  // Get incident data (mock data for now - can be replaced with real API call)
  getIncidentData(): any[] {
    return [
      {
        id: 'INC0306541',
        number: 'INC0306541',
        priority: 'P2',
        startDate: '24/06/2025',
        duration: '1.5hrs 0mins',
        affectedService: 'Service Portal',
        customerImpact: 'RSPs were unable to search historical orders and trouble tickets via Service Portal Orders and Trouble Tickets tab. Inconsistent data in HVC, CVC, NNI reports downloaded from Service Portal'
      }
    ];
  }

  // Get previous incident data (mock data for now)
  getPreviousIncidentData(): any[] {
    return [];
  }

  // Format percentage values
  formatPercentage(value: any): string {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    const numValue = parseFloat(value);
    return isNaN(numValue) ? '-' : `${numValue.toFixed(2)}%`;
  }

  // Format change values
  formatChange(value: any): string {
    if (value === null || value === undefined || value === '') {
      return '-';
    }
    const numValue = parseFloat(value);
    if (isNaN(numValue)) return '-';
    return `${numValue >= 0 ? '+' : ''}${numValue.toFixed(2)}%`;
  }

  // Get RAG status based on actual vs target
  getRAGStatus(actual: any, target: any): string {
    if (actual === null || actual === undefined || target === null || target === undefined) {
      return '-';
    }
    const actualNum = parseFloat(actual);
    const targetNum = parseFloat(target);

    if (isNaN(actualNum) || isNaN(targetNum)) return '-';

    if (actualNum >= targetNum) {
      return 'Green';
    } else if (actualNum >= targetNum - 0.5) {
      return 'Amber';
    } else {
      return 'Red';
    }
  }

  // Get RAG CSS class
  getRAGClass(actual: any, target: any): string {
    const status = this.getRAGStatus(actual, target);
    return `rag-${status.toLowerCase()}`;
  }

  // Get change CSS class
  getChangeClass(change: any): string {
    if (change === null || change === undefined || change === '') {
      return '';
    }
    const numValue = parseFloat(change);
    if (isNaN(numValue)) return '';

    if (numValue > 0) return 'change-positive';
    if (numValue < 0) return 'change-negative';
    return 'change-neutral';
  }

  // Get status color for summary items
  getStatusColor(value: number, threshold: number): string {
    if (value >= threshold) return '#4caf50'; // Green
    if (value >= threshold - 0.5) return '#ff9800'; // Orange
    return '#f44336'; // Red
  }

  // Get status icon for summary items
  getStatusIcon(value: number, threshold: number): string {
    if (value >= threshold) return 'check_circle';
    if (value >= threshold - 0.5) return 'warning';
    return 'error';
  }

  // Generate AI insights
  generateAIInsights(): void {
    const dataToUse = this.isDateRangeActive ? this.filteredPlatformsHistoryData : this.platformsHistoryData;

    if (!dataToUse || dataToUse.length === 0) {
      this.generatedInsights = 'No data available for AI insights generation.';
      return;
    }

    this.isGeneratingInsights = true;
    this.rspPlatformsWeeklyService.generateAIInsights(dataToUse).subscribe({
      next: (response) => {
        if (response && response.insights) {
          this.generatedInsights = response.insights;
        } else {
          this.generatedInsights = 'Unable to generate insights at this time.';
        }
        this.isGeneratingInsights = false;
      },
      error: (error) => {
        console.error('Error generating AI insights:', error);
        this.generatedInsights = 'Error generating insights. Please try again later.';
        this.isGeneratingInsights = false;
      }
    });
  }

  // Generate statistical analysis
  generateStatisticalAnalysis(): void {
    const dataToUse = this.isDateRangeActive ? this.filteredPlatformsHistoryData : this.platformsHistoryData;

    if (!dataToUse || dataToUse.length === 0) {
      this.statisticalAnalysis = 'No data available for statistical analysis.';
      return;
    }

    this.isGeneratingStatisticalAnalysis = true;
    this.rspPlatformsWeeklyService.generateStatisticalAnalysis(dataToUse).subscribe({
      next: (response) => {
        if (response && response.analysis) {
          this.statisticalAnalysis = response.analysis;
        } else {
          this.statisticalAnalysis = 'Unable to generate statistical analysis at this time.';
        }
        this.isGeneratingStatisticalAnalysis = false;
      },
      error: (error) => {
        console.error('Error generating statistical analysis:', error);
        this.statisticalAnalysis = 'Error generating statistical analysis. Please try again later.';
        this.isGeneratingStatisticalAnalysis = false;
      }
    });
  }

  // Export to PDF
  exportToPDF(): void {
    const startWeek = this.isDateRangeActive ? this.dateRangeStart : undefined;
    const endWeek = this.isDateRangeActive ? this.dateRangeEnd : undefined;
    const weekEnding = this.mainSelectedWeek || this.selectedWeek;

    this.rspPlatformsWeeklyService.exportToPDF(weekEnding, startWeek, endWeek).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `RSP_Platforms_Weekly_Report_${weekEnding || 'All'}_${new Date().toISOString().split('T')[0]}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error exporting to PDF:', error);
      }
    });
  }

  // Export to PowerPoint
  exportToPowerPoint(): void {
    const startWeek = this.isDateRangeActive ? this.dateRangeStart : undefined;
    const endWeek = this.isDateRangeActive ? this.dateRangeEnd : undefined;
    const weekEnding = this.mainSelectedWeek || this.selectedWeek;

    this.rspPlatformsWeeklyService.exportToPowerPoint(weekEnding, startWeek, endWeek).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `RSP_Platforms_Weekly_Report_${weekEnding || 'All'}_${new Date().toISOString().split('T')[0]}.pptx`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error exporting to PowerPoint:', error);
      }
    });
  }
}
