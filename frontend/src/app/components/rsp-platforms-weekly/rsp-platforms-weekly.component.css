/* Main card styling */
.example-card {
  margin: 20px;
  padding: 20px;
}

.example-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.5em;
  font-weight: 600;
  margin-bottom: 20px;
}

/* Period comparison controls */
.period-comparison-card {
  margin-bottom: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.period-comparison-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1em;
  color: #495057;
}

.period-controls-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.period-controls-row {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.period-field {
  min-width: 200px;
  flex: 1;
}

.period-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px;
  background-color: #e3f2fd;
  border-radius: 4px;
  color: #1976d2;
  font-size: 0.9em;
}

/* Export controls */
.export-controls {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-bottom: 8px;
}

.export-controls button {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Tab styling */
.main-tabs {
  margin-top: 8px;
}

.tab-content {
  padding: 20px 0;
}

/* Chart controls */
.chart-controls-card {
  margin-bottom: 20px;
  background-color: #f8f9fa;
}

.chart-controls-container {
  padding: 10px 0;
}

.chart-controls-row {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-field {
  min-width: 150px;
}

.projection-field {
  min-width: 120px;
}

.chart-toggle {
  margin: 0 10px;
}

/* Charts container */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-card mat-card-header {
  margin-bottom: 15px;
}

.chart-card mat-card-title {
  font-size: 1.2em;
  font-weight: 500;
  color: #333;
}

.chart-container {
  height: 400px;
  width: 100%;
  margin: 10px 0;
}

/* Loading states */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 15px;
}

.loading-container p {
  color: #666;
  font-size: 0.9em;
}

/* Insights styling */
.insights-card {
  margin-bottom: 20px;
}

.insights-card mat-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.insights-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2em;
  font-weight: 500;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.card-actions button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.insights-content {
  line-height: 1.6;
  color: #333;
}

.insights-content h2 {
  color: #1976d2;
  font-size: 1.3em;
  margin-top: 20px;
  margin-bottom: 10px;
}

.insights-content h3 {
  color: #333;
  font-size: 1.1em;
  margin-top: 15px;
  margin-bottom: 8px;
}

.insights-content ul {
  margin-left: 20px;
}

.insights-content li {
  margin-bottom: 5px;
}

/* Summary grid */
.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-item mat-icon {
  font-size: 2em;
  width: 2em;
  height: 2em;
}

.summary-content h3 {
  margin: 0;
  font-size: 1.8em;
  font-weight: 600;
  color: #333;
}

.summary-content p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 0.9em;
}

/* Data grid styling */
.data-card {
  margin-bottom: 20px;
}

.data-card mat-card-header {
  margin-bottom: 15px;
}

.data-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2em;
  font-weight: 500;
}

.grid-container {
  margin-top: 15px;
}

/* AG Grid theme customization */
.ag-theme-material {
  --ag-header-background-color: #f5f5f5;
  --ag-header-foreground-color: #333;
  --ag-border-color: #ddd;
  --ag-row-hover-color: #f0f8ff;
}

/* Responsive design */
@media (max-width: 768px) {
  .example-card {
    margin: 10px;
    padding: 15px;
  }

  .period-controls-row {
    flex-direction: column;
    align-items: stretch;
  }

  .period-field {
    min-width: unset;
  }

  .chart-controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .control-field,
  .projection-field {
    min-width: unset;
  }

  .chart-toggle {
    margin: 0;
  }

  .export-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 300px;
  }
}

@media (max-width: 480px) {
  .example-card {
    margin: 5px;
    padding: 10px;
  }

  .example-card mat-card-title {
    font-size: 1.3em;
  }

  .chart-container {
    height: 250px;
  }

  .summary-item {
    padding: 15px;
  }

  .summary-content h3 {
    font-size: 1.5em;
  }
}

/* Animation for loading states */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.insights-content,
.summary-grid,
.chart-container {
  animation: fadeIn 0.3s ease-in;
}

/* Status colors */
.status-good {
  color: #4caf50;
}

.status-warning {
  color: #ff9800;
}

.status-error {
  color: #f44336;
}

/* Button styling */
button mat-spinner {
  margin-right: 8px;
}

/* Card spacing */
.chart-card,
.insights-card,
.data-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border-radius: 8px;
}

/* Ensure proper spacing in tab content */
.tab-content > *:last-child {
  margin-bottom: 0;
}

/* Weekly Report View Styles */
.report-view-card {
  margin-bottom: 20px;
}

.weekly-report-container {
  padding: 12px;
}

.report-header {
  margin-bottom: 16px;
  text-align: center;
}

.report-header h2 {
  color: #1976d2;
  margin-bottom: 10px;
}

.platform-performance-table,
.incidents-section,
.previous-incidents-section {
  margin-bottom: 40px;
}

.platform-performance-table h3,
.incidents-section h3,
.previous-incidents-section h3 {
  color: #1976d2;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 600;
}

.performance-table,
.incidents-table,
.previous-incidents-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 12px;
}

.performance-table th,
.performance-table td,
.incidents-table th,
.incidents-table td,
.previous-incidents-table th,
.previous-incidents-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.performance-table th,
.incidents-table th,
.previous-incidents-table th {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #333;
}

.platform-name {
  text-align: left !important;
  font-weight: 500;
  background-color: #f9f9f9;
}

.target-cell,
.actual-cell {
  font-weight: 500;
}

.rag-cell {
  font-weight: 600;
}

.rag-green {
  color: #4caf50;
  background-color: #e8f5e8;
}

.rag-amber {
  color: #ff9800;
  background-color: #fff3e0;
}

.rag-red {
  color: #f44336;
  background-color: #ffebee;
}

.change-cell {
  font-weight: 600;
}

.change-positive {
  color: #4caf50;
}

.change-negative {
  color: #f44336;
}

.change-neutral {
  color: #666;
}

.priority-cell {
  font-weight: 600;
}

.priority-p1 {
  color: #f44336;
  background-color: #ffebee;
}

.priority-p2 {
  color: #ff9800;
  background-color: #fff3e0;
}

.priority-p3 {
  color: #2196f3;
  background-color: #e3f2fd;
}

.no-incidents {
  text-align: center;
  font-style: italic;
  color: #666;
  background-color: #f9f9f9;
}

/* Compact Period Comparison Controls */
.period-comparison-card-compact {
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
}

.period-controls-compact {
  padding: 12px 16px !important;
}

.period-controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.period-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1976d2;
}

.period-title mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.period-actions-compact {
  display: flex;
  gap: 8px;
}

.compact-button {
  min-width: auto;
  padding: 0 12px;
  height: 32px;
  font-size: 13px;
}

.compact-button mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.period-fields-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.period-field-compact {
  min-width: 180px;
  flex: 1;
}

.period-field-compact .mat-mdc-form-field-wrapper {
  padding-bottom: 0;
}

.filter-status-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background-color: #e3f2fd;
  border-radius: 4px;
  color: #1976d2;
  font-size: 12px;
  white-space: nowrap;
}

.filter-status-compact mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* Weekly Report Standalone Section */
.weekly-report-standalone {
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.weekly-report-standalone .mat-mdc-card-header {
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.weekly-report-standalone .mat-mdc-card-title {
  color: #1976d2;
  font-weight: 600;
}
