import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatTabsModule } from '@angular/material/tabs';
import { AgCharts } from 'ag-charts-angular';

@Component({
  selector: 'app-cio-rsp-executive-website-users',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatTabsModule,
    AgCharts
  ],
  templateUrl: './cio-rsp-executive-website-users.component.html',
  styleUrl: './cio-rsp-executive-website-users.component.scss'
})
export class CioRspExecutiveWebsiteUsersComponent implements OnInit {
  // Chart options - Initialize with empty defaults to prevent undefined errors
  options: any = {
    data: [],
    series: [],
    axes: [
      { type: 'category', position: 'bottom' },
      { type: 'number', position: 'left' }
    ]
  };

  // Selected tab index
  selectedTabIndex: number = 0;

  constructor() { }

  ngOnInit(): void {
    this.generateChart();
  }

  // Generate the chart
  generateChart(): void {
    console.log('Generating website users chart');

    // Create placeholder data
    const placeholderData = [
      { Metric: 'Total Users', Value: 0 },
      { Metric: 'Unique Users', Value: 0 },
      { Metric: 'Page Views', Value: 0 }
    ];

    // Set chart options with simplified configuration
    this.options = {
      title: {
        text: 'Public Website Users - Data Coming Soon',
        fontSize: 18,
      },
      data: placeholderData,
      series: [
        {
          type: 'bar',
          xKey: 'Metric',
          yKey: 'Value',
          fill: '#0066cc'
        }
      ],
      axes: [
        {
          type: 'category',
          position: 'bottom',
          title: {
            text: 'Metric',
          },
        },
        {
          type: 'number',
          position: 'left',
          title: {
            text: 'Count',
          },
        },
      ]
    };

    console.log('Website users chart options set:', this.options);
  }
}
