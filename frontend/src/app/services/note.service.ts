import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { INote } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class NoteService implements IStandardEntityService<INote> {
  
    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Note records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/notes/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Note record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/notes/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Note record.
     */
    createRecord(payload: INote): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/notes");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Note record.
     */
    updateRecord(recordId: string, payload: INote): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/notes/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getNoteRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getNoteRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createNoteRecord(payload: INote): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateNoteRecord(recordId: string, payload: INote): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
