import { Injectable } from '@angular/core';

export interface TemplateContext {
  period?: string;
  date?: string;
  title?: string;
  company?: string;
  [key: string]: any;
}

export interface TemplateImageData {
  dataURL: string;
  width: number;
  height: number;
}

export interface TextOverlayConfig {
  period?: {
    x: number;
    y: number;
    w: number;
    h: number;
    fontSize?: number;
    color?: string;
    align?: string;
  };
  date?: {
    x: number;
    y: number;
    w: number;
    h: number;
    fontSize?: number;
    color?: string;
    align?: string;
  };
  title?: {
    x: number;
    y: number;
    w: number;
    h: number;
    fontSize?: number;
    color?: string;
    align?: string;
  };
  company?: {
    x: number;
    y: number;
    w: number;
    h: number;
    fontSize?: number;
    color?: string;
    align?: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class PresentationTemplateService {

  private readonly templateBasePath = '/assets/data/presentation-templates';

  constructor() { }

  /**
   * Load template image (PNG/JPG) for slide insertion
   */
  async loadTemplateImage(templateType: 'intro' | 'end'): Promise<TemplateImageData | null> {
    try {
      // Try PNG first, then JPG
      const extensions = ['png', 'jpg', 'jpeg'];

      for (const ext of extensions) {
        const templatePath = `${this.templateBasePath}/images/${templateType}.${ext}`;

        try {
          const response = await fetch(templatePath);
          if (response.ok) {
            const blob = await response.blob();
            const dataURL = await this.blobToDataURL(blob);

            // Get image dimensions
            const dimensions = await this.getImageDimensions(dataURL);

            console.log(`📄 Loaded template image: ${templatePath}`);
            return {
              dataURL,
              width: dimensions.width,
              height: dimensions.height
            };
          }
        } catch (error) {
          // Continue to next extension
          continue;
        }
      }

      console.warn(`📄 No template image found for: ${templateType}`);
      return null;
    } catch (error) {
      console.warn(`Failed to load template image: ${templateType}`, error);
      return null;
    }
  }

  /**
   * Convert blob to data URL
   */
  private blobToDataURL(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  /**
   * Get image dimensions from data URL
   */
  private getImageDimensions(dataURL: string): Promise<{width: number, height: number}> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };
      img.onerror = reject;
      img.src = dataURL;
    });
  }

  /**
   * Get text overlay configuration for template type
   */
  getTextOverlayConfig(templateType: 'intro' | 'end'): TextOverlayConfig {
    if (templateType === 'intro') {
      return {
        title: {
          x: 0.3,           // 0.76cm from left (same positioning as end slide)
          y: 2.0,           // Higher positioning (same as end slide)
          w: 8,             // Width for text
          h: 1.2,           // Reduced height for smaller font
          fontSize: 36,     // 36pt font size (reduced from 70pt)
          color: '000000',  // Black color (same as end slide)
          align: 'left'     // Left-aligned (same as end slide)
        },
        period: {
          x: 1.5,
          y: 4,
          w: 7,
          h: 0.6,
          fontSize: 18,
          color: '333333',
          align: 'center'
        },
        date: {
          x: 1.5,           // Center positioning
          y: 5.2,           // Match end slide position (was 4.8)
          w: 7,             // Center width
          h: 0.5,           // Standard height
          fontSize: 14,     // 14pt font size
          color: '000000',  // Black color
          align: 'center'   // Center alignment
        },
        company: {
          x: 1.5,
          y: 6.5,
          w: 7,
          h: 0.5,
          fontSize: 16,
          color: '1976d2',
          align: 'center'
        }
      };
    } else {
      // End slide configuration
      return {
        title: {
          x: 0.3,           // 0.76 cm from left (converted to inches: 0.76/2.54 ≈ 0.3)
          y: 2.0,           // Moved higher up on slide (was 3.2, now 2.0 for better positioning)
          w: 8,             // Wider to accommodate larger font
          h: 1.8,           // Taller for larger font (increased from 1.5)
          fontSize: 70,     // 70pt as requested
          color: '000000',  // Black color
          align: 'left'     // Left-aligned
        },
        period: {
          x: 1.5,
          y: 4.5,
          w: 7,
          h: 0.6,
          fontSize: 16,
          color: '333333',
          align: 'center'
        },
        date: {
          x: 1.5,           // Back to original center positioning
          y: 5.2,           // Back to original center positioning (slightly different for end slide)
          w: 7,             // Back to original width
          h: 0.5,           // Back to original height
          fontSize: 14,     // Keep 14pt font size for end slide
          color: '000000',  // Keep black color
          align: 'center'   // Back to center alignment
        },
        company: {
          x: 1.5,
          y: 6.5,
          w: 7,
          h: 0.5,
          fontSize: 16,
          color: '1976d2',
          align: 'center'
        }
      };
    }
  }

  /**
   * Check if template images are available
   */
  async checkTemplateImagesAvailable(): Promise<{ intro: boolean; end: boolean }> {
    const [introTemplate, endTemplate] = await Promise.all([
      this.loadTemplateImage('intro'),
      this.loadTemplateImage('end')
    ]);

    return {
      intro: introTemplate !== null,
      end: endTemplate !== null
    };
  }

  /**
   * Create template context from component data
   */
  createTemplateContext(period?: string, title?: string, additionalContext?: any): TemplateContext {
    return {
      period: period || 'All Periods',
      date: new Date().toLocaleDateString(),
      title: title || 'Exec Monthly Stats',
      company: 'NBN Co',
      ...additionalContext
    };
  }


}
