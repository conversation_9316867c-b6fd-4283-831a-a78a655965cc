import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IContact } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class ContactService implements IStandardEntityService<IContact> {

    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Contact records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/contacts/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Contact record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/contacts/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Contact record.
     */
    createRecord(payload: IContact): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/contacts");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Contact record.
     */
    updateRecord(recordId: string, payload: IContact): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/contacts/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getContactRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getContactRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createContactRecord(payload: IContact): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateContactRecord(recordId: string, payload: IContact): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
