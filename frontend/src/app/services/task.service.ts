import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ITask } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class TaskService implements IStandardEntityService<ITask> {

    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Task records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/tasks/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Task record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/tasks/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Task record.
     */
    createRecord(payload: ITask): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/tasks");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Task record.
     */
    updateRecord(recordId: string, payload: ITask): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/tasks/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getTaskRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getTaskRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createTaskRecord(payload: ITask): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateTaskRecord(recordId: string, payload: ITask): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
