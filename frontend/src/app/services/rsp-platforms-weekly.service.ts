import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import {
  IRSPPlatformsWeekly,
  IRSPPlatformsWeeklyHistory,
  IRSPPlatformsWeeklyPeriod,
  IRSPPlatformsWeeklySummary,
  IRSPPlatformsWeeklyTrends
} from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService, IStandardAnalyticsService } from '../interfaces/standard-entity-service.interface';

@Injectable({
  providedIn: 'root'
})
export class RSPPlatformsWeeklyService implements IStandardEntityService<IRSPPlatformsWeekly>, IStandardAnalyticsService {

  constructor(private http: HttpClient) { }

  // ***********************************************************************
  // Standard Entity Interface Implementation
  // ***********************************************************************

  /**
   * Gets a list of RSP platforms weekly records with filtering, sorting, and pagination
   */
  getRecords(payload?: any): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly/list");
    return this.http.post(encodedURL, payload || {}) as Observable<any>;
  }

  /**
   * Gets a single RSP platforms weekly record by ID
   */
  getRecord(recordId: string): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly/" + recordId);
    return this.http.get(encodedURL) as Observable<any>;
  }

  /**
   * Creates a new RSP platforms weekly record
   */
  createRecord(record: IRSPPlatformsWeekly): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly");
    return this.http.post(encodedURL, record) as Observable<any>;
  }

  /**
   * Updates an existing RSP platforms weekly record
   */
  updateRecord(recordId: string, record: Partial<IRSPPlatformsWeekly>): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly/" + recordId);
    return this.http.patch(encodedURL, record) as Observable<any>;
  }

  // ***********************************************************************
  // Standard Analytics Interface Implementation
  // ***********************************************************************

  /**
   * Retrieves analytics data for RSP platforms weekly report.
   * This is the main method for getting platforms analytics.
   */
  getAnalyticsData(period?: string): Observable<any> {
    return this.getRSPPlatformsWeeklyHistory();
  }

  // ***********************************************************************
  // RSP Platforms Weekly Specific Services
  // ***********************************************************************

  /**
   * Gets RSP platforms weekly history data for analytics dashboard.
   */
  getRSPPlatformsWeeklyHistory(startWeek?: string, endWeek?: string): Observable<any> {
    let url = environment.base_url + "/RSPPlatformsWeekly/History";

    // Add date range parameters if provided
    if (startWeek && endWeek) {
      const formattedStartWeek = this.formatDateForAPI(startWeek);
      const formattedEndWeek = this.formatDateForAPI(endWeek);
      url += `?startWeek=${encodeURIComponent(formattedStartWeek)}&endWeek=${encodeURIComponent(formattedEndWeek)}`;
    }

    const encodedURL = encodeURI(url);
    return this.http.get(encodedURL) as Observable<any>;
  }

  /**
   * Gets available weeks for RSP platforms weekly data
   */
  getRSPPlatformsWeeklyPeriods(): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/RSPPlatformsWeekly/Periods");
    return this.http.get(encodedURL) as Observable<any>;
  }

  /**
   * Gets RSP platforms weekly data for a specific week
   */
  getRSPPlatformsWeeklyByWeek(weekEnding: string, startWeek?: string, endWeek?: string): Observable<any> {
    const formattedWeekEnding = this.formatDateForAPI(weekEnding);
    let url = environment.base_url + "/RSPPlatformsWeekly/ByWeek/" + encodeURIComponent(formattedWeekEnding);

    // Add date range parameters if provided
    if (startWeek && endWeek) {
      const formattedStartWeek = this.formatDateForAPI(startWeek);
      const formattedEndWeek = this.formatDateForAPI(endWeek);
      url += `?startWeek=${encodeURIComponent(formattedStartWeek)}&endWeek=${encodeURIComponent(formattedEndWeek)}`;
    }

    const encodedURL = encodeURI(url);
    return this.http.get(encodedURL) as Observable<any>;
  }

  /**
   * Gets RSP platforms weekly trends for the last N weeks
   */
  getRSPPlatformsWeeklyTrends(weeksBack: number = 12): Observable<any> {
    const options = { params: new HttpParams().set('weeksBack', weeksBack.toString()) };
    const encodedURL = encodeURI(environment.base_url + "/RSPPlatformsWeekly/Trends");
    return this.http.get(encodedURL, options) as Observable<any>;
  }

  /**
   * Generate AI insights for RSP platforms weekly data
   */
  generateAIInsights(platformsData: any[], comparisonData?: any): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/RSPPlatformsWeekly/GenerateInsights");
    const requestBody = {
      platformsData: platformsData,
      comparisonData: comparisonData
    };
    return this.http.post(encodedURL, requestBody) as Observable<any>;
  }

  /**
   * Generate statistical analysis for RSP platforms weekly data
   */
  generateStatisticalAnalysis(platformsData: any[], comparisonData?: any): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/RSPPlatformsWeekly/GenerateStatisticalAnalysis");
    const requestBody = {
      platformsData: platformsData,
      comparisonData: comparisonData
    };
    return this.http.post(encodedURL, requestBody) as Observable<any>;
  }

  // ***********************************************************************
  // Analytics and Summary Services
  // ***********************************************************************

  /**
   * Gets summary analytics for RSP platforms weekly data
   */
  getRSPPlatformsWeeklySummary(weekEnding?: string): Observable<any> {
    const options = weekEnding ? { params: new HttpParams().set('weekEnding', weekEnding) } : {};
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly/analytics/summary");
    return this.http.get(encodedURL, options) as Observable<any>;
  }

  /**
   * Gets performance analytics for RSP platforms weekly data
   */
  getRSPPlatformsWeeklyPerformance(weeksBack: number = 4): Observable<any> {
    const options = { params: new HttpParams().set('weeksBack', weeksBack.toString()) };
    const encodedURL = encodeURI(environment.base_url + "/api/v1/rsp-platforms-weekly/analytics/performance");
    return this.http.get(encodedURL, options) as Observable<any>;
  }

  // ***********************************************************************
  // Export Services
  // ***********************************************************************

  /**
   * Export RSP platforms weekly data to PDF
   */
  exportToPDF(weekEnding?: string, startWeek?: string, endWeek?: string): Observable<Blob> {
    let url = environment.base_url + "/RSPPlatformsWeekly/Export/PDF";
    const params = new HttpParams()
      .set('weekEnding', weekEnding || '')
      .set('startWeek', startWeek || '')
      .set('endWeek', endWeek || '');

    return this.http.get(url, { 
      params: params,
      responseType: 'blob' 
    }) as Observable<Blob>;
  }

  /**
   * Export RSP platforms weekly data to PowerPoint
   */
  exportToPowerPoint(weekEnding?: string, startWeek?: string, endWeek?: string): Observable<Blob> {
    let url = environment.base_url + "/RSPPlatformsWeekly/Export/PowerPoint";
    const params = new HttpParams()
      .set('weekEnding', weekEnding || '')
      .set('startWeek', startWeek || '')
      .set('endWeek', endWeek || '');

    return this.http.get(url, { 
      params: params,
      responseType: 'blob' 
    }) as Observable<Blob>;
  }

  // ***********************************************************************
  // Utility Methods
  // ***********************************************************************

  /**
   * Format week ending date for display
   */
  formatWeekEnding(weekEnding: string): string {
    try {
      const date = new Date(weekEnding);
      return date.toLocaleDateString('en-AU', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return weekEnding;
    }
  }

  /**
   * Convert date to MySQL format (YYYY-MM-DD)
   */
  formatDateForAPI(dateString: string): string {
    try {
      const date = new Date(dateString);
      return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD
    } catch (error) {
      console.error('Error formatting date for API:', error);
      return dateString;
    }
  }

  /**
   * Get week number from date
   */
  getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * Calculate percentage change between two values
   */
  calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }

  /**
   * Determine status color based on performance
   */
  getStatusColor(actual: number, target: number): string {
    if (actual >= target) return 'green';
    if (actual >= target * 0.95) return 'orange';
    return 'red';
  }

  /**
   * Get platform type icon
   */
  getPlatformTypeIcon(platformType: string): string {
    const iconMap: { [key: string]: string } = {
      'Outages': 'warning',
      'APIGW Availability': 'api',
      'Interfaces': 'cable',
      'API Transaction Success Rate': 'check_circle',
      'Assurance Transaction Success Rate': 'verified',
      'Service Portal Availability': 'web',
      'Connections Transaction Success Rate': 'link',
      'Key KPI Report Availability': 'assessment',
      'Service Health Summary Data Quality': 'data_usage'
    };
    return iconMap[platformType] || 'dashboard';
  }

  /**
   * Sort weeks chronologically
   */
  sortWeeksChronologically(weeks: IRSPPlatformsWeeklyPeriod[]): IRSPPlatformsWeeklyPeriod[] {
    return weeks.sort((a, b) => {
      const dateA = new Date(a.WeekEnding);
      const dateB = new Date(b.WeekEnding);
      return dateB.getTime() - dateA.getTime(); // Most recent first
    });
  }

  /**
   * Get oldest and newest weeks from data
   */
  getWeekRange(weeks: IRSPPlatformsWeeklyPeriod[]): { oldest: string, newest: string } {
    if (!weeks || weeks.length === 0) {
      return { oldest: '', newest: '' };
    }

    const sorted = this.sortWeeksChronologically(weeks);
    return {
      oldest: sorted[sorted.length - 1].WeekEnding,
      newest: sorted[0].WeekEnding
    };
  }
}
