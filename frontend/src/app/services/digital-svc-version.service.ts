import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IDigitalSvcVersion } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class DigitalSvcVersionService implements IStandardEntityService<IDigitalSvcVersion> {

    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Digital Service Version records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-service-versions/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Digital Service Version record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-service-versions/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Digital Service Version record.
     */
    createRecord(payload: IDigitalSvcVersion): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-service-versions");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Digital Service Version record.
     */
    updateRecord(recordId: string, payload: IDigitalSvcVersion): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-service-versions/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getDigitalSvcVersionRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getDigitalSvcVersionRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createDigitalSvcVersionRecord(payload: IDigitalSvcVersion): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateDigitalSvcVersionRecord(recordId: string, payload: IDigitalSvcVersion): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
