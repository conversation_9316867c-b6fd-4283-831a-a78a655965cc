import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IProject } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class ProjectService implements IStandardEntityService<IProject> {
  
    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Project records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/projects/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Project record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/projects/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Project record.
     */
    createRecord(payload: IProject): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/projects");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Project record.
     */
    updateRecord(recordId: string, payload: IProject): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/projects/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getProjectRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getProjectRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createProjectRecord(payload: IProject): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateProjectRecord(recordId: string, payload: IProject): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
