import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IDigitalSvc } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class DigitalSvcService implements IStandardEntityService<IDigitalSvc> {

    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Digital Service records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-services/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Digital Service record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-services/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Digital Service record.
     */
    createRecord(payload: IDigitalSvc): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-services");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Digital Service record.
     */
    updateRecord(recordId: string, payload: IDigitalSvc): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-services/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getDigitalSvcRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getDigitalSvcRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createDigitalSvcRecord(payload: IDigitalSvc): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateDigitalSvcRecord(recordId: string, payload: IDigitalSvc): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
