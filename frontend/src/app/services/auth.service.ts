/**
 * Authentication Service for RSPi Application
 * 
 * Provides authentication functionality with environment-based behavior:
 * - Development mode: Authentication bypass with mock responses
 * - Live mode: JWT-based authentication with LDAP integration
 * 
 * Features:
 * - Login/logout functionality
 * - Token management and validation
 * - User information storage
 * - Authentication state management
 */

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, tap, catchError, of } from 'rxjs';
import { JwtHelperService } from '@auth0/angular-jwt';
import { environment } from '../environment/environment';

export interface User {
  username: string;
  displayName: string;
  email: string;
  groups: string[];
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  user?: User;
  error?: string;
}

export interface AuthValidationResponse {
  success: boolean;
  valid: boolean;
  user?: User;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly TOKEN_KEY = 'rspi_auth_token';
  private readonly USER_KEY = 'rspi_current_user';
  
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  
  private jwtHelper = new JwtHelperService();

  constructor(private http: HttpClient) {
    this.initializeAuthState();
  }

  /**
   * Initialize authentication state from stored tokens
   */
  private initializeAuthState(): void {
    if (!environment.auth_enabled) {
      // Development mode - always authenticated
      const devUser: User = {
        username: 'dev-user',
        displayName: 'Development User',
        email: '<EMAIL>',
        groups: ['dev-group']
      };
      this.currentUserSubject.next(devUser);
      this.isAuthenticatedSubject.next(true);
      return;
    }

    // Live mode - check stored token
    const token = this.getToken();
    const user = this.getStoredUser();
    
    if (token && user && !this.isTokenExpired(token)) {
      this.currentUserSubject.next(user);
      this.isAuthenticatedSubject.next(true);
    } else {
      this.clearAuthData();
    }
  }

  /**
   * Authenticate user with username and password
   */
  login(username: string, password: string): Observable<LoginResponse> {
    const loginUrl = `${environment.base_url}/api/v1/auth/login`;
    
    return this.http.post<LoginResponse>(loginUrl, {
      username: username.trim(),
      password: password
    }).pipe(
      tap(response => {
        if (response.success && response.token && response.user) {
          this.setAuthData(response.token, response.user);
        }
      }),
      catchError(error => {
        console.error('Login error:', error);
        return of({
          success: false,
          error: error.error?.error || 'Login failed. Please try again.'
        });
      })
    );
  }

  /**
   * Logout user and clear authentication data
   */
  logout(): Observable<any> {
    if (!environment.auth_enabled) {
      // Development mode - just clear local state
      this.clearAuthData();
      return of({ success: true });
    }

    // Live mode - call logout endpoint
    const logoutUrl = `${environment.base_url}/api/v1/auth/logout`;
    
    return this.http.post(logoutUrl, {}).pipe(
      tap(() => this.clearAuthData()),
      catchError(error => {
        // Clear local data even if server call fails
        this.clearAuthData();
        return of({ success: true });
      })
    );
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    if (!environment.auth_enabled) {
      return true; // Development mode - always authenticated
    }

    const token = this.getToken();
    return token !== null && !this.isTokenExpired(token);
  }

  /**
   * Get current authentication token
   */
  getToken(): string | null {
    if (!environment.auth_enabled) {
      return 'dev-token'; // Development mode token
    }

    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Get current user information
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Get stored user information from localStorage
   */
  private getStoredUser(): User | null {
    try {
      const userJson = localStorage.getItem(this.USER_KEY);
      return userJson ? JSON.parse(userJson) : null;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token?: string): boolean {
    if (!environment.auth_enabled) {
      return false; // Development mode - tokens never expire
    }

    const authToken = token || this.getToken();
    if (!authToken || authToken === 'dev-token') {
      return false;
    }

    try {
      return this.jwtHelper.isTokenExpired(authToken);
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  /**
   * Validate current token with server
   */
  validateToken(): Observable<AuthValidationResponse> {
    const validateUrl = `${environment.base_url}/api/v1/auth/validate`;
    
    return this.http.get<AuthValidationResponse>(validateUrl).pipe(
      tap(response => {
        if (!response.valid) {
          this.clearAuthData();
        }
      }),
      catchError(error => {
        console.error('Token validation error:', error);
        this.clearAuthData();
        return of({
          success: false,
          valid: false,
          error: 'Token validation failed'
        });
      })
    );
  }

  /**
   * Set authentication data (token and user)
   */
  private setAuthData(token: string, user: User): void {
    if (environment.auth_enabled) {
      localStorage.setItem(this.TOKEN_KEY, token);
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
    
    this.currentUserSubject.next(user);
    this.isAuthenticatedSubject.next(true);
  }

  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    if (environment.auth_enabled) {
      localStorage.removeItem(this.TOKEN_KEY);
      localStorage.removeItem(this.USER_KEY);
    }
    
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
  }

  /**
   * Get user display name for UI
   */
  getUserDisplayName(): string {
    const user = this.getCurrentUser();
    return user?.displayName || user?.username || 'Unknown User';
  }

  /**
   * Check if user has specific group membership
   */
  hasGroup(groupName: string): boolean {
    const user = this.getCurrentUser();
    return user?.groups?.includes(groupName) || false;
  }

  /**
   * Get authentication headers for HTTP requests
   */
  getAuthHeaders(): { [key: string]: string } {
    const token = this.getToken();
    
    if (token && environment.auth_enabled) {
      return {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      };
    }
    
    return {
      'Content-Type': 'application/json'
    };
  }
}
