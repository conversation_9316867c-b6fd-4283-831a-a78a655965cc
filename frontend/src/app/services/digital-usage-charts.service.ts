import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from '../environment/environment';
import { IStandardAnalyticsService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class DigitalUsageChartService implements IStandardAnalyticsService {

    constructor(private http: HttpClient) { }

    // ***********************************************************************
    // Standard Analytics Interface Implementation
    // ***********************************************************************

    /**
     * Retrieves analytics data for a specific period or dataset.
     * This is the main method for getting digital usage analytics.
     */
    getAnalyticsData(period?: string): Observable<any> {
        return this.getDigitalUsageHistory();
    }

    // ***********************************************************************
    // Digital Dashboard Services
    // ***********************************************************************

    /**
     * Gets Digital Usage History data for analytics dashboard.
     */
    getDigitalUsageHistory(): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-usage/history");
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Gets Digital Usage data grouped by RSP for a specific period.
     */
    getDigitalUsageByRSP(period: string): Observable<any> {
        // Add safe, URL encoded search parameter for the period
        const options = period ? { params: new HttpParams().set('period', period) } : {};
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-usage/by-rsp");
        return this.http.get(encodedURL, options) as Observable<any>;
    }

    /**
     * Gets Digital Usage data grouped by Service for a specific period.
     */
    getDigitalUsageBySvc(period: string): Observable<any> {
        // Add safe, URL encoded search parameter for the period
        const options = period ? { params: new HttpParams().set('period', period) } : {};
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-usage/by-service");
        return this.http.get(encodedURL, options) as Observable<any>;
    }

    // ***********************************************************************
    // RSP Digital Dashboard Services
    // ***********************************************************************

    /**
     * Gets Digital Usage chart data for a specific RSP.
     */
    getDigitalUsageChartDataForRSP(accessSeekerId: string): Observable<any> {
        // Add safe, URL encoded search parameter for the access seeker ID
        const options = accessSeekerId ? { params: new HttpParams().set('accessSeekerId', accessSeekerId) } : {};
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-usage/for-rsp");
        return this.http.get(encodedURL, options) as Observable<any>;
    }

    // ***********************************************************************
    // Services Digital Dashboard
    // ***********************************************************************

    /**
     * Gets Digital Usage chart data for all services.
     */
    getDigitalUsageChartDataForServices(): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/digital-usage/for-services");
        return this.http.get(encodedURL) as Observable<any>;
    }


}
