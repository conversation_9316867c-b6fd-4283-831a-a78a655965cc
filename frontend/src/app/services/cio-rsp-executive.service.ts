import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import {
  IDigitalUsageHistory,
  IDigitalServiceUsage,
  IRSPAPIAdoption,
  IRSPDigitalUsage,
  IRSPAPIPercentage
} from '../models/models';
import { environment } from '../environment/environment';

@Injectable({
  providedIn: 'root'
})
export class CioRspExecutiveService {

  constructor(private http: HttpClient) { }

  // Gets Digital Usage History
  getDigitalUsageHistory(startPeriod?: string, endPeriod?: string): Observable<any> {
    let url = environment.base_url + "/CioRspExecutive/DigitalUsageHistory";

    // Add date range parameters if provided
    if (startPeriod && endPeriod) {
      url += `?startPeriod=${encodeURIComponent(startPeriod)}&endPeriod=${encodeURIComponent(endPeriod)}`;
    }

    const encodedURL = encodeURI(url);
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Gets Digital Service Usage
  getDigitalServiceUsage(period: string, startPeriod?: string, endPeriod?: string): Observable<any> {
    let url = environment.base_url + "/CioRspExecutive/DigitalServiceUsage/" + period;

    // Add date range parameters if provided
    if (startPeriod && endPeriod) {
      url += `?startPeriod=${encodeURIComponent(startPeriod)}&endPeriod=${encodeURIComponent(endPeriod)}`;
    }

    const encodedURL = encodeURI(url);
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Gets RSP API Adoption
  getRSPAPIAdoption(period: string): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/CioRspExecutive/RSPAPIAdoption/" + period);
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Gets RSP Digital Usage
  getRSPDigitalUsage(period: string): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/CioRspExecutive/RSPDigitalUsage/" + period);
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Gets RSP API Percentage
  getRSPAPIPercentage(period: string): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/CioRspExecutive/RSPAPIPercentage/" + period);
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Gets available periods for digital usage data
  getDigitalUsagePeriods(): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/CioRspExecutive/DigitalUsagePeriods");
    return this.http.get(encodedURL) as Observable<any>;
  }

  // Generate AI insights for digital usage data
  generateAIInsights(digitalUsageData: any[], comparisonData?: any): Observable<any> {
    const encodedURL = encodeURI(environment.base_url + "/CioRspExecutive/GenerateInsights");
    const requestBody = {
      digitalUsageData: digitalUsageData,
      comparisonData: comparisonData
    };
    return this.http.post(encodedURL, requestBody) as Observable<any>;
  }
}
