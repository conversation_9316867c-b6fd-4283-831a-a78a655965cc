import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { IAccessSeeker } from '../models/models';
import { environment } from '../environment/environment';
import { IStandardEntityService } from '../interfaces/standard-entity-service.interface';

@Injectable({
    providedIn: 'root'
})
export class AccessSeekerService implements IStandardEntityService<IAccessSeeker> {

    constructor(private http: HttpClient) { }

    /**
     * Retrieves a list of Access Seeker records with optional filtering, sorting, and pagination.
     * Used with AG Grid for server-side operations.
     */
    getRecords(payload?: any): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/access-seekers/list");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Retrieves a single Access Seeker record by its ID.
     */
    getRecord(recordId: string): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/access-seekers/" + recordId);
        return this.http.get(encodedURL) as Observable<any>;
    }

    /**
     * Creates a new Access Seeker record.
     */
    createRecord(payload: IAccessSeeker): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/access-seekers");
        return this.http.post(encodedURL, payload) as Observable<any>;
    }

    /**
     * Updates an existing Access Seeker record.
     */
    updateRecord(recordId: string, payload: IAccessSeeker): Observable<any> {
        const encodedURL = encodeURI(environment.base_url + "/api/v1/access-seekers/" + recordId);
        return this.http.patch(encodedURL, payload) as Observable<any>;
    }

    // DEPRECATED METHODS - Use standardized method names above
    // These methods are kept for backward compatibility and will be removed in future versions

    /** @deprecated Use getRecords() instead */
    getAccessSeekerRecords(payload: any): Observable<any> {
        return this.getRecords(payload);
    }

    /** @deprecated Use getRecord() instead */
    getAccessSeekerRecord(recordId: string): Observable<any> {
        return this.getRecord(recordId);
    }

    /** @deprecated Use createRecord() instead */
    createAccessSeekerRecord(payload: IAccessSeeker): Observable<any> {
        return this.createRecord(payload);
    }

    /** @deprecated Use updateRecord() instead */
    updateAccessSeekerRecord(recordId: string, payload: IAccessSeeker): Observable<any> {
        return this.updateRecord(recordId, payload);
    }

}
