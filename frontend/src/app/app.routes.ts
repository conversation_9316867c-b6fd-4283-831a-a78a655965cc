import { Routes } from '@angular/router';
import { AccessSeekerComponent } from './components/access-seeker/access-seeker.component';
import { NoteComponent } from './components/note/note.component';
import { ContactComponent } from './components/contact/contact.component';
import { TaskComponent } from './components/task/task.component';
import { ProjectComponent } from './components/project/project.component';
import { DigitalSvcComponent } from './components/digital-svc/digital-svc.component';
import { DigitalSvcVersionComponent } from './components/digital-svc-version/digital-svc-version.component';
import { DigitalUsageDashboardComponent } from './components/digital-usage-dashboard/digital-usage-dashboard.component';
import { DigitalUsageRspDashboardComponent } from './components/digital-usage-rsp-dashboard/digital-usage-rsp-dashboard.component';
import { DigitalUsageSvcDashboardComponent } from './components/digital-usage-svc-dashboard/digital-usage-svc-dashboard.component';
import { CioRspExecutiveComponent } from './components/cio-rsp-executive/cio-rsp-executive.component';
import { RSPPlatformsWeeklyComponent } from './components/rsp-platforms-weekly/rsp-platforms-weekly.component';
import { TestComponent } from './components/test/test.component';
import { LoginComponent } from './components/login/login.component';
import { authGuard, noAuthGuard } from './guards/auth.guard';

export const routes: Routes = [
    // Authentication routes
    { path: 'login', component: LoginComponent, canActivate: [noAuthGuard] },

    // Protected application routes
    { path: 'accessseeker', component: AccessSeekerComponent, canActivate: [authGuard] },
    { path: 'note', component: NoteComponent, canActivate: [authGuard] },
    { path: 'contact', component: ContactComponent, canActivate: [authGuard] },
    { path: 'task', component: TaskComponent, canActivate: [authGuard] },
    { path: 'project', component: ProjectComponent, canActivate: [authGuard] },
    { path: 'digitalsvc', component: DigitalSvcComponent, canActivate: [authGuard] },
    { path: 'digitalsvcversion', component: DigitalSvcVersionComponent, canActivate: [authGuard] },
    { path: 'digitalusagedashboard', component: DigitalUsageDashboardComponent, canActivate: [authGuard] },
    { path: 'digitalusagerspdashboard', component: DigitalUsageRspDashboardComponent, canActivate: [authGuard] },
    { path: 'digitalusagesvcdashboard', component: DigitalUsageSvcDashboardComponent, canActivate: [authGuard] },
    { path: 'cio-rsp-executive', component: CioRspExecutiveComponent, canActivate: [authGuard] },
    { path: 'rsp-platforms-weekly', component: RSPPlatformsWeeklyComponent, canActivate: [authGuard] },
    { path: 'test', component: TestComponent, canActivate: [authGuard] },

    // Default routes
    { path: '', redirectTo: '/accessseeker', pathMatch: 'full' },
    { path: '**', redirectTo: '/accessseeker' }
];
