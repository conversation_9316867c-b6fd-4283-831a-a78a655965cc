// Access Seeker
export interface IAccessSeeker {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
    AccessSeekerId?: string,
    Name?: string,
    Alias?: string,
    Status?: string,
    Type?: string,
    ParentAccessSeekerRecordId?: string,
    ParentAccessSeekerId?: string,
    Category1?: string,
    Category2?: string,
    Website?: string,
    About?: string,
    Comments?: string,
    SDM?: string,
    AGM?: string,
    CDM?: string
}

// Access Seeker Skeleton (used to pass skeleton Access Seeker data between components)
export interface IAccessSeekerSkeleton {
    id: string,
    AccessSeekerId: string,
}

export interface INote {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
	AccessSeekerRecordId?: string,
    AccessSeekerId?: string,
	Status?: string,
	Type?: string,
	Title?: string,
	Sequence?: string,
	Description?: string,
}

// Note Skeleton (used to pass skeleton Note data between components)
export interface INoteSkeleton {
    id: string,
    AccessSeekerRecordId: string,
    AccessSeekerId: string,
}

export interface IContact {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
    AccessSeekerRecordId?: string,
    AccessSeekerId?: string,
    FirstName?: string,
    LastName?: string,
    Role?: string,
    Phone?: string,
    Email?: string,
    Notes?: string,
}

// Contact Skeleton (used to pass skeleton Contact data between components)
export interface IContactSkeleton {
    id: string,
    AccessSeekerRecordId: string,
    AccessSeekerId: string,
}

export interface ITask {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
    AccessSeekerRecordId?: string,
    AccessSeekerId?: string,
    Title?: string,
    Type?: string,
    Status?: string,
    AssignedTo?: string,
    DueDate?: string,
    CompletedDate?: string,
    Notes ?: string,
}

// Task Skeleton (used to pass skeleton Task data between components)
export interface ITaskSkeleton {
    id: string,
    AccessSeekerRecordId: string,
    AccessSeekerId: string,
}

export interface IProject {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
    AccessSeekerRecordId?: string,
    AccessSeekerId?: string,
	Name?: string,
	Org?: string,
	Category1?: string,
	Category2?: string,
	Status?: string,
	PlannedStartDate?: string,
	ActualStartDate?: string,
	PlannedEndDate?: string,
	ActualEndDate?: string,
	Description?: string,
	Notes?: string,
}

// Project Skeleton (used to pass skeleton Project data between components)
export interface IProjectSkeleton {
    id: string,
    AccessSeekerRecordId: string,
    AccessSeekerId: string,
}

export interface IDigitalSvc {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
	ServiceCode?: string,
	ServiceName?: string,
	Status?: string,
	APIName?: string,
	Purpose?: string,
	Description?: string,
	UsedForConnect?: string,
	UsedForAssure?: string,
	UsedForBilling?: string,
	UsedForOther?: string,
	Notes?: string,
}

// DigitalSvc Skeleton (used to pass skeleton Project data between components)
export interface IDigitalSvcSkeleton {
    id: string,
    ServiceName: string,
    APIName: string,
}

export interface IDigitalSvcVersion {
    id?: string,
    created?: Date,
    created_by?: string,
    modified?: Date,
    modified_by?: string,
    DigitalServiceRecordId?: string,
    ServiceName?: string,
    APIName?: string,
    Version?: string,
    Status?: string,
    ReleaseDate?: Date,
    Description?: string,
}

// DigitalSvcVersion Skeleton (used to pass skeleton Project data between components)
export interface IDigitalSvcVersionSkeleton {
    id: string,
    DigitalServiceRecordId: string,
    ServiceName: string,
    APIName: string,
}

// Enum for Yes/No
export enum IYNEnum {
    Y = 'Y',
    N = 'N'
}

// Data for a Yes / No Dialog box
export interface IYesNoDialogData {
    Title: string;
    Subtitle: string;
    Message: string;
    IconName: string;
}

// Data for the Contact Form  Dialog box
export interface IContactFormDialogData {
    ContactRecordId: string;
    AccessSeekerRecordId: string;
}


// Interface for the Digital Usage History Charts
export interface IDigitalUsageHistory {
    Period: string;
    TotalAPITxns: number;
    TotalPortalTxns: number;
    TotalTxns: number;
}

// Interface for Digital Service Usage
export interface IDigitalServiceUsage {
    APICode?: string;
    ServiceName: string;
    UsedForConnect?: number;
    UsedForAssure?: number;
    LatestVersion?: string;
    APICertCount?: number;
    OnLatestVersionCount?: number;
    NotOnLatestVersionCount?: number;
    CertOnLatestVersionPercent?: number;
    APIUtilCount?: number;
    TotalAPITxns: number;
    TotalPortalTxns: number;
    APIPercentage?: number;
}

// Interface for RSP API Adoption
export interface IRSPAPIAdoption {
    APIName: string;
    CertCount: number;
    UtilCount: number;
}

// Interface for RSP Digital Usage
export interface IRSPDigitalUsage {
    DigitalVolRank?: number;
    RSPName: string;
    APIPercentage?: number;
    TotalServices?: number;
    TxnPerService?: number;
    TotalAPITxns: number;
    TotalPortalTxns: number;
    TotalTxns: number;
}

// Interface for RSP API Percentage
export interface IRSPAPIPercentage {
    DigitalVolRank: number;
    RSPName: string;
    APIPercentage: number;
    ServiceCountRank?: number;
}

// Interface for RSP Platforms Weekly Report
export interface IRSPPlatformsWeekly {
    id?: string;
    created?: Date;
    created_by?: string;
    modified?: Date;
    modified_by?: string;
    WeekEnding: string;
    WeekNumber?: number;
    Year?: number;
    PlatformServiceType: string;
    CurrentWeekTarget?: number;
    CurrentWeekActual?: number;
    LastWeekTarget?: number;
    LastWeekActual?: number;
    LastMonthTarget?: number;
    LastMonthActual?: number;
    RollingAverage?: number;
    ChangeFromLastWeek?: number;
    IncidentCount?: number;
    ServiceAvailability?: number;
    APITransactionSuccess?: number;
    PortalAvailability?: number;
    Status?: string;
    Notes?: string;
}

// RSP Platforms Weekly Skeleton (used to pass skeleton data between components)
export interface IRSPPlatformsWeeklySkeleton {
    id: string;
    WeekEnding: string;
    PlatformServiceType: string;
}

// Interface for RSP Platforms Weekly History
export interface IRSPPlatformsWeeklyHistory {
    WeekEnding: string;
    WeekNumber: number;
    Year: number;
    PlatformServiceType: string;
    CurrentWeekActual: number;
    CurrentWeekTarget: number;
    IncidentCount: number;
    ServiceAvailability: number;
}

// Interface for RSP Platforms Weekly Period
export interface IRSPPlatformsWeeklyPeriod {
    WeekEnding: string;
    WeekLabel: string;
    WeekNumber: number;
    Year: number;
}

// Interface for RSP Platforms Weekly Summary
export interface IRSPPlatformsWeeklySummary {
    totalPlatforms: number;
    averageAvailability: number;
    totalIncidents: number;
    platformsAboveTarget: number;
    platformsBelowTarget: number;
}

// Interface for RSP Platforms Weekly Trends
export interface IRSPPlatformsWeeklyTrends {
    [platformType: string]: {
        WeekEnding: string;
        ActualValue: number;
        TargetValue: number;
        IncidentCount: number;
        ServiceAvailability: number;
    }[];
}