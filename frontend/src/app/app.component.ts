import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet, Router } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatRippleModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { Subject, takeUntil } from 'rxjs';

// Import custom components
import { SideNavbarComponent } from './components/side-navbar/side-navbar.component';
import { AboutAppDialogComponent } from './components/about-app-dialog/about-app-dialog.component';
import { AuthService, User } from './services/auth.service';
import { environment } from './environment/environment';

@Component({
    selector: 'app-root',
    imports: [
        CommonModule,
        RouterOutlet,
        MatToolbarModule,
        MatSidenavModule,
        MatIconModule,
        MatMenuModule,
        MatButtonModule,
        SideNavbarComponent,
        MatRippleModule
    ],
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'RSPTracker';
  currentUser: User | null = null;
  isAuthenticated = false;
  environment = environment;

  private destroy$ = new Subject<void>();

  constructor(
    public dialog: MatDialog,
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Subscribe to authentication state changes
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
      });

    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuth => {
        this.isAuthenticated = isAuth;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  openAboutAppDialog(): void {
    const dialogRef = this.dialog.open(AboutAppDialogComponent, {
      width: '700px',
      data: {},
    });

    dialogRef.afterClosed().subscribe(result => {
      // Nothing to do...
    });
  }

  logout(): void {
    this.authService.logout().subscribe(() => {
      this.router.navigate(['/login']);
    });
  }

  getUserDisplayName(): string {
    return this.authService.getUserDisplayName();
  }
}
