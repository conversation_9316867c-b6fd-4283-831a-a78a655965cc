<mat-toolbar class="example-header" color="primary">
  <a href="/" mat-icon-button aria-label="Home">
      <mat-icon class="material-icons md-24">home</mat-icon>
  </a>
  <span style="padding-left:5px;">RSP Tracker Prototype</span>
  <span class="example-spacer" style="flex: 1 1 auto;"></span>

  <span style="font-size: 0.75em; padding-right: 40px;" *ngIf="isAuthenticated">
    User: {{ getUserDisplayName() }}
    <span *ngIf="!environment.auth_enabled" style="color: #ffeb3b; margin-left: 8px;">(DEV)</span>
  </span>
  <button mat-icon-button [matMenuTriggerFor]="menu" class="menu-button" matRipple [matRippleRadius]="20" >
      <mat-icon>menu</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item (click)="logout()" *ngIf="isAuthenticated && environment.auth_enabled">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
      <button mat-menu-item (click)="openAboutAppDialog()">
          <mat-icon>info</mat-icon>
          <span>About</span>
        </button>
    </mat-menu>

</mat-toolbar>

<mat-sidenav-container class="example-container">
  <mat-sidenav mode="side" opened class="example-sidenav">
      <app-side-navbar></app-side-navbar>
  </mat-sidenav>
  
  <mat-sidenav-content>
      <router-outlet></router-outlet>
  </mat-sidenav-content>
</mat-sidenav-container>