# RSPi Component Templates

## Overview

This directory contains standardized templates for creating new components in the RSPi application. These templates ensure consistency, prevent layout overflow issues, and maintain responsive design standards across all components.

## 🚨 MANDATORY USAGE

**All new components MUST use these templates** to prevent layout overflow issues and maintain consistency.

## Files

### `component-template.scss`
Standard SCSS template that includes:
- ✅ Global box-sizing rule (`* { box-sizing: border-box; }`)
- ✅ Standard container patterns
- ✅ Form field containment rules
- ✅ Responsive breakpoints
- ✅ Material UI integration
- ✅ Utility classes

## How to Use

### 1. Copy Template
```bash
cp frontend/src/app/templates/component-template.scss frontend/src/app/components/your-component/your-component.component.scss
```

### 2. Customize Variables
Update the color variables and component-specific values:
```scss
// Customize these variables for your component
$primary-color: #673ab7;        // Your primary color
$card-background: #ffffff;      // Background color
// ... other variables
```

### 3. Rename Classes
Replace `component-` prefixes with your component name:
```scss
// Change from:
.component-container { }
.component-wrapper { }
.component-card { }

// To:
.login-container { }
.login-wrapper { }
.login-card { }
```

### 4. Customize Layout
Adjust the max-width and padding values based on your component needs:
```scss
.your-component-wrapper {
  max-width: 450px; // Adjust based on content needs
}

.your-component-content {
  padding: 28px 28px 20px; // Adjust padding as needed
}
```

## 📋 Pre-Implementation Checklist

Before using the template, ensure you understand:

- [ ] Global box-sizing rule is mandatory
- [ ] All containers must have explicit width constraints
- [ ] Form fields must be properly contained
- [ ] Responsive breakpoints are required
- [ ] Testing at multiple viewport sizes is mandatory

## 🎯 Standard Patterns Included

### Container Pattern
```scss
.component-wrapper {
  position: relative;
  width: 100%;
  max-width: 450px;
  box-sizing: border-box;
}
```

### Form Field Pattern
```scss
.form-field {
  width: 100%;
  box-sizing: border-box;

  .mat-mdc-form-field {
    width: 100%;
    box-sizing: border-box;
  }
}
```

### Responsive Pattern
```scss
@media (max-width: 768px) {
  .component-content {
    padding: 20px 20px 16px;
  }
}
```

## 🔍 Testing Requirements

After implementing your component:

1. **Multi-Viewport Testing**: Test at 320px, 768px, 1024px, 1920px
2. **Overflow Check**: Verify no horizontal scrollbars
3. **Form Validation**: Ensure all form fields are contained
4. **Responsive Behavior**: Verify mobile responsiveness

## 🚫 Common Mistakes to Avoid

1. **Removing box-sizing rule**: Never remove `* { box-sizing: border-box; }`
2. **Fixed widths**: Don't use fixed pixel widths without max-width constraints
3. **Ignoring padding**: Always account for padding in width calculations
4. **Skipping responsive**: Always implement mobile breakpoints
5. **Material UI conflicts**: Always override Material form field widths

## 📊 Quality Standards

Your component must meet:
- ✅ Zero horizontal scrollbars at any viewport size
- ✅ All form fields properly contained
- ✅ Responsive design at standard breakpoints
- ✅ Consistent spacing and padding
- ✅ Material UI integration without conflicts

## 🔧 VS Code Integration

Add to your VS Code settings for automatic validation:
```json
{
  "scss.lint.boxModel": "warning",
  "scss.lint.duplicateProperties": "warning",
  "scss.lint.zeroUnits": "warning"
}
```

## 📞 Support

If you encounter layout issues:
1. Review the augment-guidelines.md CSS/SCSS standards section
2. Check that all mandatory patterns are implemented
3. Test at multiple viewport sizes
4. Verify box-sizing is applied to all elements

## 🔄 Template Updates

This template will be updated as new standards are established. Always use the latest version for new components.

---

**Remember: Following these templates is not optional. They are mandatory standards to prevent layout overflow issues and maintain consistency across the RSPi application.**
