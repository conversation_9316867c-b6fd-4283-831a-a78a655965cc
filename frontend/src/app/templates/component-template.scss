// Component SCSS Template - RSPi Standards
// Use this template as starting point for all new components
// Ensures layout overflow prevention and responsive design

// MANDATORY: Global box-sizing for all elements
* {
  box-sizing: border-box;
}

// Color Variables (customize as needed)
$primary-color: #673ab7;
$primary-light: #9c27b0;
$primary-dark: #512da8;
$accent-color: #ff4081;
$card-background: #ffffff;
$text-primary: #212121;
$text-secondary: #757575;
$text-hint: #9e9e9e;
$success-color: #4caf50;
$warning-color: #ff9800;
$error-color: #f44336;

// Main Container (Standard Pattern)
.component-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
}

// Component Wrapper (Standard Pattern)
.component-wrapper {
  position: relative;
  width: 100%;
  max-width: 450px; // Adjust based on component needs
  box-sizing: border-box;
}

// Component Card (Standard Pattern)
.component-card {
  background: $card-background;
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
  width: 100%;
  box-sizing: border-box;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 
      0 25px 50px rgba(0, 0, 0, 0.15),
      0 12px 24px rgba(0, 0, 0, 0.08);
  }
}

// Header Section (Standard Pattern)
.component-header {
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  color: white;
  padding: 28px 28px 20px;
  text-align: center;
  box-sizing: border-box;

  h1, h2, h3 {
    margin: 0 0 8px 0;
    font-weight: 600;
  }

  .subtitle {
    margin: 0;
    opacity: 0.9;
    font-weight: 400;
  }
}

// Content Section (Standard Pattern)
.component-content {
  padding: 28px 28px 20px;
  width: 100%;
  box-sizing: border-box;

  .content-header {
    text-align: center;
    margin-bottom: 32px;

    h2 {
      margin: 0 0 8px 0;
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
    }

    .content-subtitle {
      margin: 0;
      font-size: 14px;
      color: $text-secondary;
      line-height: 1.4;
    }
  }
}

// Form Styles (Standard Pattern)
.component-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
  box-sizing: border-box;

  .form-field {
    width: 100%;
    box-sizing: border-box;

    .mat-mdc-form-field {
      width: 100%;
      box-sizing: border-box;
    }

    .mat-mdc-form-field-wrapper {
      padding-bottom: 0;
      width: 100%;
      box-sizing: border-box;
    }

    .form-input {
      font-size: 16px;
      padding: 14px 16px;
      width: 100%;
      box-sizing: border-box;
    }

    .field-icon {
      color: $text-hint;
    }

    // Error state styling
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        color: $error-color;
      }
    }
  }

  .form-actions {
    margin-top: 16px;

    .action-button {
      width: 100%;
      height: 56px;
      font-size: 16px;
      font-weight: 600;
      border-radius: 12px;
      background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
      box-shadow: 0 4px 12px rgba($primary-color, 0.3);
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba($primary-color, 0.4);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }
  }
}

// Footer Section (Standard Pattern)
.component-footer {
  padding: 20px 28px;
  background-color: #fafafa;
  border-top: 1px solid #e0e0e0;
  box-sizing: border-box;

  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: $text-hint;

    .copyright {
      font-weight: 500;
    }

    .version {
      font-weight: 400;
    }
  }
}

// Data Table Styles (Standard Pattern)
.component-table {
  width: 100%;
  box-sizing: border-box;

  .ag-theme-alpine {
    width: 100%;
    box-sizing: border-box;
  }

  .table-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;

    .action-button {
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// Responsive Design (MANDATORY)
@media (max-width: 768px) {
  .component-container {
    padding: 16px;
  }

  .component-wrapper {
    max-width: 100%;
  }

  .component-header {
    padding: 20px 20px 16px;
  }

  .component-content {
    padding: 20px 20px 16px;
  }

  .component-footer {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .component-form {
    gap: 16px;

    .form-actions .action-button {
      height: 48px;
      font-size: 14px;
    }
  }

  .component-header {
    padding: 16px 16px 12px;
  }

  .component-content {
    padding: 16px 16px 12px;
  }

  .component-footer {
    padding: 12px 16px;
  }
}

// Utility Classes
.full-width {
  width: 100% !important;
  box-sizing: border-box;
}

.no-overflow {
  overflow: hidden;
}

.responsive-container {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

// Loading States
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  box-sizing: border-box;
}

// Error States
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  text-align: center;
  box-sizing: border-box;

  .error-icon {
    font-size: 48px;
    color: $error-color;
    margin-bottom: 16px;
  }

  .error-message {
    color: $text-secondary;
    margin-bottom: 16px;
  }
}
