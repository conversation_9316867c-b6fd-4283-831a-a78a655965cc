/**
 * Authentication Guard for RSPi Application
 * 
 * Protects routes from unauthorized access with environment-based behavior:
 * - Development mode: Always allows access (authentication bypass)
 * - Live mode: Requires valid JWT token, redirects to login if not authenticated
 * 
 * Usage:
 * - Apply to routes in app.routes.ts using canActivate: [authGuard]
 * - Automatically handles authentication state and redirects
 */

import { inject } from '@angular/core';
import { Router } from '@angular/router';
import { CanActivateFn } from '@angular/router';
import { AuthService } from '../services/auth.service';
import { environment } from '../environment/environment';

/**
 * Authentication guard function
 * 
 * @param route - The activated route snapshot
 * @param state - The router state snapshot
 * @returns boolean - true if access is allowed, false otherwise
 */
export const authGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Development mode - always allow access
  if (!environment.auth_enabled) {
    return true;
  }

  // Live mode - check authentication
  if (authService.isAuthenticated()) {
    return true;
  }

  // Not authenticated - redirect to login
  console.log('Access denied - redirecting to login');
  router.navigate(['/login'], { 
    queryParams: { returnUrl: state.url }
  });
  
  return false;
};

/**
 * Reverse authentication guard - prevents access when already authenticated
 * Useful for login page to redirect authenticated users away
 */
export const noAuthGuard: CanActivateFn = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Development mode - allow access to login page for testing
  if (!environment.auth_enabled) {
    return true;
  }

  // Live mode - redirect if already authenticated
  if (authService.isAuthenticated()) {
    console.log('Already authenticated - redirecting to home');
    router.navigate(['/']);
    return false;
  }

  return true;
};
