import { Observable } from 'rxjs';

/**
 * Standard interface for all entity services in the RSPi application.
 * This interface ensures consistent method naming across all CRUD services.
 * 
 * @template T The entity type (e.g., IAccessSeeker, IContact, etc.)
 */
export interface IStandardEntityService<T> {
  /**
   * Retrieves a list of records with optional filtering, sorting, and pagination.
   * Used with AG Grid for server-side operations.
   * 
   * @param payload Optional payload containing filters, sorting, and pagination parameters
   * @returns Observable containing the list of records and total count
   */
  getRecords(payload?: any): Observable<any>;

  /**
   * Retrieves a single record by its ID.
   * 
   * @param recordId The unique identifier of the record
   * @returns Observable containing the single record data
   */
  getRecord(recordId: string): Observable<any>;

  /**
   * Creates a new record.
   * 
   * @param payload The data for the new record
   * @returns Observable containing the created record data
   */
  createRecord(payload: T): Observable<any>;

  /**
   * Updates an existing record.
   * 
   * @param recordId The unique identifier of the record to update
   * @param payload The updated data for the record
   * @returns Observable containing the updated record data
   */
  updateRecord(recordId: string, payload: T): Observable<any>;
}

/**
 * Standard interface for analytics services in the RSPi application.
 * This interface ensures consistent method naming for analytics and reporting services.
 */
export interface IStandardAnalyticsService {
  /**
   * Retrieves analytics data for a specific period or dataset.
   * 
   * @param period Optional period parameter for time-based analytics
   * @returns Observable containing the analytics data
   */
  getAnalyticsData(period?: string): Observable<any>;

  /**
   * Generates insights based on provided data.
   * 
   * @param data The data to analyze
   * @param options Optional parameters for insight generation
   * @returns Observable containing the generated insights
   */
  generateInsights?(data: any[], options?: any): Observable<any>;
}
