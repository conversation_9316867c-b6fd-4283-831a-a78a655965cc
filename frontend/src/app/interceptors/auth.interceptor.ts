/**
 * Authentication HTTP Interceptor for RSPi Application
 * 
 * Automatically adds JWT tokens to outgoing HTTP requests with environment-based behavior:
 * - Development mode: No token modification (authentication bypass)
 * - Live mode: Adds Authorization header with Bearer token
 * 
 * Features:
 * - Automatic token injection for authenticated requests
 * - Error handling for 401 Unauthorized responses
 * - Token refresh logic (if needed)
 * - Logout on authentication failures
 */

import { inject } from '@angular/core';
import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { Router } from '@angular/router';
import { catchError, throwError } from 'rxjs';
import { AuthService } from '../services/auth.service';
import { environment } from '../environment/environment';

/**
 * Authentication HTTP interceptor function
 * 
 * @param req - The outgoing HTTP request
 * @param next - The next handler in the chain
 * @returns Observable of the HTTP response
 */
export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const authService = inject(AuthService);
  const router = inject(Router);

  // Skip authentication for auth endpoints to avoid circular calls
  const isAuthEndpoint = req.url.includes('/api/v1/auth/');
  
  // Development mode or auth endpoint - proceed without modification
  if (!environment.auth_enabled || isAuthEndpoint) {
    return next(req).pipe(
      catchError((error: HttpErrorResponse) => handleHttpError(error, authService, router))
    );
  }

  // Live mode - add authentication token
  const token = authService.getToken();
  
  if (token && !authService.isTokenExpired(token)) {
    // Clone request and add Authorization header
    const authReq = req.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
    
    return next(authReq).pipe(
      catchError((error: HttpErrorResponse) => handleHttpError(error, authService, router))
    );
  }

  // No valid token - proceed without auth header
  return next(req).pipe(
    catchError((error: HttpErrorResponse) => handleHttpError(error, authService, router))
  );
};

/**
 * Handle HTTP errors, particularly authentication-related errors
 * 
 * @param error - The HTTP error response
 * @param authService - The authentication service
 * @param router - The Angular router
 * @returns Observable that throws the error
 */
function handleHttpError(
  error: HttpErrorResponse, 
  authService: AuthService, 
  router: Router
) {
  // Handle authentication errors
  if (error.status === 401) {
    console.warn('Authentication error detected - clearing auth data');
    
    // Clear authentication data
    authService.logout().subscribe();
    
    // Redirect to login if in live mode and not already on login page
    if (environment.auth_enabled && !router.url.includes('/login')) {
      router.navigate(['/login'], {
        queryParams: { returnUrl: router.url }
      });
    }
  }
  
  // Handle forbidden errors
  if (error.status === 403) {
    console.warn('Access forbidden - insufficient permissions');
    // Could redirect to an access denied page or show a message
  }
  
  // Handle server errors
  if (error.status >= 500) {
    console.error('Server error detected:', error.message);
    // Could show a global error message or redirect to error page
  }
  
  // Re-throw the error for component-level handling
  return throwError(() => error);
}
