# Presentation Templates

This folder contains template slides for PowerPoint and PDF exports.

## File Structure

```
presentation-templates/
├── images/
│   ├── intro.png           # Introduction slide template image
│   ├── intro.jpg           # Alternative format for intro slide
│   ├── end.png             # Ending slide template image
│   └── end.jpg             # Alternative format for end slide
└── README.md               # This file
```

## Hybrid Image Templates (Recommended Approach)

### intro.png / intro.jpg
- High-resolution background image (1920x1080 recommended for 16:9 aspect ratio)
- Should contain your background design, logos, and static branding elements
- **Leave space for dynamic text areas** (title, period, date, company)
- Dynamic text will be overlaid automatically in predetermined positions
- Supported formats: PNG, JPG, JPEG

### end.png / end.jpg
- High-resolution background image (1920x1080 recommended for 16:9 aspect ratio)
- Should contain your background design, logos, and static branding elements
- **Leave space for dynamic text areas** (title, period, date, company)
- Dynamic text will be overlaid automatically in predetermined positions
- Supported formats: PNG, JPG, JPEG

### Dynamic Text Overlays
The system automatically adds the following dynamic text over your background images:
- **Title**: "Exec Monthly Stats" (center, large font)
- **Period**: "Report Period: [Selected Period]" (center, medium font)
- **Date**: "Generated: [Current Date]" (center, small font)
- **Company**: "NBN Co" or "© NBN Co" (center, medium font)

## Usage

1. Place your template image files in the `images/` folder
2. The export functionality will automatically detect and use these templates
3. If templates are not found, the system will fall back to programmatically generated slides
4. Images are automatically resized to fit slide dimensions while maintaining aspect ratio

## Template Creation Tips

### Creating Hybrid Template Images
1. **Design Background in PowerPoint**: Create your slide background with branding elements
2. **Reserve Text Areas**: Leave clear spaces where dynamic text will be overlaid:
   - **Center-top area**: For title text (large, bold)
   - **Center-middle area**: For period information
   - **Center-lower area**: For date and company information
3. **Export as Images**: Use PowerPoint's "Save As" → "PNG" or "JPEG" option
4. **High Resolution**: Use at least 1920x1080 pixels for crisp quality
5. **Consistent Branding**: Include logos, colors, and background styling
6. **Safe Margins**: Keep background elements away from text overlay areas

### Text Overlay Positioning
The system uses these predetermined positions for dynamic text:
- **Title**: Center area, 1.5" from left, 2.5" from top
- **Period**: Center area, 1.5" from left, 4" from top
- **Date**: Center area, 1.5" from left, 4.8" from top
- **Company**: Center area, 1.5" from left, 6.5" from top

### Advantages of Hybrid Templates
- ✅ **Professional Quality**: Uses actual PowerPoint designs as backgrounds
- ✅ **Dynamic Content**: Real-time text replacement for dates, periods, titles
- ✅ **Simple & Reliable**: No complex XML parsing or coordinate conversion
- ✅ **Perfect Branding**: Background preserves exact fonts, colors, and styling
- ✅ **Easy Creation**: Design background in PowerPoint, export as image
- ✅ **Future-Proof**: Works with any PowerPoint design complexity
- ✅ **Browser Compatible**: Works entirely in Angular frontend
