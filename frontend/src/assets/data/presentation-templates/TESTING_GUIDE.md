# Testing Guide for Hybrid Template Slides

## Quick Test Setup (Hybrid Image + Text Overlay)

### Step 1: Create a Background Template
1. Open PowerPoint
2. Create a new blank presentation with 16:9 aspect ratio
3. Design your intro slide background with:
   - Your company logo and branding
   - Background colors, shapes, or images
   - **Important**: Leave clear space in the center for dynamic text:
     - Top center: For title text
     - Middle center: For period information
     - Lower center: For date and company text
4. **Do NOT include** title, period, date, or company text (these will be added dynamically)
5. Export the slide as an image:
   - Go to File → Export → Change File Type → PNG or JPEG
   - Choose "Just This Slide" and save as `intro.png` or `intro.jpg`
   - Save in `/frontend/src/assets/data/presentation-templates/images/`

### Step 2: Create End Background Template
1. Create another slide with:
   - Your company branding and background styling
   - Consistent design with your intro slide
   - **Leave space** for dynamic text overlay
2. Export as image and save as `end.png` or `end.jpg` in the same folder

### Step 3: Test the Implementation
1. Start your application
2. Navigate to CIO RSP Executive Digital Usage
3. Open browser console (F12) to see debug logs
4. Click "PowerPoint" export button
5. Check the console for template processing logs:
   - `📄 Using intro template image for title slide`
   - `📄 Loaded template image: /assets/data/presentation-templates/images/intro.png`
   - `📄 Adding intro hybrid template slide (image + text overlay)`
   - `📄 Added title overlay: "Exec Monthly Stats"`
   - `📄 Added period overlay: "Report Period: [Your Selected Period]"`
   - `📄 Added date overlay: "Generated: [Current Date]"`
   - `📄 Added company overlay: "NBN Co"`
   - `📄 Successfully created intro hybrid template slide with 4 text overlays`

### Expected Results
- **First slide**: Your custom background image with dynamic text overlaid
  - Background preserves your exact branding and design
  - Dynamic text shows current period, date, and title
  - Text is positioned in predetermined areas over your background
- **Last slide**: Your custom end background with dynamic text overlaid
- **Professional quality**: Background images maintain full resolution and quality
- **Dynamic content**: Text updates automatically based on selected period and current date
- **Date text visibility**: "Generated: [date]" should appear in bottom-right area
- **Fallback protection**: If templates are missing, system uses default slides

### Expected Console Output
Look for this confirmation message in the console:
- `📄 Added date overlay: "Generated: [date]" at position (5.5, 6.0)` - Confirms date text was added in bottom-right area

### Troubleshooting Steps
1. **Check console logs** for date overlay confirmation
2. **Verify positioning** - Date should appear at bottom-right area (x:5.5, y:6.0)
3. **Check slide dimensions** - Positioning is within 10"x7.5" slide bounds
4. **Verify text styling** - 12pt gray on intro, 14pt gray on end slide

### Troubleshooting

#### Template Not Loading
- Check file names exactly match: `intro.png`, `intro.jpg`, `end.png`, or `end.jpg`
- Verify files are in correct folder: `/frontend/src/assets/data/presentation-templates/images/`
- Check browser console for 404 errors
- Supported formats: PNG, JPG, JPEG (case-insensitive)

#### Image Quality Issues
- Use high resolution images (1920x1080 recommended)
- Ensure images are in 16:9 aspect ratio for best fit
- Check that images aren't corrupted or too large
- Try different image formats (PNG vs JPG)

#### Sizing Issues
- Images are automatically resized to fit slide dimensions
- Aspect ratio is preserved to prevent distortion
- If image appears too small/large, adjust original image dimensions
- Check console for image dimension logs

#### Fallback Behavior
- If any step fails, system automatically uses default slides
- Check console logs to identify where the process failed
- Missing templates will trigger fallback with appropriate logging

### Advanced Testing

#### Test Different Content Types
- Text with various fonts and sizes
- Colored text and backgrounds
- Multiple text boxes
- Different alignments (left, center, right)

#### Test Placeholder Variations
- Add custom placeholders by modifying template context
- Test with long and short content
- Test with special characters

#### Performance Testing
- Test with larger template files
- Test with multiple slides in template (only first slide is used)
- Monitor console for processing times

### Debug Information
The implementation provides extensive logging:
- Template loading status
- Placeholder processing results
- XML parsing progress
- Content extraction details
- Slide creation success/failure
- Fallback triggers

All debug messages are prefixed with `📄` for easy identification in console.
