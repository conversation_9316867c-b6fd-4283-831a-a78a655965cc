# Hybrid Template Instructions

## How to Create Custom Intro and End Slide Backgrounds

### Step 1: Design Background Templates
1. Open PowerPoint and create a new presentation (16:9 aspect ratio)
2. Design your intro slide background with:
   - Your company logo and branding elements
   - Background colors, gradients, or images
   - Decorative elements and styling
   - **Important**: Leave the center area clear for dynamic text
3. **Do NOT include** title, period, date, or company text (these will be added automatically)
4. Export as high-resolution image:
   - Go to File → Export → Change File Type → PNG or JPEG
   - Choose "Just This Slide"
   - Save as `intro.png` or `intro.jpg`
   - Place in `/frontend/src/assets/data/presentation-templates/images/`

### Step 2: Create End Background Template
1. Design your end slide background with consistent branding
2. Leave center area clear for dynamic text overlay
3. Export and save as `end.png` or `end.jpg` in the same folder

### Step 3: Test Your Templates
1. Place your template image files in the images folder
2. Run the application and navigate to CIO RSP Executive Digital Usage
3. Click the PowerPoint export button
4. Check that your custom backgrounds appear with dynamic text overlaid

### Dynamic Text Areas (Automatically Added)

The system will automatically overlay the following text on your background images:

#### Intro Slide Text:
- **Title**: "Exec Monthly Stats" (positioned 0.76cm from left, higher on slide, Aptos font, 36pt, bold, black)
- **Period**: "Report Period: [Selected Period]" (center-middle, medium, dark gray, Aptos font)
- **Date**: "Generated: [Current Date]" (center area, 14pt, black, Aptos font)
- **Company**: "NBN Co" (bottom-center, medium, blue, bold)

#### End Slide Text:
- **Title**: "Thank You" (positioned 0.76cm from left, higher on slide, Aptos font, 70pt, bold, black)
- **Period**: "Report Period: [Selected Period]" (center-middle, medium, dark gray, Aptos font)
- **Date**: "Generated: [Current Date]" (center area, 14pt, black, Aptos font)
- **Company**: "© NBN Co" (bottom-center, medium, blue)

### Text Positioning Guide

Reserve these areas in your background design for dynamic text:

```
Intro Slide Layout:
┌─────────────────────────────────────────┐
│                 Logo/Branding           │
│[EXEC MONTHLY]   ← Large "Exec Monthly   │  ← Aptos 70pt (0.76cm from left)
│[STATS]            Stats"                │
│                                         │
│         [PERIOD AREA]                   │  ← Period information (center)
│        [COMPANY AREA]                   │  ← Company name (center)
│  Additional Branding        [DATE AREA] │  ← Date (bottom-right corner)
└─────────────────────────────────────────┘

End Slide Layout:
┌─────────────────────────────────────────┐
│                 Logo/Branding           │
│[THANK YOU]      ← Large "Thank You"     │  ← Aptos 70pt (0.76cm from left, higher position)
│                                         │
│                                         │
│         [PERIOD AREA]                   │  ← Period information (center)
│        [COMPANY AREA]                   │  ← Company name (center)
│  Additional Branding        [DATE AREA] │  ← Date (bottom-right corner)
└─────────────────────────────────────────┘
```

**Key positioning specifications:**
- **Intro Title**: "Exec Monthly Stats" positioned at 0.76cm from left, Aptos font 36pt, black
- **End Title**: "Thank You" positioned at 0.76cm from left, Aptos font 70pt, black
- **Period**: Center area (both slides) - "Report Period: [Selected Period]"
- **Date**: Bottom-right area (both slides) - "Generated: [Current Date]" (12pt intro, 14pt end)
- **Company**: Bottom-center area (both slides) - "NBN Co" / "© NBN Co"

### Design Tips
- **Keep it simple**: Complex backgrounds may interfere with text readability
- **Use contrasting colors**: Ensure text will be visible over your background
- **Test with different content**: Text length may vary based on period selection
- **Maintain consistency**: Use similar styling between intro and end slides
- **High resolution**: Use 1920x1080 for best quality in final presentation

### Troubleshooting
- If templates don't load, check file names exactly match: `intro.png`, `intro.jpg`, `end.png`, `end.jpg`
- Check file paths: `/frontend/src/assets/data/presentation-templates/images/`
- Verify image formats are supported: PNG, JPG, JPEG
- Check browser console for template loading errors
- Templates will automatically fall back to default slides if there are any issues

### Advanced Customization
- Modify text positions in `getTextOverlayConfig()` method in `PresentationTemplateService`
- Adjust font sizes, colors, and alignment for different branding requirements
- Add additional text overlays by extending the configuration interface
- The system preserves full background image quality and styling
