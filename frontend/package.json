{"name": "r<PERSON><PERSON><PERSON>", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.1.6", "@angular/cdk": "^19.1.4", "@angular/common": "^19.1.6", "@angular/compiler": "^19.1.6", "@angular/core": "^19.1.6", "@angular/forms": "^19.1.6", "@angular/material": "^19.1.4", "@angular/platform-browser": "^19.1.6", "@angular/platform-browser-dynamic": "^19.1.6", "@angular/router": "^19.1.6", "@auth0/angular-jwt": "^5.2.0", "@ngx-env/builder": "^19.0.4", "ag-charts-angular": "^11.1.1", "ag-grid-angular": "^33.1.0", "ag-grid-enterprise": "^33.1.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "pptxgenjs": "^4.0.0", "rxjs": "~7.8.0", "simple-statistics": "^7.8.8", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.1.7", "@angular-devkit/core": "^19.2.6", "@angular/cli": "^19.1.7", "@angular/compiler-cli": "^19.1.6", "@types/jasmine": "~5.1.0", "@types/node": "^22.14.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.5.4"}}