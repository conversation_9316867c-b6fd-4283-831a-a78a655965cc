# RSPi Authentication System - Quick Start Guide

## 🚀 Quick Start Commands

### Development Mode (No Authentication)
```bash
# Start in development mode (default)
./start_app.sh
# OR
./start_dev.sh
# OR
./start_app.sh --dev
```

### Production Mode (LDAP Authentication)
```bash
# Start in production mode
./start_app.sh --prod
# OR
./start_prod.sh
```

## 🔧 Environment Modes

| Mode | Authentication | Login Required | User Context | LDAP |
|------|---------------|----------------|--------------|------|
| **Development** | ❌ Disabled | ❌ No | `dev-user (DEV)` | ❌ Bypassed |
| **Production** | ✅ Enabled | ✅ Yes | LDAP User | ✅ Required |

## 📋 Prerequisites

### Required Software
- **Node.js** (v20.9.0+)
- **Python 3** (3.8+)
- **Docker** (for MySQL)
- **npm** (for frontend dependencies)

### Required Dependencies
```bash
# Backend dependencies (auto-installed)
pip install PyJWT==2.8.0 ldap3==2.9.1 cryptography==41.0.7

# Frontend dependencies (auto-installed)
npm install @auth0/angular-jwt@^5.2.0
```

## ⚙️ Configuration Files

### Development Mode
**Backend** (`.env.local`):
```bash
AUTH_ENABLED=false
JWT_SECRET_KEY=dev-secret-key
NG_APP_BASE_URL=http://localhost:5001
```

**Frontend** (`.env`):
```bash
NG_APP_AUTH_ENABLED=false
```

### Production Mode
**Backend** (`.env.production`):
```bash
AUTH_ENABLED=true
JWT_SECRET_KEY=your-production-secret-key
LDAP_HOST=SVEDC2000004PR.nbnco.local
LDAP_BASE_DN=DC=nbnco,DC=local
```

**Frontend** (`.env.production`):
```bash
NG_APP_AUTH_ENABLED=true
```

## 🧪 Testing Authentication

### Test Development Mode
```bash
python test_auth_system.py --mode dev
```

### Test Production Mode
```bash
python test_auth_system.py --mode live --username YOUR_USERNAME --password YOUR_PASSWORD
```

## 🔍 Troubleshooting

### Common Issues

1. **Backend won't start - Missing JWT module**
   ```bash
   cd backend
   source venv/bin/activate
   pip install PyJWT==2.8.0 ldap3==2.9.1
   ```

2. **Frontend won't start - Missing auth module**
   ```bash
   cd frontend
   npm install @auth0/angular-jwt@^5.2.0
   ```

3. **Environment variables not loading**
   - Ensure `.env.local` exists in backend directory
   - Check file permissions and encoding

4. **LDAP authentication fails**
   - Verify network connectivity to NBNCO domain
   - Check username format (use just username, not NBNCO\username)
   - Ensure you're on NBNCO network or VPN

### Debug Commands
```bash
# Check backend logs
tail -f backend.log

# Check frontend logs
tail -f frontend.log

# Test LDAP connectivity
nc -z SVEDC2000004PR.nbnco.local 636

# Validate JWT tokens
python -c "import jwt; print('JWT module working')"
```

## 🔐 Security Notes

### Development Mode
- ⚠️ **No authentication** - suitable for local development only
- 👤 User context shows `dev-user (DEV)`
- 🔓 All API endpoints accessible without tokens

### Production Mode
- 🔒 **Full LDAP authentication** required
- 👤 Real user context from Active Directory
- 🔐 JWT tokens protect all API endpoints
- ⏰ Configurable token expiration (default: 8 hours)

## 📱 Application URLs

| Service | Development | Production |
|---------|-------------|------------|
| **Frontend** | http://localhost:4200 | https://rspi.nbnco.com.au |
| **Backend** | http://localhost:5001 | https://rspi.nbnco.com.au/api |
| **Auth Health** | http://localhost:5001/api/v1/auth/health | - |

## 🛠️ Manual Setup (if scripts fail)

### Backend Manual Start
```bash
cd backend
source venv/bin/activate
pip install -r requirements.txt
python app/app.py
```

### Frontend Manual Start
```bash
cd frontend
npm install
npm start
```

## 📞 Support

### Authentication System Components
- **AuthAPI.py** - Authentication endpoints
- **auth_middleware.py** - JWT validation
- **ldap_auth.py** - LDAP integration
- **AuthService** - Frontend authentication
- **authGuard** - Route protection
- **authInterceptor** - HTTP token injection

### Key Files
- `backend/app/AuthAPI.py` - Authentication API
- `frontend/src/app/services/auth.service.ts` - Auth service
- `frontend/src/app/guards/auth.guard.ts` - Route guards
- `frontend/src/app/components/login/login.component.ts` - Login UI

### Environment Files
- `backend/.env.local` - Development backend config
- `backend/.env.production` - Production backend config
- `frontend/.env` - Development frontend config
- `frontend/.env.production` - Production frontend config

## 🔄 Switching Modes

### From Development to Production
1. Stop current application (Ctrl+C)
2. Run: `./start_app.sh --prod`
3. Login with NBNCO credentials

### From Production to Development
1. Stop current application (Ctrl+C)
2. Run: `./start_app.sh --dev`
3. Access application directly (no login)

---

**Need help?** Check the comprehensive documentation in `AUTHENTICATION_README.md` or the troubleshooting section in `augment-guidelines.md`.
