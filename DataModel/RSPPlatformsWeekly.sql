-- RSP Platforms Weekly Report Database Schema
-- This table stores weekly platform metrics and KPIs for RSP platform performance tracking

CREATE TABLE RSPPlatformsWeekly (
    id INT auto_increment NOT NULL,
    created DATETIME NULL,
    created_by varchar(45) NULL,
    modified <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    modified_by varchar(45) NULL,
    
    -- Week identification
    WeekEnding DATE NOT NULL,
    WeekNumber INT NULL,
    Year INT NULL,
    
    -- Platform/Service Type categorization
    PlatformServiceType varchar(100) NULL,
    
    -- Current Week Metrics
    CurrentWeekTarget DECIMAL(5,2) NULL,
    CurrentWeekActual DECIMAL(5,2) NULL,
    
    -- Last Week Metrics  
    LastWeekTarget DECIMAL(5,2) NULL,
    LastWeekActual DECIMAL(5,2) NULL,
    
    -- Last Month Metrics
    LastMonthTarget DECIMAL(5,2) NULL,
    LastMonthActual DECIMAL(5,2) NULL,
    
    -- Rolling Average
    RollingAverage DECIMAL(5,2) NULL,
    
    -- Change indicators
    ChangeFromLastWeek DECIMAL(5,2) NULL,
    
    -- Additional metrics for comprehensive tracking
    IncidentCount INT NULL DEFAULT 0,
    ServiceAvailability DECIMAL(5,2) NULL,
    APITransactionSuccess DECIMAL(5,2) NULL,
    PortalAvailability DECIMAL(5,2) NULL,
    
    -- Status and notes
    Status varchar(25) NULL DEFAULT 'Active',
    Notes varchar(500) NULL,
    
    CONSTRAINT RSPPlatformsWeekly_PK PRIMARY KEY (id),
    INDEX idx_week_ending (WeekEnding),
    INDEX idx_platform_type (PlatformServiceType),
    INDEX idx_week_platform (WeekEnding, PlatformServiceType)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_0900_ai_ci;

-- Insert sample data for demonstration (spanning multiple weeks for trending)
INSERT INTO RSPPlatformsWeekly (
    WeekEnding, WeekNumber, Year, PlatformServiceType,
    CurrentWeekTarget, CurrentWeekActual, LastWeekTarget, LastWeekActual,
    LastMonthTarget, LastMonthActual, RollingAverage, ChangeFromLastWeek,
    IncidentCount, ServiceAvailability, APITransactionSuccess, PortalAvailability,
    Status, Notes, created, created_by
) VALUES
-- Week 1: June 29, 2025
('2025-06-29', 26, 2025, 'Outages', 99.1, 99.2, 99.0, 98.8, 99.2, 99.1, 99.0, 0.4, 1, 99.2, 99.5, 99.0, 'Active', 'Minor outage resolved quickly', NOW(), 'system'),
('2025-06-29', 26, 2025, 'APIGW Availability', 100.00, 99.95, 100.00, 99.90, 100.00, 99.92, 99.93, 0.05, 0, 99.95, 99.98, 99.92, 'Active', 'All API gateways operational', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Interfaces', 100.00, 99.90, 100.00, 99.85, 100.00, 99.88, 99.88, 0.05, 0, 99.90, 99.92, 99.88, 'Active', 'Interface performance stable', NOW(), 'system'),
('2025-06-29', 26, 2025, 'API Transaction Success Rate', 100.00, 99.90, 100.00, 99.85, 99.94, 99.87, 99.89, 0.05, 2, 99.90, 99.90, 99.88, 'Active', 'API transactions performing well', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Assurance Transaction Success Rate', 99.95, 99.90, 99.95, 99.88, 99.94, 99.85, 99.88, 0.02, 0, 99.90, 99.90, 99.88, 'Active', 'Assurance services stable', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Service Portal Availability', 99.95, 99.90, 99.95, 99.88, 99.94, 99.85, 99.88, 0.02, 0, 99.90, 99.88, 99.90, 'Active', 'Service portal performing well', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Connections Transaction Success Rate', 99.95, 99.90, 99.95, 99.88, 99.94, 99.85, 99.88, 0.02, 0, 99.90, 99.90, 99.88, 'Active', 'Connection services optimal', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Key KPI Report Availability', 100.00, 99.90, 100.00, 99.85, 100.00, 99.88, 99.88, 0.05, 0, 99.90, 99.88, 99.92, 'Active', 'KPI reports generated successfully', NOW(), 'system'),
('2025-06-29', 26, 2025, 'Service Health Summary Data Quality', 98.47, 98.40, 98.50, 98.35, 98.70, 98.45, 98.42, 0.05, 1, 98.40, 98.38, 98.45, 'Active', 'Data quality metrics within range', NOW(), 'system'),

-- Week 2: July 6, 2025
('2025-07-06', 27, 2025, 'Outages', 99.1, 99.0, 99.1, 99.2, 99.0, 98.8, 99.0, -0.2, 2, 99.0, 99.2, 98.8, 'Active', 'Two minor incidents this week', NOW(), 'system'),
('2025-07-06', 27, 2025, 'APIGW Availability', 100.00, 99.92, 100.00, 99.95, 100.00, 99.90, 99.92, -0.03, 0, 99.92, 99.95, 99.90, 'Active', 'Slight decrease in availability', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Interfaces', 100.00, 99.88, 100.00, 99.90, 100.00, 99.85, 99.88, -0.02, 1, 99.88, 99.90, 99.86, 'Active', 'One interface issue resolved', NOW(), 'system'),
('2025-07-06', 27, 2025, 'API Transaction Success Rate', 100.00, 99.87, 100.00, 99.90, 100.00, 99.85, 99.87, -0.03, 1, 99.87, 99.87, 99.85, 'Active', 'Minor API performance dip', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Assurance Transaction Success Rate', 99.95, 99.88, 99.95, 99.90, 99.95, 99.88, 99.89, -0.02, 0, 99.88, 99.88, 99.86, 'Active', 'Assurance metrics stable', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Service Portal Availability', 99.95, 99.88, 99.95, 99.90, 99.95, 99.88, 99.89, -0.02, 0, 99.88, 99.86, 99.88, 'Active', 'Portal performance consistent', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Connections Transaction Success Rate', 99.95, 99.88, 99.95, 99.90, 99.95, 99.88, 99.89, -0.02, 0, 99.88, 99.88, 99.86, 'Active', 'Connection services stable', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Key KPI Report Availability', 100.00, 99.88, 100.00, 99.90, 100.00, 99.85, 99.88, -0.02, 0, 99.88, 99.86, 99.90, 'Active', 'KPI reporting consistent', NOW(), 'system'),
('2025-07-06', 27, 2025, 'Service Health Summary Data Quality', 98.47, 98.38, 98.47, 98.40, 98.50, 98.35, 98.38, -0.02, 0, 98.38, 98.36, 98.40, 'Active', 'Data quality maintained', NOW(), 'system'),

-- Week 3: July 13, 2025
('2025-07-13', 28, 2025, 'Outages', 99.1, 99.3, 99.1, 99.0, 99.1, 99.2, 99.2, 0.3, 0, 99.3, 99.5, 99.1, 'Active', 'Excellent week with no incidents', NOW(), 'system'),
('2025-07-13', 28, 2025, 'APIGW Availability', 100.00, 99.98, 100.00, 99.92, 100.00, 99.95, 99.95, 0.06, 0, 99.98, 100.00, 99.96, 'Active', 'Outstanding API gateway performance', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Interfaces', 100.00, 99.92, 100.00, 99.88, 100.00, 99.90, 99.90, 0.04, 0, 99.92, 99.94, 99.90, 'Active', 'Interface performance improved', NOW(), 'system'),
('2025-07-13', 28, 2025, 'API Transaction Success Rate', 100.00, 99.92, 100.00, 99.87, 100.00, 99.90, 99.90, 0.05, 0, 99.92, 99.92, 99.90, 'Active', 'API performance back to target', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Assurance Transaction Success Rate', 99.95, 99.92, 99.95, 99.88, 99.95, 99.90, 99.90, 0.04, 0, 99.92, 99.92, 99.90, 'Active', 'Assurance services performing well', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Service Portal Availability', 99.95, 99.92, 99.95, 99.88, 99.95, 99.90, 99.90, 0.04, 0, 99.92, 99.90, 99.92, 'Active', 'Portal availability excellent', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Connections Transaction Success Rate', 99.95, 99.92, 99.95, 99.88, 99.95, 99.90, 99.90, 0.04, 0, 99.92, 99.92, 99.90, 'Active', 'Connection performance strong', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Key KPI Report Availability', 100.00, 99.92, 100.00, 99.88, 100.00, 99.90, 99.90, 0.04, 0, 99.92, 99.90, 99.94, 'Active', 'KPI reports generated reliably', NOW(), 'system'),
('2025-07-13', 28, 2025, 'Service Health Summary Data Quality', 98.47, 98.45, 98.47, 98.38, 98.47, 98.40, 98.41, 0.07, 0, 98.45, 98.43, 98.47, 'Active', 'Data quality improvement noted', NOW(), 'system'),

-- Week 4: July 20, 2025
('2025-07-20', 29, 2025, 'Outages', 99.1, 98.9, 99.1, 99.3, 99.1, 99.0, 99.1, -0.4, 3, 98.9, 99.1, 98.7, 'Active', 'Three incidents impacted availability', NOW(), 'system'),
('2025-07-20', 29, 2025, 'APIGW Availability', 100.00, 99.85, 100.00, 99.98, 100.00, 99.92, 99.92, -0.13, 1, 99.85, 99.88, 99.82, 'Active', 'API gateway maintenance window', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Interfaces', 100.00, 99.82, 100.00, 99.92, 100.00, 99.88, 99.87, -0.10, 2, 99.82, 99.85, 99.80, 'Active', 'Interface performance affected by incidents', NOW(), 'system'),
('2025-07-20', 29, 2025, 'API Transaction Success Rate', 100.00, 99.80, 100.00, 99.92, 100.00, 99.87, 99.85, -0.12, 2, 99.80, 99.80, 99.78, 'Active', 'API performance impacted by outages', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Assurance Transaction Success Rate', 99.95, 99.82, 99.95, 99.92, 99.95, 99.88, 99.86, -0.10, 1, 99.82, 99.82, 99.80, 'Active', 'Assurance services affected', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Service Portal Availability', 99.95, 99.82, 99.95, 99.92, 99.95, 99.88, 99.86, -0.10, 1, 99.82, 99.80, 99.82, 'Active', 'Portal availability impacted', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Connections Transaction Success Rate', 99.95, 99.82, 99.95, 99.92, 99.95, 99.88, 99.86, -0.10, 1, 99.82, 99.82, 99.80, 'Active', 'Connection services affected by incidents', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Key KPI Report Availability', 100.00, 99.82, 100.00, 99.92, 100.00, 99.88, 99.87, -0.10, 1, 99.82, 99.80, 99.84, 'Active', 'KPI report generation delayed', NOW(), 'system'),
('2025-07-20', 29, 2025, 'Service Health Summary Data Quality', 98.47, 98.30, 98.47, 98.45, 98.47, 98.38, 98.36, -0.15, 1, 98.30, 98.28, 98.32, 'Active', 'Data quality affected by system issues', NOW(), 'system'),

-- Week 5: July 27, 2025
('2025-07-27', 30, 2025, 'Outages', 99.1, 99.4, 99.1, 98.9, 99.1, 99.3, 99.2, 0.5, 0, 99.4, 99.6, 99.2, 'Active', 'Recovery week with excellent performance', NOW(), 'system'),
('2025-07-27', 30, 2025, 'APIGW Availability', 100.00, 99.97, 100.00, 99.85, 100.00, 99.98, 99.94, 0.12, 0, 99.97, 99.99, 99.95, 'Active', 'API gateway performance restored', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Interfaces', 100.00, 99.94, 100.00, 99.82, 100.00, 99.92, 99.89, 0.12, 0, 99.94, 99.96, 99.92, 'Active', 'Interface performance fully recovered', NOW(), 'system'),
('2025-07-27', 30, 2025, 'API Transaction Success Rate', 100.00, 99.94, 100.00, 99.80, 100.00, 99.92, 99.88, 0.14, 0, 99.94, 99.94, 99.92, 'Active', 'API transactions back to optimal', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Assurance Transaction Success Rate', 99.95, 99.94, 99.95, 99.82, 99.95, 99.92, 99.88, 0.12, 0, 99.94, 99.94, 99.92, 'Active', 'Assurance services fully operational', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Service Portal Availability', 99.95, 99.94, 99.95, 99.82, 99.95, 99.92, 99.88, 0.12, 0, 99.94, 99.92, 99.94, 'Active', 'Portal performance excellent', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Connections Transaction Success Rate', 99.95, 99.94, 99.95, 99.82, 99.95, 99.92, 99.88, 0.12, 0, 99.94, 99.94, 99.92, 'Active', 'Connection services performing optimally', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Key KPI Report Availability', 100.00, 99.94, 100.00, 99.82, 100.00, 99.92, 99.89, 0.12, 0, 99.94, 99.92, 99.96, 'Active', 'KPI reporting back to normal', NOW(), 'system'),
('2025-07-27', 30, 2025, 'Service Health Summary Data Quality', 98.47, 98.50, 98.47, 98.30, 98.47, 98.45, 98.41, 0.20, 0, 98.50, 98.48, 98.52, 'Active', 'Data quality metrics improved', NOW(), 'system'),

-- Week 6: August 3, 2025 (Current week)
('2025-08-03', 31, 2025, 'Outages', 99.1, 99.2, 99.1, 99.4, 99.1, 98.9, 99.2, -0.2, 1, 99.2, 99.4, 99.0, 'Active', 'One minor incident early in week', NOW(), 'system'),
('2025-08-03', 31, 2025, 'APIGW Availability', 100.00, 99.95, 100.00, 99.97, 100.00, 99.85, 99.94, -0.02, 0, 99.95, 99.97, 99.93, 'Active', 'API gateway stable performance', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Interfaces', 100.00, 99.90, 100.00, 99.94, 100.00, 99.82, 99.90, -0.04, 0, 99.90, 99.92, 99.88, 'Active', 'Interface performance consistent', NOW(), 'system'),
('2025-08-03', 31, 2025, 'API Transaction Success Rate', 100.00, 99.90, 100.00, 99.94, 100.00, 99.80, 99.89, -0.04, 0, 99.90, 99.90, 99.88, 'Active', 'API performance within targets', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Assurance Transaction Success Rate', 99.95, 99.90, 99.95, 99.94, 99.95, 99.82, 99.89, -0.04, 0, 99.90, 99.90, 99.88, 'Active', 'Assurance metrics stable', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Service Portal Availability', 99.95, 99.90, 99.95, 99.94, 99.95, 99.82, 99.89, -0.04, 0, 99.90, 99.88, 99.90, 'Active', 'Portal availability consistent', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Connections Transaction Success Rate', 99.95, 99.90, 99.95, 99.94, 99.95, 99.82, 99.89, -0.04, 0, 99.90, 99.90, 99.88, 'Active', 'Connection services stable', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Key KPI Report Availability', 100.00, 99.90, 100.00, 99.94, 100.00, 99.82, 99.90, -0.04, 0, 99.90, 99.88, 99.92, 'Active', 'KPI reporting consistent', NOW(), 'system'),
('2025-08-03', 31, 2025, 'Service Health Summary Data Quality', 98.47, 98.40, 98.47, 98.50, 98.47, 98.30, 98.42, -0.10, 0, 98.40, 98.38, 98.42, 'Active', 'Data quality within acceptable range', NOW(), 'system');
